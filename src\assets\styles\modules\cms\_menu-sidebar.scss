/* Match input height with button */
.search-input :deep(.el-input__wrapper) {
  height: 38px;
  display: flex;
  align-items: center;
}

.search-input :deep(.el-input__inner) {
  height: 100%;
}

/* Fixed height container for menu list */
.menu-list-container {
  height: 590px; /* Fixed height */
  flex-shrink: 0; /* Don't shrink */
}

/* Scrollable area inside container */
.menu-list-scroll {
  height: 100%;
  overflow-y: auto;
}

.menu-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.menu-list-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.menu-list-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.menu-list-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark .menu-list-scroll::-webkit-scrollbar-track {
  background: #374151;
}

.dark .menu-list-scroll::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .menu-list-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .menu-sidebar {
    height: auto !important;
  }

  .menu-list-container {
    height: 500px !important;
  }
}