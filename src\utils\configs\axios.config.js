import axios from 'axios'
import { getDataByKey } from '@/utils/helpers/localStorage.helper.js'
import router from '@/router/index.js'
import { useAuthStore } from '@/state/index.js'

const apiAxios = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: {
    Accept: 'application/json',
  },
  withCredentials: true, // Cho phép gửi cookies nếu cần
})

apiAxios.interceptors.request.use(
  function (config) {
    const token = getDataByKey('access_token')

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  function (error) {
    return Promise.reject(error)
  },
)

apiAxios.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized - redirect to login page
    if (error.response?.status === 401) {
      console.error('[API ERROR] Unauthorized:', error.response.data)
      useAuthStore().logout()
      router.push({ name: 'login' })
      return Promise.reject({
        message: '<PERSON><PERSON><PERSON> đăng nhập đã hết hạn, vui lòng đăng nhập lại',
        status: 401,
      })
    }

    // Handle 403 Forbidden - redirect to forbidden page
    if (error.response?.status === 403) {
      console.error('[API ERROR] Access denied:', error.response.data)
      router.push({ name: 'forbidden' })
      return Promise.reject({
        message: 'Bạn không có quyền thực hiện hành động này',
        status: 403,
      })
    }

    // Handle 404 Not Found - redirect to 404 page
    if (error.response?.status === 404) {
      console.error('[API ERROR] Not found:', error.response.data)
      router.push({ name: 'not-found' })
      return Promise.reject({
        message: 'Tài nguyên không tồn tại',
        status: 404,
      })
    }

    // Handle 422 Validation Error
    if (error.response?.status === 422) {
      const validationErrors = error.response.data.errors || {}
      return Promise.reject({
        message: 'Dữ liệu không hợp lệ',
        errors: validationErrors,
        status: 422,
      })
    }

    // Handle 500 Server Error
    if (error.response?.status >= 500) {
      return Promise.reject({
        message: 'Lỗi máy chủ, vui lòng thử lại sau',
        status: error.response.status,
      })
    }

    // Handle network errors
    if (!error.response) {
      return Promise.reject({
        message: 'Không thể kết nối đến máy chủ',
        error: error.message,
        status: 0,
      })
    }

    return Promise.reject(error.response.data || error)
  },
)

export default apiAxios
