.menu-item-tree {
  width: 100%;
}

.menu-item-node {
  margin-bottom: 2px;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #ffffff;
}

.menu-item-content:hover {
  background-color: #f8fafc;
  border-color: #e2e8f0;
}

.menu-item-content.selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.expand-button {
  margin-right: 4px;
  width: 20px;
  flex-shrink: 0;
}

.expand-button :deep(.inline-flex) {
  min-width: 20px !important;
  padding: 2px !important;
  min-height: 20px !important;
}

.expand-placeholder {
  width: 20px;
  margin-right: 4px;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  min-width: 0;
  margin-left: 4px;
}

.item-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 2px;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.item-slug {
  font-family: monospace;
  background-color: #f3f4f6;
  padding: 2px 6px;
  border-radius: 3px;
}

.item-type {
  background-color: #dbeafe;
  color: #1e40af;
  padding: 2px 6px;
  border-radius: 3px;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 1; /* Always visible */
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.delete-button {
  color: #dc2626 !important;
  background-color: #fef2f2 !important;
  border-radius: 6px !important;
  padding: 6px !important;
  min-height: 32px !important;
  width: 32px !important;
}

.delete-button:hover {
  background-color: #fee2e2 !important;
  color: #b91c1c !important;
}

.children {
  margin-left: 24px;
  border-left: 2px solid #e5e7eb;
  margin-top: 4px;
  padding-left: 12px;
}

/* Animations */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

/* Dark mode */
.dark .menu-item-content {
  background-color: rgba(255, 255, 255, 0.03);
}

.dark .menu-item-content:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: #374151;
}

.dark .menu-item-content.selected {
  background-color: rgba(59, 130, 246, 0.15);
  border-color: #60a5fa;
}

.dark .item-title {
  color: #f9fafb;
}

.dark .item-meta {
  color: #9ca3af;
}

.dark .item-slug {
  background-color: #374151;
  color: #d1d5db;
}

.dark .children {
  border-left-color: #374151;
}

.dark .delete-button {
  background-color: rgba(220, 38, 38, 0.1) !important;
  color: #f87171 !important;
}

.dark .delete-button:hover {
  background-color: rgba(220, 38, 38, 0.2) !important;
  color: #ef4444 !important;
}
