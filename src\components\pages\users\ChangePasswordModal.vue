<template>
  <Modal v-model="isVisible" title="Thay đổi mật khẩu người dùng" width="500px" @close="handleClose">
    <template #body>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- User Info -->
          <div class="mb-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <div class="flex items-center">
              <el-avatar :src="user?.avatar" :alt="user?.name" class="mr-3" :size="40">
                {{ user?.name?.charAt(0)?.toUpperCase() }}
              </el-avatar>
              <div>
                <div class="font-medium text-gray-900 dark:text-white">{{ user?.name }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ user?.email }}</div>
              </div>
            </div>
          </div>

          <!-- Form Fields -->
          <div class="space-y-6">
            <!-- New Password Field -->
            <FormField label="Mật khẩu mới" :icon="Lock" required :error="errors.password">
              <PasswordInput
                v-model="form.password"
                placeholder="Nhập mật khẩu mới"
                :has-error="!!errors.password"
                :disabled="saving"
              />

              <!-- Password Strength -->
              <PasswordStrength :password="form.password" />
            </FormField>

            <!-- Password Confirmation Field -->
            <FormField label="Xác nhận mật khẩu" :icon="Check" required :error="errors.password_confirmation">
              <PasswordInput
                v-model="form.password_confirmation"
                placeholder="Nhập lại mật khẩu mới"
                :has-error="!!errors.password_confirmation"
                :disabled="saving"
              />
            </FormField>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-4 border-t border-gray-200 pt-6 dark:border-gray-700">
            <el-button type="default" @click="handleClose" :disabled="saving" class="flex-1" size="large">
              <el-icon class="mr-1"><Close /></el-icon>
              Hủy
            </el-button>

            <el-button
              type="primary"
              :loading="saving"
              :disabled="!canSubmit"
              class="flex-1"
              size="large"
              @click="handleSubmit"
            >
              <el-icon v-if="!saving" class="mr-1"><Check /></el-icon>
              {{ saving ? 'Đang lưu...' : 'Cập nhật mật khẩu' }}
            </el-button>
          </div>
        </form>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import { Close, Lock, Check } from '@element-plus/icons-vue'

// Components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import PasswordInput from '@/components/common/PasswordInput.vue'
import PasswordStrength from '@/components/common/PasswordStrength.vue'

// Composables
import { useUsers } from '@/composables/modules/users/useUsers.js'
import { VALIDATION } from '@/utils/configs/constant.config.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  user: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Composables
const { changeUserPassword, saving, errors, clearErrors } = useUsers()

// Computed
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const canSubmit = computed(() => {
  return (
    form.password &&
    form.password_confirmation &&
    form.password === form.password_confirmation &&
    form.password.length >= VALIDATION.PASSWORD_MIN_LENGTH
  )
})

// Form data
const form = reactive({
  password: '',
  password_confirmation: '',
})

// Methods
const resetForm = () => {
  Object.assign(form, {
    password: '',
    password_confirmation: '',
  })
}

const handleSubmit = async () => {
  try {
    clearErrors()

    if (!canSubmit.value) {
      ElMessage.warning('Vui lòng điền đầy đủ thông tin và đảm bảo mật khẩu khớp nhau')
      return
    }

    const passwordData = {
      password: form.password,
      password_confirmation: form.password_confirmation,
    }

    await changeUserPassword(props.user.id, passwordData)
    emit('success')
    resetForm()
  } catch (error) {
    console.error('Password change error:', error)
  }
}

const handleClose = () => {
  resetForm()
  clearErrors()
  emit('update:visible', false)
}

// Watchers
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm()
    }
  },
)
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/users/_change-password-modal.scss' as *;
</style>
