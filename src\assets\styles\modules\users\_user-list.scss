// User List Component Styles
// Extracted from UserList.vue <style> section

.user-table {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-bg-color-page);
  --el-table-row-hover-bg-color: var(--el-fill-color-light);
}

/* Table header styling */
:deep(.el-table th.el-table__cell) {
  background-color: var(--el-table-header-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-table-border-color) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 12px !important;
}

/* Table cell styling */
:deep(.el-table td.el-table__cell) {
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-table-border-color) !important;
  padding: 16px 12px !important;
}

/* Row hover effects */
:deep(.el-table tr:hover > td.el-table__cell) {
  background-color: var(--el-table-row-hover-bg-color) !important;
  transition: background-color 0.2s ease;
}

/* Alternating row colors */
:deep(.el-table .even-row td.el-table__cell) {
  background-color: var(--el-bg-color) !important;
}

:deep(.el-table .odd-row td.el-table__cell) {
  background-color: var(--el-fill-color-lighter) !important;
}

/* Input styling */
:deep(.el-input__wrapper) {
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color);
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: var(--el-border-color-hover);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 3px var(--el-color-primary-light-9);
}

:deep(.el-input__inner) {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

:deep(.el-input__inner::placeholder) {
  color: var(--el-text-color-placeholder);
}

/* Enhanced Button styling */
:deep(.el-button) {
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  padding: 10px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
}

:deep(.el-button--default) {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  color: #475569;
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-button--default:hover) {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #334155;
  border-color: #94a3b8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #1d4ed8;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border-color: #1e40af;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Reset button styling using Tailwind */
.reset-btn {
  @apply border-2 border-gray-200 bg-white text-blue-500;
  @apply shadow-sm shadow-black/5;
  @apply relative overflow-hidden font-semibold;
  @apply transition-all duration-300 ease-out;
}

.reset-btn:hover {
  @apply border-blue-500 bg-gray-50 text-blue-600;
  @apply -translate-y-0.5;
  @apply shadow-lg shadow-blue-500/15;
}

.reset-btn:focus {
  @apply outline-none;
  @apply ring-2 ring-blue-500/30;
}

.dark .reset-btn {
  @apply border-gray-600 bg-gray-700 text-blue-400;
  @apply shadow-sm shadow-black/20;
}

.dark .reset-btn:hover {
  @apply border-blue-400 bg-gray-600 text-blue-300;
  @apply shadow-lg shadow-blue-400/20;
}

.dark .reset-btn:focus {
  @apply ring-2 ring-blue-400/30;
}

/* Create User button styling using Tailwind */
.create-user-btn {
  @apply h-[40px];
  @apply bg-gradient-to-br from-blue-500 to-blue-600;
  @apply border-0 text-white;
  @apply shadow-lg shadow-blue-500/30;
  @apply relative overflow-hidden;
  @apply transition-all duration-300 ease-out;
}

.create-user-btn:hover {
  @apply bg-gradient-to-br from-blue-600 to-blue-700;
  @apply -translate-y-0.5 scale-105;
  @apply shadow-xl shadow-blue-500/40;
}

.dark .create-user-btn {
  @apply bg-gradient-to-br from-blue-600 to-blue-700;
  @apply shadow-lg shadow-blue-500/40;
}

.dark .create-user-btn:hover {
  @apply bg-gradient-to-br from-blue-700 to-blue-800;
  @apply shadow-xl shadow-blue-500/50;
}

/* Action buttons using Tailwind classes - No Element Plus overrides */
.action-btn {
  @apply rounded-lg px-3 py-1.5 text-xs font-medium;
  @apply transition-all duration-300 ease-out;
  @apply relative overflow-hidden;
  @apply h-8 min-w-[70px];
  @apply inline-flex items-center justify-center;
  @apply gap-1;
}

.edit-btn {
  @apply bg-gradient-to-br from-indigo-500 to-indigo-600;
  @apply border-0 text-white;
  @apply shadow-sm shadow-indigo-500/20;
}

.edit-btn:hover {
  @apply bg-gradient-to-br from-indigo-600 to-indigo-700;
  @apply -translate-y-0.5;
  @apply shadow-md shadow-indigo-500/30;
}

.password-btn {
  @apply bg-gradient-to-br from-amber-500 to-amber-600;
  @apply border-0 text-white;
  @apply shadow-sm shadow-amber-500/20;
}

.password-btn:hover {
  @apply bg-gradient-to-br from-amber-600 to-amber-700;
  @apply -translate-y-0.5;
  @apply shadow-md shadow-amber-500/30;
}

.delete-btn {
  @apply bg-gradient-to-br from-red-500 to-red-600;
  @apply border-0 text-white;
  @apply shadow-sm shadow-red-500/20;
}

.delete-btn:hover {
  @apply bg-gradient-to-br from-red-600 to-red-700;
  @apply -translate-y-0.5;
  @apply shadow-md shadow-red-500/30;
}

/* Dark mode button enhancements */
.dark :deep(.el-button--default) {
  background: linear-gradient(135deg, #374151, #4b5563);
  color: #e5e7eb;
  border-color: #6b7280;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark :deep(.el-button--default:hover) {
  background: linear-gradient(135deg, #4b5563, #6b7280);
  color: #f9fafb;
  border-color: #9ca3af;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Dark mode for action buttons */
.dark .edit-btn {
  @apply bg-gradient-to-br from-indigo-600 to-indigo-700;
  @apply shadow-sm shadow-indigo-500/30;
}

.dark .edit-btn:hover {
  @apply bg-gradient-to-br from-indigo-700 to-indigo-800;
  @apply shadow-md shadow-indigo-500/40;
}

.dark .password-btn {
  @apply bg-gradient-to-br from-amber-600 to-amber-700;
  @apply shadow-sm shadow-amber-500/30;
}

.dark .password-btn:hover {
  @apply bg-gradient-to-br from-amber-700 to-amber-800;
  @apply shadow-md shadow-amber-500/40;
}

.dark .delete-btn {
  @apply bg-gradient-to-br from-red-600 to-red-700;
  @apply shadow-sm shadow-red-500/30;
}

.dark .delete-btn:hover {
  @apply bg-gradient-to-br from-red-700 to-red-800;
  @apply shadow-md shadow-red-500/40;
}

/* Focus states for accessibility using Tailwind */
.action-btn:focus,
.create-user-btn:focus,
.reset-btn:focus {
  @apply outline-none;
  @apply ring-2 ring-blue-500/30;
}

.dark .action-btn:focus,
.dark .create-user-btn:focus,
.dark .reset-btn:focus {
  @apply ring-2 ring-blue-400/30;
}

/* Loading states */
.action-btn.is-loading,
.create-user-btn.is-loading,
.reset-btn.is-loading {
  @apply cursor-not-allowed opacity-70;
  @apply transform-none;
}

/* Active states */
.action-btn:active,
.create-user-btn:active,
.reset-btn:active {
  @apply translate-y-0 scale-95;
  @apply transition-transform duration-100;
}

/* Disabled states */
.action-btn:disabled,
.create-user-btn:disabled,
.reset-btn:disabled {
  @apply cursor-not-allowed opacity-50;
  @apply transform-none;
}

/* Pagination styling */
.custom-pagination {
  --el-pagination-bg-color: var(--el-bg-color);
  --el-pagination-text-color: var(--el-text-color-primary);
  --el-pagination-border-color: var(--el-border-color);
}

:deep(.el-pagination .el-pager li) {
  background-color: var(--el-pagination-bg-color);
  color: var(--el-pagination-text-color);
  border: 1px solid var(--el-pagination-border-color);
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.2s ease;
}

:deep(.el-pagination .el-pager li:hover) {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-hover);
  transform: translateY(-1px);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
  color: white;
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background-color: var(--el-pagination-bg-color);
  color: var(--el-pagination-text-color);
  border: 1px solid var(--el-pagination-border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-hover);
  transform: translateY(-1px);
}

/* Tag styling */
.status-tag,
.role-tag {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  padding: 4px 8px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.status-tag:hover,
.role-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-tag--info) {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
}

:deep(.el-tag--success) {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

:deep(.el-tag--warning) {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

:deep(.el-tag--danger) {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

/* Dark mode specific adjustments */
.dark :deep(.el-table) {
  --el-table-border-color: #374151;
  --el-table-header-bg-color: #1e2636;
  --el-table-row-hover-bg-color: #374151;
}

.dark :deep(.el-table .odd-row td.el-table__cell) {
  background-color: #1e2636 !important;
}

.dark :deep(.el-table .even-row td.el-table__cell) {
  background-color: #1e2636 !important;
}

.dark :deep(.el-table tr:hover > td.el-table__cell) {
  background-color: #374151 !important;
}

/* Responsive adjustments using Tailwind */
@media (max-width: 768px) {
  :deep(.el-table th.el-table__cell),
  :deep(.el-table td.el-table__cell) {
    @apply p-2 text-sm;
  }

  .action-btn {
    @apply px-2 py-1 text-xs;
    @apply h-7 min-w-[60px];
  }

  .action-btn .w-4 {
    @apply h-3 w-3;
  }

  .create-user-btn,
  .reset-btn {
    @apply px-4 py-2 text-sm;
  }

  :deep(.el-button) {
    @apply px-4 py-2 text-sm;
  }
}

@media (max-width: 480px) {
  .action-btn {
    @apply px-1.5 py-0.5 text-xs;
    @apply h-6 min-w-[50px];
  }

  .action-btn .w-4 {
    @apply h-2.5 w-2.5;
  }

  .create-user-btn,
  .reset-btn {
    @apply px-3 py-1.5 text-xs;
  }

  :deep(.el-button) {
    @apply px-3 py-1.5 text-xs;
  }
}
