import { ref, nextTick } from 'vue'

/**
 * Composable for real-time form validation
 * Provides utilities for clearing validation errors and real-time field validation
 */
export function useFormValidation() {
  const formRef = ref()

  /**
   * Clear validation error for a specific field
   * @param {string} field - Field name to clear validation for
   */
  const clearFieldValidation = (field) => {
    if (formRef.value) {
      formRef.value.clearValidate(field)
    }
  }

  /**
   * Clear validation for multiple fields
   * @param {string[]} fields - Array of field names to clear validation for
   */
  const clearMultipleFieldsValidation = (fields) => {
    if (formRef.value && Array.isArray(fields)) {
      fields.forEach(field => {
        formRef.value.clearValidate(field)
      })
    }
  }

  /**
   * Clear all form validation
   */
  const clearAllValidation = () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  /**
   * Validate a specific field
   * @param {string} field - Field name to validate
   * @returns {Promise} - Validation result
   */
  const validateField = async (field) => {
    if (formRef.value) {
      try {
        await formRef.value.validateField(field)
        return true
      } catch (error) {
        return false
      }
    }
    return false
  }

  /**
   * Validate entire form
   * @returns {Promise<boolean>} - Form validation result
   */
  const validateForm = async () => {
    if (formRef.value) {
      try {
        await formRef.value.validate()
        return true
      } catch (error) {
        return false
      }
    }
    return false
  }

  /**
   * Smart field validation - clears error if field has valid content, validates if empty
   * @param {string} field - Field name
   * @param {any} value - Field value
   * @param {Object} options - Validation options
   * @param {boolean} options.required - Whether field is required
   * @param {number} options.minLength - Minimum length for validation
   * @param {RegExp} options.pattern - Pattern for validation
   */
  const smartValidateField = (field, value, options = {}) => {
    if (!formRef.value) return

    const { required = true, minLength = 0, pattern } = options
    const trimmedValue = String(value || '').trim()

    // If field has content and meets basic requirements, clear validation
    if (trimmedValue && 
        trimmedValue.length >= minLength && 
        (!pattern || pattern.test(trimmedValue))) {
      clearFieldValidation(field)
    } else if (required && !trimmedValue) {
      // If required field is empty, validate to show error
      validateField(field)
    }
  }

  /**
   * Handle input change with smart validation
   * @param {string} field - Field name
   * @param {any} value - New field value
   * @param {Object} options - Validation options
   */
  const handleInputChange = (field, value, options = {}) => {
    // Use nextTick to ensure DOM is updated
    nextTick(() => {
      smartValidateField(field, value, options)
    })
  }

  /**
   * Handle blur event with validation
   * @param {string} field - Field name
   */
  const handleFieldBlur = (field) => {
    validateField(field)
  }

  /**
   * Create input handlers for a field
   * @param {string} field - Field name
   * @param {Object} options - Validation options
   * @returns {Object} - Object with input and blur handlers
   */
  const createFieldHandlers = (field, options = {}) => {
    return {
      onInput: (value) => handleInputChange(field, value, options),
      onBlur: () => handleFieldBlur(field)
    }
  }

  /**
   * Auto-generate field value and clear validation
   * Useful for auto-generated fields like slugs
   * @param {string} sourceField - Source field name
   * @param {string} targetField - Target field name
   * @param {any} sourceValue - Source field value
   * @param {Function} generator - Function to generate target value
   * @param {Object} options - Options
   * @param {boolean} options.clearTargetValidation - Whether to clear target field validation
   */
  const autoGenerateField = (sourceField, targetField, sourceValue, generator, options = {}) => {
    const { clearTargetValidation = true } = options
    
    if (typeof generator === 'function') {
      const generatedValue = generator(sourceValue)
      
      if (clearTargetValidation && generatedValue && generatedValue.trim()) {
        clearFieldValidation(targetField)
      }
      
      return generatedValue
    }
    
    return sourceValue
  }

  return {
    formRef,
    clearFieldValidation,
    clearMultipleFieldsValidation,
    clearAllValidation,
    validateField,
    validateForm,
    smartValidateField,
    handleInputChange,
    handleFieldBlur,
    createFieldHandlers,
    autoGenerateField
  }
}

/**
 * Common validation patterns
 */
export const validationPatterns = {
  slug: /^[a-z0-9-]+$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[0-9\-+\s()]+$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithDash: /^[a-zA-Z0-9-]+$/,
  numbersOnly: /^[0-9]+$/
}

/**
 * Common validation options presets
 */
export const validationPresets = {
  required: { required: true },
  slug: { required: true, minLength: 2, pattern: validationPatterns.slug },
  email: { required: true, pattern: validationPatterns.email },
  name: { required: true, minLength: 2 },
  description: { required: false, minLength: 0 },
  url: { required: false, pattern: validationPatterns.url }
}
