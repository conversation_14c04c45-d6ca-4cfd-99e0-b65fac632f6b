<!-- 
  AvatarUpload Component
  Common component cho upload avatar - tu<PERSON> thủ kiến trúc dự án
-->
<template>
  <div class="avatar-upload relative z-10 mx-auto w-full max-w-md" :data-size="size">
    <!-- Avatar Wrapper -->
    <div class="avatar-wrapper relative inline-block h-auto w-auto p-1">
      <!-- Custom Upload Container -->
      <div
        class="avatar-container relative z-1 m-1 inline-block h-auto w-auto cursor-pointer transition-all duration-500 ease-out"
        :class="{
          'cursor-not-allowed opacity-50': disabled,
          'has-avatar': avatarSrc,
          'is-loading': loading,
          'is-dragover': isDragOver,
        }"
        @click="handleClick"
        @dragenter="handleDragEnter"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
        @keydown.enter="handleClick"
        @keydown.space="handleClick"
        tabindex="0"
        role="button"
        :aria-label="avatarSrc ? 'Thay đổi <PERSON>nh avatar' : 'Tải lên ảnh avatar'"
      >
        <!-- Avatar Square -->
        <div
          class="avatar-square relative z-2 block h-28 w-28 overflow-hidden rounded-3xl border-4 border-white/90 bg-gradient-to-br from-violet-500 via-purple-500 to-fuchsia-500 shadow-2xl backdrop-blur-sm transition-all duration-500 ease-out"
        >
          <!-- Loading Spinner -->
          <div
            v-if="loading"
            class="loading-spinner absolute inset-0 z-20 flex items-center justify-center rounded-3xl bg-black/20 backdrop-blur-sm"
          >
            <div class="spinner h-8 w-8 animate-spin rounded-full border-4 border-white/30 border-t-white"></div>
          </div>

          <img
            v-if="avatarSrc"
            :src="avatarSrc"
            :alt="name"
            class="avatar-image ease h-full w-full rounded-2xl object-cover transition-all duration-500"
            @error="onImageError"
          />
          <div
            v-else
            class="avatar-placeholder flex h-full w-full flex-col items-center justify-center rounded-2xl bg-gradient-to-br from-violet-500/90 via-purple-500/90 to-fuchsia-500/90 text-white backdrop-blur-sm"
          >
            <el-icon class="placeholder-icon ease mb-2 text-6xl opacity-90 drop-shadow-lg transition-all duration-300">
              <UserFilled />
            </el-icon>
            <span
              v-if="name"
              class="initials ease text-4xl font-bold tracking-wider transition-all duration-300 text-shadow-lg"
            >
              {{ getInitials(name) }}
            </span>
          </div>
        </div>

        <!-- Upload Overlay -->
        <div
          class="upload-overlay pointer-events-none absolute inset-0 z-10 flex items-center justify-center rounded-2xl bg-gradient-to-br from-black/80 via-black/70 to-black/60 opacity-0 backdrop-blur-md transition-all duration-500 ease-out"
        >
          <div
            class="overlay-content ease flex translate-y-4 transform flex-col items-center gap-3 transition-transform duration-500"
          >
            <div class="upload-icon-wrapper rounded-full border border-white/30 bg-white/20 p-3 backdrop-blur-sm">
              <el-icon class="upload-icon ease text-5xl text-white drop-shadow-lg transition-all duration-500">
                <Camera />
              </el-icon>
            </div>
            <span
              class="upload-text text-center text-sm leading-tight font-semibold tracking-wider text-white uppercase text-shadow-lg"
            >
              {{ avatarSrc ? 'Thay đổi' : 'Tải lên' }}
            </span>
          </div>
        </div>

        <!-- Drag Overlay -->
        <div
          v-if="isDragOver"
          class="drag-overlay absolute inset-0 z-15 flex items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500/80 via-purple-500/80 to-pink-500/80 backdrop-blur-md"
        >
          <div class="drag-content flex flex-col items-center gap-3">
            <div class="drag-icon-wrapper rounded-full border-2 border-white/50 bg-white/30 p-4 backdrop-blur-sm">
              <el-icon class="drag-icon text-6xl text-white drop-shadow-lg">
                <Upload />
              </el-icon>
            </div>
            <span
              class="drag-text text-center text-lg leading-tight font-bold tracking-wider text-white uppercase text-shadow-lg"
            >
              Thả ảnh vào đây
            </span>
          </div>
        </div>
      </div>

      <!-- Delete Button -->
      <button
        v-if="avatarSrc && !isDefaultAvatar"
        @click.stop="handleRemove"
        :disabled="disabled"
        class="delete-btn absolute -top-2 -right-2 z-30 flex h-8 w-8 scale-100 transform cursor-pointer items-center justify-center rounded-full border-3 border-white/90 bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white shadow-xl backdrop-blur-sm transition-all duration-300 ease-out outline-none hover:scale-110 hover:shadow-2xl focus:shadow-xl active:scale-95 disabled:transform-none disabled:cursor-not-allowed disabled:bg-gray-400 disabled:opacity-50"
        title="Xóa ảnh"
        aria-label="Xóa ảnh avatar"
      >
        <el-icon class="delete-icon ease h-4 w-4 text-current transition-transform duration-200">
          <Delete />
        </el-icon>
      </button>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :disabled="disabled"
      @change="handleFileChange"
      class="hidden-file-input pointer-events-none absolute h-0 w-0 opacity-0"
      :multiple="false"
    />

    <!-- Error Message -->
    <div v-if="error" class="error-message mt-2 w-full text-left">
      <span class="error-text text-xs text-red-500 dark:text-red-400">
        {{ error }}
      </span>
    </div>

    <!-- File Info -->
    <div
      v-if="fileInfo && showFileInfo"
      class="file-info mt-6 rounded-2xl border border-gray-200/80 bg-gradient-to-r from-gray-50/80 via-white/80 to-gray-50/80 p-4 shadow-lg backdrop-blur-sm dark:border-gray-600/80 dark:from-gray-800/80 dark:via-gray-700/80 dark:to-gray-800/80"
    >
      <div class="flex items-center gap-3">
        <el-icon class="file-icon text-xl text-blue-500">
          <Document />
        </el-icon>
        <div class="file-details min-w-0 flex-1">
          <p
            class="file-name overflow-hidden text-sm font-semibold text-ellipsis whitespace-nowrap text-gray-700 dark:text-gray-200"
          >
            {{ fileInfo.name }}
          </p>
          <p class="file-size mt-1 text-xs text-gray-500 dark:text-gray-400">
            {{ formatFileSize(fileInfo.size) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Upload Instructions -->
    <div v-if="!avatarSrc && !error" class="upload-instructions mt-2 text-left">
      <p class="text-xs text-gray-500 dark:text-gray-400">Click để chọn ảnh hoặc kéo thả</p>
    </div>
  </div>
</template>

<script setup>
// 1. Vue imports
import { ref, computed, watch } from 'vue'

// 2. Third-party imports
import { ElIcon } from 'element-plus'
import { Camera, Delete, UserFilled, Upload, Warning, Document } from '@element-plus/icons-vue'

// 3. Props & Emits
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value),
  },
  accept: {
    type: String,
    default: 'image/*',
  },
  allowDrag: {
    type: Boolean,
    default: true,
  },
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024, // 5MB
  },
  allowedTypes: {
    type: Array,
    default: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  },
  validateFile: {
    type: Function,
    default: null,
  },
  showFileInfo: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change', 'remove', 'error'])

// 4. Local state
const fileInputRef = ref(null)
const imageError = ref(false)
const error = ref('')
const fileInfo = ref(null)
const loading = ref(false)
const isDragOver = ref(false)

// 5. Computed properties
const avatarSrc = computed(() => {
  const hasValue = props.modelValue && props.modelValue.trim() !== ''
  return hasValue && !imageError.value ? props.modelValue : ''
})

const isDefaultAvatar = computed(() => {
  return !props.modelValue || props.modelValue.includes('ui-avatars.com')
})

const avatarSize = computed(() => {
  const sizeMap = {
    small: 80,
    medium: 100,
    large: 120,
  }
  return sizeMap[props.size] || sizeMap.medium
})

// 6. Methods
const getInitials = (name) => {
  if (!name) return ''
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const onImageError = () => {
  imageError.value = true
  error.value = 'Không thể tải ảnh avatar!'
  emit('error', 'Không thể tải ảnh avatar!')
}

const validateFileType = (file) => {
  if (!props.allowedTypes.includes(file.type)) {
    error.value = `Chỉ chấp nhận các định dạng: ${props.allowedTypes.map((type) => type.split('/')[1]).join(', ')}`
    return false
  }
  return true
}

const validateFileSize = (file) => {
  if (file.size > props.maxSize) {
    error.value = `Kích thước file không được vượt quá ${formatFileSize(props.maxSize)}`
    return false
  }
  return true
}

const validateFile = (file) => {
  error.value = ''

  // Validate file type
  if (!validateFileType(file)) {
    emit('error', error.value)
    return false
  }

  // Validate file size
  if (!validateFileSize(file)) {
    emit('error', error.value)
    return false
  }

  // Custom validation
  if (props.validateFile) {
    const customValidation = props.validateFile(file)
    if (customValidation !== true) {
      error.value = typeof customValidation === 'string' ? customValidation : 'File không hợp lệ'
      emit('error', error.value)
      return false
    }
  }

  return true
}

const processFile = (file) => {
  if (!validateFile(file)) {
    return
  }

  // Show loading state
  loading.value = true

  // Store file info
  fileInfo.value = {
    name: file.name,
    size: file.size,
    type: file.type,
  }

  // Create preview
  const reader = new FileReader()
  reader.onload = (e) => {
    imageError.value = false
    error.value = ''
    loading.value = false
    emit('update:modelValue', e.target.result)
    emit('change', {
      raw: file,
      name: file.name,
      size: file.size,
      type: file.type,
      preview: e.target.result,
    })
  }

  reader.onerror = () => {
    error.value = 'Không thể đọc file ảnh!'
    loading.value = false
    emit('error', error.value)
  }

  reader.readAsDataURL(file)
}

const handleClick = () => {
  if (props.disabled || loading.value) return
  fileInputRef.value?.click()
}

const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  processFile(file)

  // Clear input for next upload
  event.target.value = ''
}

const handleDragEnter = (event) => {
  if (!props.allowDrag || props.disabled || loading.value) return
  event.preventDefault()
  isDragOver.value = true
}

const handleDragOver = (event) => {
  if (!props.allowDrag || props.disabled || loading.value) return
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event) => {
  if (!props.allowDrag || props.disabled || loading.value) return
  event.preventDefault()
  // Only set to false if we're leaving the container entirely
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

const handleDrop = (event) => {
  if (!props.allowDrag || props.disabled || loading.value) return
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer.files
  if (files.length > 0) {
    const file = files[0]
    processFile(file)
  }
}

const handleRemove = () => {
  imageError.value = false
  error.value = ''
  fileInfo.value = null

  // Clear file input
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }

  emit('update:modelValue', '')
  emit('remove')
}

// 7. Watchers
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      imageError.value = false
      error.value = ''
    }
  },
)

// 8. Expose methods for parent components
defineExpose({
  clearFiles: () => {
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
    fileInfo.value = null
  },
  triggerUpload: () => {
    if (!props.disabled && !loading.value) {
      fileInputRef.value?.click()
    }
  },
})
</script>

<style lang="scss" scoped>
// Avatar Container Hover Effects
.avatar-container {
  &:hover:not(.opacity-50):not(.is-loading) {
    @apply scale-110;

    .avatar-square {
      @apply shadow-3xl scale-110 border-white;
      animation: pulse 2s infinite;
      box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    }

    .upload-overlay {
      @apply opacity-100;

      .overlay-content {
        @apply translate-y-0;
      }

      .upload-icon-wrapper {
        @apply scale-110 bg-white/30;
        animation: bounce 1s infinite;
      }

      .upload-icon {
        @apply scale-110;
      }
    }
  }

  &.is-loading {
    .avatar-square {
      animation: pulse 1.5s infinite;
    }
  }

  &.is-dragover {
    .avatar-square {
      @apply scale-105;
      animation: pulse 0.5s infinite;
    }
  }

  // Focus styles for accessibility
  &:focus {
    outline: none;
    .avatar-square {
      box-shadow:
        0 0 0 3px rgba(139, 92, 246, 0.3),
        0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }
  }
}

// Delete Button Hover Effects
.delete-btn {
  &:hover:not(:disabled) {
    @apply scale-110 border-white bg-gradient-to-br from-red-600 via-red-700 to-red-800 shadow-2xl;
    box-shadow:
      0 20px 25px -5px rgba(239, 68, 68, 0.3),
      0 10px 10px -5px rgba(239, 68, 68, 0.2);

    .delete-icon {
      @apply scale-110;
      animation: shake 0.5s ease-in-out;
    }
  }

  &:focus {
    @apply shadow-xl;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
  }
}

// Size Variants
.avatar-upload {
  &[data-size='small'] {
    .avatar-wrapper {
      @apply p-0.5;
    }

    .avatar-container {
      @apply m-0.5;
    }

    .avatar-square {
      @apply h-20 w-20 border-2;
    }

    .delete-btn {
      @apply -top-1 -right-1 h-6 w-6;

      .delete-icon {
        @apply h-3 w-3;
      }
    }

    .placeholder-icon {
      @apply text-4xl;
    }

    .initials {
      @apply text-2xl;
    }
  }

  &[data-size='large'] {
    .avatar-wrapper {
      @apply p-2;
    }

    .avatar-container {
      @apply m-2;
    }

    .avatar-square {
      @apply h-36 w-36 border-4;
    }

    .delete-btn {
      @apply -top-3 -right-3 h-12 w-12;

      .delete-icon {
        @apply h-6 w-6;
      }
    }

    .placeholder-icon {
      @apply text-8xl;
    }

    .initials {
      @apply text-6xl;
    }
  }
}

// Responsive Design
@media (max-width: 640px) {
  .avatar-upload {
    @apply max-w-full;
  }

  .avatar-wrapper {
    @apply p-0.5;
  }

  .avatar-container {
    @apply m-0.5;
  }

  .avatar-square {
    @apply h-20 w-20 border-2;
  }

  .delete-btn {
    @apply -top-1 -right-1 h-6 w-6;

    .delete-icon {
      @apply h-3 w-3;
    }
  }

  .placeholder-icon {
    @apply text-4xl;
  }

  .initials {
    @apply text-2xl;
  }

  .error-message {
    @apply mt-4;
  }

  .file-info {
    @apply mt-4;
  }
}

// Disabled States
.avatar-container.opacity-50 {
  .upload-overlay,
  .delete-btn {
    @apply hidden;
  }

  &:hover {
    @apply transform-none;

    .avatar-square {
      @apply transform-none border-white/80 shadow-2xl;
    }
  }
}

// Loading States
.avatar-container.is-loading {
  .upload-overlay {
    @apply opacity-100;

    .overlay-content {
      @apply translate-y-0;
    }
  }
}

// Animations
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.avatar-container:hover .delete-btn {
  animation: float 2s ease-in-out infinite;
}

// Glass Morphism Effects
.avatar-square {
  @apply bg-gradient-to-br from-violet-500/95 via-purple-500/95 to-fuchsia-500/95 backdrop-blur-sm;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.upload-overlay {
  @apply bg-gradient-to-br from-black/80 via-black/70 to-black/60 backdrop-blur-md;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.drag-overlay {
  animation: pulse 0.5s infinite;
}

// Loading Spinner
.loading-spinner {
  .spinner {
    animation: spin 1s linear infinite;
  }
}

// File Info Styling
.file-info {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

// Enhanced hover effects for upload icon wrapper
.upload-icon-wrapper {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

// Improved text shadows
.upload-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.drag-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.initials {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

// Smooth transitions for all interactive elements
.avatar-container * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Hidden file input
.hidden-file-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 0;
  height: 0;
}

// Error and Info Messages
.file-info {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.1);
  }
}

// Simple error message
.error-message {
  opacity: 0.9;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}

// Simple upload instructions
.upload-instructions {
  opacity: 0.8;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}
</style>
