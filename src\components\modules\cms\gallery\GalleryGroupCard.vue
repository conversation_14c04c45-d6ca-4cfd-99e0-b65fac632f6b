<template>
  <!-- <PERSON> nhóm gallery -->
  <div class="overflow-hidden rounded-lg bg-white shadow-md">
    <!-- Header v<PERSON>i tên và mô tả nhóm -->
    <div class="flex items-center justify-between border-b border-gray-200 p-4">
      <div>
        <h5 class="text-lg font-semibold text-gray-800">{{ localGroup.name }}</h5>
        <p v-if="localGroup.description" class="text-sm text-gray-600">{{ localGroup.description }}</p>
      </div>
      <div class="flex items-center gap-2">
        <button
          class="cursor-pointer rounded-md bg-blue-600 px-2 py-1 text-xs font-semibold text-white shadow-md hover:bg-blue-700"
          @click="openEditGroupModal(localGroup)"
          title="Chỉnh sửa nhóm"
        >
          Sửa
        </button>
        <button
          class="cursor-pointer rounded-md bg-red-600 px-2 py-1 text-xs font-semibold text-white shadow-md hover:bg-red-700"
          @click="handleDeleteGroup(localGroup.id)"
          title="Xóa nhóm"
        >
          Xóa
        </button>
      </div>
    </div>

    <!-- Nội dung chính - danh sách ảnh -->
    <div class="p-4">
      <!-- Tiêu đề danh sách ảnh -->
      <div class="mb-4 flex items-center justify-between">
        <h6 class="text-md font-semibold text-gray-700">Danh sách Slider</h6>
        <el-button type="primary" size="small" @click="openAddItemModal">
          <PlusIcon class="mr-1 h-4 w-4" />
          Thêm Ảnh
        </el-button>
      </div>

      <!-- Danh sách ảnh -->
      <div v-if="sortedItems.length === 0" class="rounded-lg bg-gray-50 p-6 text-center">
        <p class="text-gray-500">Chưa có ảnh nào trong nhóm này.</p>
      </div>

      <div v-else class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        <div
          v-for="item in sortedItems"
          :key="item.id"
          class="group relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-lg"
        >
          <!-- Ảnh thumbnail -->
          <div class="aspect-w-16 aspect-h-9 w-full overflow-hidden bg-gray-100">
            <img
              :src="item.image_url"
              :alt="item.alt_text || item.title"
              class="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
              @error="handleImageError"
            />
          </div>

          <!-- Lớp phủ thông tin -->
          <div class="pointer-events-none absolute inset-0 bg-gradient-to-t from-black/60 to-transparent">
            <!-- Actions buttons (hiển thị khi hover) -->
            <div
              class="pointer-events-auto absolute top-2 right-2 z-10 flex scale-90 transform gap-2 opacity-0 transition-all duration-200 group-hover:scale-100 group-hover:opacity-100"
            >
              <button
                class="cursor-pointer rounded-md bg-blue-600 px-2 py-1 text-xs font-semibold text-white shadow-md hover:bg-blue-700"
                @click="openEditItemModal(item)"
                title="Chỉnh sửa ảnh"
              >
                Sửa
              </button>
              <button
                class="cursor-pointer rounded-md bg-red-600 px-2 py-1 text-xs font-semibold text-white shadow-md hover:bg-red-700"
                @click="handleDeleteGalleryItem(localGroup.id, item.id)"
                title="Xóa ảnh"
              >
                Xóa
              </button>
            </div>

            <!-- Thông tin ảnh ở dưới -->
            <div class="absolute bottom-0 left-0 w-full p-3">
              <h6 class="truncate font-semibold text-white" :title="item.title">
                {{ item.title || 'Chưa có tiêu đề' }}
              </h6>
            </div>
          </div>

          <!-- Badge trạng thái -->
          <div class="absolute top-2 right-2 transition-opacity duration-200 group-hover:opacity-0">
            <span
              :class="[
                'rounded-full px-2 py-1 text-xs font-medium',
                item.status === 1
                  ? 'bg-green-100/80 text-green-900 backdrop-blur-sm'
                  : 'bg-red-100/80 text-red-900 backdrop-blur-sm',
              ]"
            >
              {{ item.status === 1 ? 'Hiện' : 'Ẩn' }}
            </span>
          </div>

          <!-- Badge thứ tự -->
          <div
            class="absolute top-2 left-2 flex h-6 w-6 items-center justify-center rounded-full bg-gray-800/50 text-xs font-bold text-white shadow-md backdrop-blur-sm transition-opacity duration-200 group-hover:opacity-0"
            title="Thứ tự"
          >
            {{ item.order }}
          </div>
        </div>
      </div>
    </div>

    <!-- Modal thêm/sửa ảnh -->
    <GalleryItemFormModal
      :isVisible="isItemModalVisible"
      :itemData="selectedItem"
      @close="closeItemModal"
      @save-item="handleSaveItem"
    />

    <!-- Modal thêm/sửa nhóm -->
    <GalleryGroupFormModal
      :isVisible="isGroupModalVisible"
      :groupData="selectedGroup"
      @close="closeGroupModal"
      @save="handleSaveGroup"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Components
import GalleryItemFormModal from './GalleryItemFormModal.vue'
import GalleryGroupFormModal from './GalleryGroupFormModal.vue'

// Icons
import { PlusIcon } from '@/components/icons/index.js'

// Composables
import { useGalleries } from '@/composables/modules/cms/useGallery.js'

// ===== PROPS & EMITS =====
/**
 * Props của component
 */
const props = defineProps({
  /** @type {Object} Dữ liệu nhóm gallery */
  group: {
    type: Object,
    required: true,
  },
})

/**
 * Events được emit từ component
 */
const emit = defineEmits(['update-gallery'])

// ===== COMPOSABLE SETUP =====
const { deleteGalleryItem, updateGalleryItem, createGalleryItem, deleteGallery, updateGallery, createGallery } =
  useGalleries()

/** @type {import('vue').Ref<Object>} Bản sao local của group để theo dõi thay đổi */
const localGroup = ref({ ...props.group })

/** @type {import('vue').Ref<boolean>} Trạng thái hiển thị modal thêm/sửa ảnh */
const isItemModalVisible = ref(false)

/** @type {import('vue').Ref<Object|null>} Dữ liệu ảnh đang được chọn */
const selectedItem = ref(null)

/** @type {import('vue').Ref<boolean>} Trạng thái hiển thị modal thêm/sửa nhóm */
const isGroupModalVisible = ref(false)

/** @type {import('vue').Ref<Object|null>} Dữ liệu nhóm đang được chọn */
const selectedGroup = ref(null)

// ===== COMPUTED =====
/**
 * Sắp xếp danh sách ảnh theo thứ tự tăng dần
 * @type {import('vue').ComputedRef<Array>}
 */
const sortedItems = computed(() => {
  if (!localGroup.value.galleryItems) return []
  return [...localGroup.value.galleryItems].sort((a, b) => a.order - b.order)
})

// ===== WATCHERS =====
/**
 * Theo dõi thay đổi từ prop group để cập nhật lại localGroup
 */
watch(
  () => props.group,
  (newGroup) => {
    localGroup.value = { ...newGroup }
  },
  { deep: true },
)

// ===== MODAL METHODS =====
/**
 * Mở modal chỉnh sửa nhóm ảnh
 * @param {Object} group - Dữ liệu nhóm cần chỉnh sửa
 */
const openEditGroupModal = (group) => {
  selectedGroup.value = { ...group }
  isGroupModalVisible.value = true
}

/**
 * Mở modal thêm mới ảnh
 */
const openAddItemModal = () => {
  selectedItem.value = {
    image_url: '', // file ảnh
    alt_text: '', // Mô tả ảnh (SEO alt)
    title: '', // Tiêu đề ảnh
    link: '', // Đường dẫn khi click ảnh
    target: '_self', // Target mở link
    status: 1, // 1: hoạt động, 0: không hoạt động
    order: localGroup.value.galleryItems?.length ? localGroup.value.galleryItems.length + 1 : 1, // Thứ tự hiển thị
  }
  isItemModalVisible.value = true
}

/**
 * Mở modal chỉnh sửa ảnh
 * @param {Object} galleryItem - Dữ liệu ảnh cần chỉnh sửa
 */
const openEditItemModal = (galleryItem) => {
  selectedItem.value = { ...galleryItem }
  isItemModalVisible.value = true
}

/**
 * Đóng modal thêm/chỉnh sửa ảnh
 */
const closeItemModal = () => {
  isItemModalVisible.value = false
  selectedItem.value = null
}

/**
 * Đóng modal chỉnh sửa nhóm ảnh
 */
const closeGroupModal = () => {
  isGroupModalVisible.value = false
  selectedGroup.value = null
}

// ===== EVENT HANDLERS =====
/**
 * Xử lý lưu thông tin ảnh (thêm mới hoặc cập nhật)
 * @param {Object} itemData - Dữ liệu ảnh cần lưu
 */
const handleSaveItem = async (itemData) => {
  try {
    if (itemData.id) {
      // Nếu có ID thì cập nhật ảnh đã tồn tại
      const updatedItemData = {
        ...itemData,
        _method: 'PUT',
      }
      await updateGalleryItem(localGroup.value.id, itemData.id, updatedItemData)
    } else {
      // Nếu không có ID thì thêm mới ảnh
      await createGalleryItem(localGroup.value.id, itemData)
    }
    closeItemModal()
    emit('update-gallery', localGroup.value.id)
  } catch (error) {
    console.error('Lỗi khi lưu ảnh:', error)
  }
}

/**
 * Xử lý lưu thông tin nhóm ảnh
 * @param {Object} groupData - Dữ liệu nhóm cần lưu
 */
const handleSaveGroup = async (groupData) => {
  try {
    if (groupData.id) {
      await updateGallery(groupData.id, groupData)
    } else {
      await createGallery(groupData)
    }
    emit('update-gallery', groupData.id)
    closeGroupModal()
  } catch (error) {
    console.error('Lỗi khi lưu nhóm:', error)
  }
}

/**
 * Xử lý khi xóa nhóm ảnh
 * @param {string|number} groupId - ID của nhóm ảnh cần xóa
 */
const handleDeleteGroup = async (groupId) => {
  try {
    await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa nhóm ảnh này không?', 'Xác nhận xóa', {
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      type: 'warning',
    })
    await deleteGallery(groupId)
    emit('update-gallery')
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('Đã hủy thao tác xóa.')
    } else {
      console.error('Lỗi khi xóa nhóm:', error)
    }
  }
}

/**
 * Xử lý khi xóa ảnh
 * @param {string|number} groupId - ID của nhóm ảnh
 * @param {string|number} itemId - ID của ảnh cần xóa
 */
const handleDeleteGalleryItem = async (groupId, itemId) => {
  try {
    await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa ảnh này không?', 'Xác nhận xóa', {
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      type: 'warning',
    })
    await deleteGalleryItem(groupId, itemId)
    emit('update-gallery', groupId)
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('Đã hủy thao tác xóa.')
    } else {
      console.error('Lỗi khi xóa ảnh:', error)
    }
  }
}

/**
 * Xử lý lỗi khi không tải được ảnh
 * @param {Event} event - Event lỗi
 */
const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg' // Ảnh placeholder khi lỗi
  event.target.alt = 'Không thể tải ảnh'
}
</script>

<style scoped>
.slider-thumbnail {
  max-width: 120px;
  max-height: 80px;
  object-fit: cover;
  border-radius: 4px;
}
</style>
