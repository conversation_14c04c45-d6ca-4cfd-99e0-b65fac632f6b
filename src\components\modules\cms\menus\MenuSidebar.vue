<template>
  <div class="menu-sidebar flex h-full flex-col">
    <!-- Search and Action Buttons -->
    <div class="flex items-center gap-3">
      <el-input
        v-model="searchQuery"
        placeholder="Tìm kiếm menu..."
        clearable
        class="search-input flex-1"
        @input="handleInput"
        @clear="handleClear"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- Action buttons group -->
      <div class="flex gap-1.5">
        <ButtonCommon text="Tạo Menu" :icon="PlusIcon" type="primary" size="medium" @click="$emit('create-menu')" />
      </div>
    </div>

    <!-- Search Results Info -->
    <div v-if="searchQuery && !loading" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Tìm thấy {{ menus.length }} menu{{ menus.length !== 1 ? '' : '' }}
    </div>

    <!-- Menu List with fixed height -->
    <div class="menu-list-container mt-5 rounded-lg border border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900/50">
      <div v-loading="loading" class="menu-list-scroll p-2" @scroll="handleScroll" ref="scrollContainer">
        <div
          v-for="menu in menus"
          :key="menu.id"
          class="mb-2 flex cursor-pointer items-center justify-between rounded-lg border p-3 transition-all duration-200 hover:shadow-md dark:hover:shadow-lg"
          :class="{
            'border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg dark:border-blue-500 dark:from-blue-900/30 dark:to-indigo-900/30 dark:shadow-blue-500/20':
              selectedMenu?.id === menu.id,
            'border-gray-200 bg-white hover:border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500':
              selectedMenu?.id !== menu.id,
          }"
          @click="$emit('select-menu', menu)"
        >
          <!-- Menu Info -->
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <div class="text-base font-medium text-gray-900 dark:text-white">
                {{ menu.name || 'Không xác định' }}
              </div>
              <!-- Selected indicator -->
              <div v-if="selectedMenu?.id === menu.id" class="flex items-center gap-1">
                <div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400"> Đang chọn </span>
              </div>
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ menu.location_key }}
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center gap-2">
            <ButtonCommon
              :icon="EditIcon"
              type="info"
              size="small"
              rounded
              tooltip="Chỉnh sửa menu"
              @click.stop="$emit('edit-menu', menu)"
            />
            <ButtonCommon
              :icon="TrashIcon"
              type="danger"
              size="small"
              rounded
              tooltip="Xóa menu"
              @click.stop="$emit('delete-menu', menu.id)"
            />
          </div>
        </div>

        <!-- Loading more indicator -->
        <div v-if="loadingMore" class="py-4 text-center">
          <el-icon class="animate-spin text-blue-500">
            <Loading />
          </el-icon>
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">Đang tải thêm...</div>
        </div>

        <!-- Empty state -->
        <div v-if="!loading && menus.length === 0" class="py-8 text-center text-gray-500 dark:text-gray-400">
          <div class="flex flex-col items-center gap-2">
            <div class="text-lg">🔍</div>
            <div class="font-medium">
              {{ searchQuery ? 'Không tìm thấy menu nào' : 'Không có menu nào' }}
            </div>
            <div class="text-sm">
              {{ searchQuery ? `Không có menu nào phù hợp với "${searchQuery}"` : 'Hãy tạo menu đầu tiên' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Search, Loading } from '@element-plus/icons-vue'
import { PlusIcon, EditIcon, TrashIcon, RefreshIcon } from '@/components/icons/index.js'
import ButtonCommon from '@/components/common/ButtonCommon.vue'

// Debounce function
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Props
const props = defineProps({
  menus: {
    type: Array,
    default: () => [],
  },
  selectedMenu: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  loadingMore: {
    type: Boolean,
    default: false,
  },
  hasMorePages: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['create-menu', 'edit-menu', 'delete-menu', 'select-menu', 'refresh', 'load-more', 'search'])

// Local state
const searchQuery = ref('')
const scrollContainer = ref(null)



// Methods
const debouncedSearch = debounce((query) => {
  emit('search', query)
}, 300)

const handleInput = () => {
  // Emit search event to parent component with debounce
  debouncedSearch(searchQuery.value)
}

const handleClear = () => {
  searchQuery.value = ''
  // Emit empty search to reset immediately
  emit('search', '')
}

const handleScroll = (event) => {
  // Don't load more when searching or loading
  if (props.loadingMore || !props.hasMorePages || searchQuery.value) return

  const { scrollTop, scrollHeight, clientHeight } = event.target
  const threshold = 50 // pixels from bottom

  if (scrollHeight - scrollTop - clientHeight < threshold) {
    emit('load-more')
  }
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/cms/menu-sidebar' as *;
</style>
