import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { getDataByKey, destroyDataByKey } from '@/utils/helpers/localStorage.helper.js'

export const useAuthStore = defineStore('auth', () => {
  /* states */
  const getInitialAuthUser = () => {
    const authUserData = getDataByKey('authUser')
    if (!authUserData) return null

    try {
      return JSON.parse(authUserData)
    } catch (error) {
      console.error('Error parsing authUser from localStorage:', error)
      return null
    }
  }

  const authUser = ref(getInitialAuthUser())

  /* Getters */
  // Ki<PERSON>m tra cả token và authUser để đảm bảo đã đăng nhập hoàn toàn
  const isAuthenticated = computed(() => {
    const hasToken = !!getDataByKey('access_token')
    const hasUser = !!authUser.value
    return hasToken && hasUser
  })

  const userDisplayName = computed(() => {
    if (!authUser.value) return 'User'
    return authUser.value.name || authUser.value.email || 'User'
  })

  /* Actions */
  function setAuthUser(data) {
    authUser.value = data
  }

  function logout() {
    authUser.value = null
    destroyDataByKey('access_token')
    destroyDataByKey('authUser')
  }

  return {
    authUser, // Return ref directly for better reactivity
    isAuthenticated,
    userDisplayName,
    setAuthUser,
    logout,
  }
})
