<template>
  <div class="post-edit-wrapper">
    <PageBreadcrumb
      :page-title="currentPageTitle"
      :breadcrumbs="[
        { label: 'Quản lý CMS', to: '/cms' },
        { label: 'Quản lý bài viết', to: '/cms/posts' },
      ]"
    />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Action Buttons -->
      <div class="mb-8 flex items-center justify-between">
        <ButtonCommon
          @click="$router.push('/cms/posts')"
          :icon="ArrowLeft"
          class="reset-btn"
          size="medium"
        >
          Quay lại
        </ButtonCommon>
        <div class="flex items-center gap-3">
          <ButtonCommon
            @click="handleSaveDraft"
            :loading="loading"
            :icon="SaveIcon"
            class="reset-btn"
            size="medium"
          >
            <PERSON><PERSON><PERSON> nh<PERSON>p
          </ButtonCommon>
          <ButtonCommon
            type="primary"
            @click="handleUpdate"
            :loading="loading"
            :icon="EditIcon"
            class="create-post-btn"
            size="medium"
          >
            Cập nhật
          </ButtonCommon>
        </div>
      </div>

      <!-- Form Content -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="top"
        hide-required-asterisk
        v-loading="loading"
      >
        <el-row :gutter="32">
          <!-- Left Column - Main Content (2/3) -->
          <el-col :span="16">
            <!-- Thông tin cơ bản -->
            <div
              class="mb-8 overflow-hidden rounded-2xl border-2 border-gray-200 bg-white shadow-sm transition-all duration-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20 hover:-translate-y-1 hover:border-blue-500 hover:shadow-lg dark:border-slate-600 dark:bg-slate-900 dark:focus-within:border-blue-400 dark:focus-within:ring-blue-400/20 dark:hover:border-blue-400"
            >
              <div
                class="relative border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 dark:border-slate-600 dark:from-slate-800 dark:to-slate-700"
              >
                <div class="absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-blue-500 to-blue-600"></div>
                <h2 class="flex items-center gap-3 text-xl font-semibold text-gray-900 dark:text-slate-100">
                  📝 Thông tin cơ bản
                </h2>
              </div>
              <div class="p-8 pt-10">
                <el-form-item prop="category_id">
                  <template #label>
                    <span>Danh mục <span style="color: #f56c6c">*</span></span>
                  </template>
                  <el-select v-model="formData.category_id" placeholder="Chọn danh mục" style="width: 100%" clearable>
                    <el-option
                      v-for="category in categories"
                      :key="category.id"
                      :label="category.name"
                      :value="category.id"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item prop="title">
                  <template #label>
                    <span>Tiêu đề bài viết <span style="color: #f56c6c">*</span></span>
                  </template>
                  <el-input
                    v-model="formData.title"
                    placeholder="Nhập tiêu đề bài viết"
                    maxlength="255"
                    show-word-limit
                    size="large"
                    @input="(value) => {
                      generateSlug()
                      handleInputChange('title', value, { required: true, minLength: 3 })
                    }"
                    @blur="() => handleFieldBlur('title')"
                  />
                </el-form-item>

                <el-form-item prop="slug">
                  <template #label>
                    <span>Slug <span style="color: #f56c6c">*</span></span>
                  </template>
                  <el-input
                    v-model="formData.slug"
                    placeholder="URL thân thiện (tự động tạo từ tiêu đề)"
                    maxlength="255"
                    show-word-limit
                    size="large"
                    @input="(value) => {
                      handleSlugInput()
                      handleInputChange('slug', value, { required: true, minLength: 3 })
                    }"
                    @blur="() => handleFieldBlur('slug')"
                  />
                  <div class="form-help text-gray-500 dark:text-gray-300">
                    Để trống để tự động tạo từ tiêu đề bài viết
                  </div>
                </el-form-item>

                <el-form-item label="Tóm tắt" prop="excerpt">
                  <el-input
                    v-model="formData.excerpt"
                    type="textarea"
                    :rows="4"
                    placeholder="Nhập tóm tắt ngắn gọn về bài viết"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item prop="body">
                  <template #label>
                    <span>Nội dung <span style="color: #f56c6c">*</span></span>
                  </template>
                  <div class="editor-container">
                    <QuillEditor
                      v-model="formData.body"
                      placeholder="Nhập nội dung bài viết..."
                      height="450px"
                      upload-url="/api/upload-image"
                      @blur="() => {
                        validateBodyContent()
                        handleFieldBlur('body')
                      }"
                      @input="(value) => {
                        validateBodyContentRealtime(value)
                        handleInputChange('body', value, { required: true, minLength: 10 })
                      }"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- Thông tin SEO -->
            <div
              class="mb-8 overflow-hidden rounded-2xl border-2 border-gray-200 bg-white shadow-sm transition-all duration-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20 hover:-translate-y-1 hover:border-blue-500 hover:shadow-lg dark:border-slate-600 dark:bg-slate-900 dark:focus-within:border-blue-400 dark:focus-within:ring-blue-400/20 dark:hover:border-blue-400"
            >
              <div
                class="relative border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 dark:border-slate-600 dark:from-slate-800 dark:to-slate-700"
              >
                <div class="absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-green-500 to-green-600"></div>
                <h2 class="flex items-center gap-3 text-xl font-semibold text-gray-900 dark:text-slate-100">
                  🔍 Thông tin SEO
                </h2>
              </div>
              <div class="p-8 pt-10">
                <el-form-item label="Meta Title" prop="meta_title">
                  <el-input
                    v-model="formData.meta_title"
                    placeholder="Nhập meta title cho SEO"
                    maxlength="60"
                    show-word-limit
                  />
                  <div class="form-help text-gray-500 dark:text-gray-300">
                    Khuyến nghị: 50-60 ký tự. Mô tả ngắn gọn nội dung trang cho công cụ tìm kiếm.
                  </div>
                </el-form-item>

                <el-form-item label="Meta Description" prop="meta_description">
                  <el-input
                    v-model="formData.meta_description"
                    type="textarea"
                    :rows="3"
                    placeholder="Nhập meta description cho SEO"
                    maxlength="160"
                    show-word-limit
                  />
                  <div class="form-help text-gray-500 dark:text-gray-300">
                    Khuyến nghị: 140-160 ký tự. Mô tả chi tiết nội dung bài viết cho kết quả tìm kiếm.
                  </div>
                </el-form-item>

                <el-form-item label="Meta Keywords" prop="meta_keywords">
                  <el-input
                    v-model="formData.meta_keywords"
                    placeholder="Nhập các từ khóa, phân cách bằng dấu phẩy"
                    maxlength="255"
                  />
                  <div class="form-help text-gray-500 dark:text-gray-300">
                    Các từ khóa liên quan đến nội dung bài viết, phân cách bằng dấu phẩy.
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-col>

          <!-- Right Column - Sidebar (1/3) -->
          <el-col :span="8">
            <!-- Ảnh bìa & Tùy chọn -->
            <div
              class="mb-8 overflow-hidden rounded-2xl border-2 border-gray-200 bg-white shadow-sm transition-all duration-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20 hover:-translate-y-1 hover:border-blue-500 hover:shadow-lg dark:border-slate-600 dark:bg-slate-900 dark:focus-within:border-blue-400 dark:focus-within:ring-blue-400/20 dark:hover:border-blue-400"
            >
              <div
                class="relative border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 dark:border-slate-600 dark:from-slate-800 dark:to-slate-700"
              >
                <div class="absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-orange-500 to-orange-600"></div>
                <h2 class="flex items-center gap-3 text-xl font-semibold text-gray-900 dark:text-slate-100">
                  🖼️ Ảnh bìa & Tùy chọn
                </h2>
              </div>
              <div class="p-8 pt-10">
                <el-form-item prop="cover_image">
                  <template #label>
                    <span>Ảnh bìa</span>
                  </template>
                  <div class="upload-area" style="width: 100% !important">
                    <el-upload
                      ref="uploadRef"
                      :before-upload="beforeUpload"
                      :http-request="customUpload"
                      :show-file-list="false"
                      drag
                      accept="image/*"
                      class="upload-full-width w-full"
                      style="width: 100% !important; display: block !important"
                    >
                      <div class="el-upload__text">
                        <div v-if="!formData.cover_image && !formData.cover_image_preview" class="py-8 text-center">
                          <Plus class="mx-auto mb-2 h-8 w-8 text-gray-400" />
                          <div class="text-gray-600 dark:text-gray-300">
                            Kéo thả ảnh vào đây hoặc <em>click để chọn</em>
                          </div>
                          <div class="mt-1 text-xs text-gray-400">Hỗ trợ: JPG, PNG, GIF, WEBP (tối đa 5MB)</div>
                          <div class="text-xs text-gray-400">Kích thước khuyến nghị: 800x450px</div>
                        </div>

                        <!-- Image Preview -->
                        <div v-else class="group relative">
                          <img
                            :src="getImageUrl(formData.cover_image)"
                            alt="Cover Image"
                            class="mx-auto max-h-48 max-w-full rounded object-cover"
                            @error="handleImageError"
                          />
                          <div
                            class="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded bg-black opacity-0 transition-opacity group-hover:opacity-100"
                          >
                            <div class="flex gap-2">
                              <el-button type="primary" size="small" @click.stop="triggerFileSelect">
                                <Plus class="mr-1 h-4 w-4" />
                                Thay đổi
                              </el-button>
                              <el-button type="danger" size="small" @click.stop="removeImage"> Xóa </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-upload>

                    <!-- Upload Progress -->
                    <el-progress
                      v-if="uploadProgress > 0 && uploadProgress < 100"
                      :percentage="uploadProgress"
                      :show-text="true"
                    />
                  </div>
                </el-form-item>

                <el-form-item label="Trạng thái" prop="status">
                  <el-select v-model="formData.status" placeholder="Chọn trạng thái" style="width: 100%">
                    <el-option
                      v-for="option in statusOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span>Bài viết nổi bật</span>
                  </template>
                  <el-switch v-model="formData.is_hot" />
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <span>Hiển thị trên trang chủ</span>
                  </template>
                  <el-switch v-model="formData.show_on_homepage" />
                </el-form-item>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, Plus, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { usePosts } from '@/composables/modules/cms/usePosts.js'
import { useCategories } from '@/composables/modules/cms/useCategories.js'
import { usePostForm } from '@/composables/modules/cms/usePostForm.js'
import { useFormValidation } from '@/composables/useFormValidation.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import QuillEditor from '@/components/common/QuillEditor.vue'
import { SaveIcon, EditIcon } from '@/components/icons/index.js'

// Page title
const currentPageTitle = ref('Chỉnh sửa bài viết')

// Upload ref
const uploadRef = ref(null)

// Router
const router = useRouter()
const route = useRoute()

// Composables
const { loading, currentPost, getPostById, updatePost } = usePosts()
const { categories, fetchCategories } = useCategories()
const {
  formRef,
  formData,
  uploadProgress,
  formRules,
  statusOptions,
  editorOptions,
  generateSlug,
  handleSlugInput,
  handleBeforeUpload,
  handleUpload,
  handleImageError,
  loadPostData,
} = usePostForm()

// Form validation
const {
  validateField,
  validateForm,
  smartValidateField,
  handleInputChange,
  handleFieldBlur,
  createFieldHandlers,
} = useFormValidation()

// Upload methods
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('Chỉ được phép upload file hình ảnh!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('Kích thước file không được vượt quá 5MB!')
    return false
  }
  return true
}

const customUpload = (options) => {
  try {
    // Lưu File object vào formData.cover_image để gửi form-data
    formData.cover_image = options.file

    // Tạo preview URL để hiển thị ảnh
    const previewUrl = URL.createObjectURL(options.file)
    formData.cover_image_preview = previewUrl

    ElMessage.success('Upload ảnh thành công!')

    // Gọi onSuccess callback nếu có
    if (options.onSuccess) {
      options.onSuccess(options.file)
    }
  } catch (error) {
    console.error('Upload error:', error)
    ElMessage.error('Upload ảnh thất bại!')

    // Gọi onError callback nếu có
    if (options.onError) {
      options.onError(error)
    }
  }
}

// Override getImageUrl để hiển thị preview hoặc URL
const getImageUrl = (image) => {
  // Nếu là File object, dùng preview URL
  if (image instanceof File) {
    return formData.cover_image_preview || URL.createObjectURL(image)
  }

  // Nếu có preview URL, dùng preview URL
  if (formData.cover_image_preview) {
    return formData.cover_image_preview
  }

  // Nếu là string URL từ server
  if (typeof image === 'string' && image) {
    return image.startsWith('http') ? image : `${import.meta.env.VITE_API_BASE_URL}${image}`
  }

  return ''
}

// Override removeImage để cleanup
const removeImage = () => {
  // Cleanup preview URL
  if (formData.cover_image_preview) {
    URL.revokeObjectURL(formData.cover_image_preview)
    formData.cover_image_preview = null
  }

  formData.cover_image = ''
  ElMessage.success('Đã xóa ảnh bìa!')
}

// Trigger file select khi click "Thay đổi"
const triggerFileSelect = () => {
  const fileInput = uploadRef.value?.$el?.querySelector('input[type="file"]')
  if (fileInput) {
    fileInput.click()
  }
}

// Methods
const validateBodyContent = () => {
  // Trigger manual validation for body field
  if (formRef.value) {
    formRef.value.validateField('body')
  }
}

// Enhanced validation for body content with real-time feedback
const validateBodyContentRealtime = (value) => {
  const textContent = value?.replace(/<[^>]*>/g, '').trim() || ''
  
  // Add a small delay to prevent too frequent validation
  setTimeout(() => {
    if (!textContent) {
      // Show error immediately if empty
      if (formRef.value) {
        formRef.value.validateField('body')
      }
    } else if (textContent.length < 10) {
      // Show error if too short
      if (formRef.value) {
        formRef.value.validateField('body')
      }
    } else {
      // Clear error if valid
      if (formRef.value) {
        formRef.value.clearValidate('body')
      }
    }
  }, 300) // 300ms delay to prevent spam
}

const handleSaveDraft = async () => {
  try {
   

    const isValid = await formRef.value?.validate().catch(() => false)
    if (!isValid) {
      ElMessage.warning('Vui lòng kiểm tra lại thông tin nhập vào')
      return
    }

    const updateData = { ...formData, status: 'draft' }
    await updatePost(route.params.id, updateData)
    ElMessage.success('Lưu bản nháp thành công!')

    // Không cần navigate khi lưu draft, chỉ thông báo
  } catch (error) {
    console.error('Error saving draft:', error)
  }
}

const handleUpdate = async () => {
  try {
   

    const isValid = await formRef.value?.validate().catch(() => false)
    if (!isValid) {
      ElMessage.warning('Vui lòng kiểm tra lại thông tin nhập vào')
      return
    }

    await updatePost(route.params.id, formData)
    ElMessage.success('Cập nhật bài viết thành công!')

    // Delay nhỏ để đảm bảo toast hiển thị
    setTimeout(() => {
      router.push('/cms/posts')
    }, 1500)
  } catch (error) {
    console.error('Error updating post:', error)
  }
}

// Initialize
onMounted(async () => {
  try {
    await Promise.all([getPostById(route.params.id), fetchCategories()])
    loadPostData(currentPost.value)
  } catch (error) {
    console.error('Error initializing post edit:', error)
    router.push('/cms/posts')
  }
})
</script>

<style lang="scss">
@use '@/assets/styles/modules/cms/post-create';

/* Hide Element Plus default left-side red asterisks */
:deep(.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:before) {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
}

:deep(.el-form-item.is-required .el-form-item__label:before) {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
}

:deep(.el-form-item__label:before) {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
}

/* Error styling is now handled in _post-create.scss with higher specificity */

/* Additional inline override for maximum specificity - like MenuFormModal */
.post-edit-wrapper .el-form-item__error {
  margin-top: 12px !important;
  margin-bottom: 8px !important;
  padding: 12px 16px !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
  color: #ef4444 !important;
  background: #fef2f2 !important;
  border: 1px solid #fca5a5 !important;
  border-radius: 8px !important;
  position: static !important;
  display: block !important;
  width: 100% !important;
  clear: both !important;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.1) !important;
}

.dark .post-edit-wrapper .el-form-item__error {
  color: #f87171 !important;
  background: rgba(127, 29, 29, 0.15) !important;
  border-color: #991b1b !important;
  box-shadow: 0 2px 4px rgba(248, 113, 113, 0.1) !important;
}

/* Upload component styling - Make it full width */
.upload-full-width,
.upload-full-width.el-upload,
.upload-full-width.el-upload.el-upload--drag,
:deep(.upload-full-width),
:deep(.upload-full-width .el-upload-dragger),
:deep(.upload-full-width .el-upload__text) {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  display: block !important;
  margin: 0 !important;
  padding: 1rem !important;
  box-sizing: border-box !important;
  border-radius: 0.5rem !important;
}

/* More specific selectors */
:deep(.el-form-item__content .upload-full-width) {
  width: 100% !important;
}

:deep(.el-form-item__content .upload-full-width .el-upload-dragger) {
  width: 100% !important;
  margin: 0 !important;
}

:deep(.el-form-item__content .upload-full-width .el-upload__text) {
  width: 100% !important;
  margin: 0 !important;
}

/* Force override any Element Plus styles */
.el-col .el-form-item .upload-full-width {
  width: 100% !important;
}

.el-col .el-form-item .upload-full-width .el-upload-dragger {
  width: 100% !important;
}

/* Ensure upload area takes full width */
.el-upload--drag {
  width: 100% !important;
  margin: 0 !important;
  display: block !important;
}

.el-upload-dragger {
  width: 100% !important;
  margin: 0 !important;
  display: block !important;
}

/* Remove any default spacing from upload wrapper */
:deep(.el-upload__input) {
  width: 100% !important;
}

:deep(.el-upload .el-upload-dragger) {
  width: 100% !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  display: block !important;
}

/* Make sure the container div also takes full width */
:deep(.el-form-item__content) {
  width: 100% !important;
}

/* Ensure the space-y-3 div takes full width */
.space-y-3 {
  width: 100% !important;
}

/* Make sure any wrapper divs take full width */
:deep(.el-upload-dragger .el-upload__text) {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Force all children of upload to be full width */
:deep(.el-upload *) {
  max-width: 100% !important;
  width: 100% !important;
}

/* Override any inline styles that might limit width */
:deep(.el-upload),
:deep(.el-upload-dragger),
:deep(.el-upload__text) {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
}

/* Remove flex properties that might cause issues */
:deep(.el-upload-dragger) {
  display: block !important;
  flex: none !important;
}

/* Ultra specific selectors to override Element Plus */
.post-edit-wrapper .el-col .el-form-item .upload-full-width.el-upload.el-upload--drag {
  width: 100% !important;
  max-width: none !important;
}

.post-edit-wrapper .el-col .el-form-item .upload-full-width .el-upload-dragger {
  width: 100% !important;
  max-width: none !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Override any potential grid or flex container constraints */
.post-edit-wrapper .el-row .el-col:last-child .el-form-item .upload-full-width {
  width: 100% !important;
}
</style>
