import { ElMessage } from 'element-plus'
import { computed, reactive, ref } from 'vue'
import logsApi from '~/utils/apis/logs.api'
import { PAGINATION, SORT_FILTER } from '~/utils/configs/constant.config'

export const useUserLogs = () => {
  // State
  const userLogs = ref([])
  const loading = ref(false)
  const filters = reactive({
    search: '',
    sort_order: SORT_FILTER.DESC,
    sort_by: 'id',
    page: PAGINATION.DEFAULT_PAGE,
    limit: PAGINATION.DEFAULT_PER_PAGE,
  })

  const pagination = reactive({
    current_page: PAGINATION.DEFAULT_PAGE,
    per_page: PAGINATION.DEFAULT_PER_PAGE,
    total: 0,
    last_page: 1,
    from: 1,
    to: 0,
    has_more_pages: false,
  })

  // Computed
  const isFiltered = computed(() => {
    return filters.search
  })

  // Helper methods

  /**
   * Clearn filter
   * lọc, làm sạch giá trị từ object trước khi gửi Request đến API
   */
  const cleanParams = (filters) => {
    const params = {}

    // làm sạch filters
    for (const [key, value] of Object.entries(filters)) {
      if (value !== '' && value !== null && value !== undefined) {
        params[key] = value
      }
    }

    return params
  }

  // Methods
  const fetchUserLogs = async () => {
    loading.value = true
    try {
      const apiParams = cleanParams(filters)
      const response = await logsApi.getUserLogs(apiParams)

      // response.data của axios, response.data.data của BE trả về
      if (response.data.success) {
        // user logs
        userLogs.value = response.data.data.data

        // lấy ra pagination của response trả về
        const paginationResponse = response.data.data.pagination
        Object.assign(pagination, paginationResponse)
      }
    } catch (error) {
      console.log(error.message)
      ElMessage.error(error.message)
      throw error
    } finally {
      loading.value = false
    }
  }

  const resetFilters = () => {
    filters = {
      search: '',
      sort_order: SORT_FILTER.DESC,
      sort_by: 'id',
      page: PAGINATION.DEFAULT_PAGE,
      limit: PAGINATION.DEFAULT_PER_PAGE,
    }
  }

  return {
    // Reactive, states
    userLogs,
    loading,
    pagination,
    filters,

    //
    isFiltered,

    // User Logs methods
    fetchUserLogs,
    resetFilters,
  }
}
