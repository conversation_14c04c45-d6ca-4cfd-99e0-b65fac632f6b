// Post Create Component Styles
// Tách ra từ PostCreate.vue để duy trì tính tổ chức

/* Import các style cơ bản */
@use './post-list';

/* Form Card Styling */
.form-card {
  background: #ffffff;
  border: 3px solid #cbd5e1; /* Tăng border để thấy rõ hơn */
  border-radius: 12px;
  margin-bottom: 1.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #9ca3af; /* <PERSON><PERSON><PERSON> hơn khi hover */
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  padding: 1.5rem;
  padding-top: 2rem; /* Tăng khoảng cách từ tiêu đề xuống */
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.dark .form-help {
  color: #cbd5e1 !important;
}

/* Editor Styling */
.editor-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ql-editor) {
  min-height: 300px;
  font-size: 16px;
  font-family: inherit;
  background: #ffffff;
  color: #1f2937;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  max-width: 100%;
}

:deep(.ql-editor p) {
  color: #1f2937;
  max-width: 100%;
  overflow-wrap: break-word;
}

:deep(.ql-editor h1),
:deep(.ql-editor h2),
:deep(.ql-editor h3),
:deep(.ql-editor h4),
:deep(.ql-editor h5),
:deep(.ql-editor h6) {
  color: #1f2937;
  max-width: 100%;
  overflow-wrap: break-word;
}

:deep(.ql-editor img) {
  max-width: 100%;
  height: auto;
}

:deep(.ql-toolbar) {
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

:deep(.ql-toolbar .ql-formats) {
  margin-right: 15px;
}

/* Upload Styling - Giống CategoryFormModal */
.upload-container {
  width: 100%;
}

.upload-dragger {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  width: 100%;
  height: 120px;
  text-align: center;
  cursor: pointer;
  position: relative;
  transition: border-color 0.3s;
}

.upload-dragger:hover {
  border-color: #3b82f6;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.upload-icon {
  font-size: 28px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.upload-text p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.upload-hint {
  font-size: 12px !important;
  color: #9ca3af !important;
  margin-top: 4px !important;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.remove-image-btn {
  margin-top: 8px;
}

/* Form Field Enhancements */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  background: #ffffff;
}

:deep(.el-input__wrapper:hover) {
  border-color: #9ca3af;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #1f2937;
}

:deep(.el-textarea__inner:hover) {
  border-color: #9ca3af;
}

:deep(.el-textarea__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-date-editor .el-input__wrapper) {
  border-radius: 8px;
}

/* Checkbox Styling */
:deep(.el-checkbox) {
  align-items: center;
}

:deep(.el-checkbox__label) {
  font-weight: 500;
  color: #374151;
}

/* Light mode input styles */
:deep(.el-input__inner) {
  color: #1f2937;
  background: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

:deep(.el-textarea__inner::placeholder) {
  color: #9ca3af;
}

:deep(.el-switch) {
  --el-switch-on-color: #3b82f6;
  --el-switch-off-color: #d1d5db;
}

:deep(.el-select-dropdown) {
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

:deep(.el-select-dropdown__item) {
  color: #1f2937;
}

:deep(.el-select-dropdown__item:hover) {
  background: #f3f4f6;
  color: #1f2937;
}

:deep(.el-select-dropdown__item.selected) {
  background: #3b82f6;
  color: #ffffff;
}

:deep(.el-picker-panel) {
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

:deep(.el-picker-panel__content) {
  color: #1f2937;
}

:deep(.el-date-table th) {
  color: #6b7280;
}

:deep(.el-date-table td) {
  color: #1f2937;
}

:deep(.el-date-table td.available:hover) {
  background: #f3f4f6;
}

:deep(.el-date-table td.current) {
  background: #3b82f6;
  color: #ffffff;
}

/* Dark mode styles */
.dark .form-card {
  background: #1e293b;
  border: 3px solid #475569;
}

.dark .form-card:hover {
  border-color: #64748b;
}

.dark .card-header {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  border-bottom-color: #475569;
}

.dark .card-title {
  color: #f8fafc;
}

.dark .form-help {
  color: #cbd5e1 !important;
}

.dark .editor-container {
  border-color: #475569;
}

.dark :deep(.ql-toolbar) {
  background: #334155;
  border-bottom-color: #475569;
}

.dark :deep(.ql-editor) {
  background: #1e293b !important;
  color: #f1f5f9 !important;
}

.dark :deep(.ql-editor p) {
  color: #f1f5f9 !important;
}

.dark :deep(.ql-editor h1),
.dark :deep(.ql-editor h2),
.dark :deep(.ql-editor h3),
.dark :deep(.ql-editor h4),
.dark :deep(.ql-editor h5),
.dark :deep(.ql-editor h6) {
  color: #f1f5f9 !important;
}

.dark :deep(.ql-editor::before) {
  color: #94a3b8 !important;
}

.dark :deep(.ql-editor.ql-blank::before) {
  color: #94a3b8 !important;
}

.dark .upload-dragger {
  border-color: #475569 !important;
  background: #334155 !important;
}

.dark .upload-dragger:hover {
  border-color: #3b82f6;
}

.dark .upload-text p {
  color: #94a3b8;
}

.dark .upload-hint {
  color: #94a3b8 !important;
}

.dark .text-gray-400 {
  color: #94a3b8 !important;
}

.dark .text-gray-600 {
  color: #e2e8f0 !important;
}

.dark :deep(.el-form-item__label) {
  color: #e2e8f0;
}

.dark :deep(.el-input__wrapper) {
  background: #334155 !important;
  border-color: #475569 !important;
}

.dark :deep(.el-input__wrapper:hover) {
  border-color: #64748b;
}

.dark :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  background: #334155;
}

.dark :deep(.el-input__inner) {
  background: transparent;
  color: #f1f5f9;
}

.dark :deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

.dark :deep(.el-textarea__inner) {
  background: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark :deep(.el-textarea__inner:hover) {
  border-color: #64748b;
}

.dark :deep(.el-textarea__inner:focus) {
  border-color: #3b82f6;
  background: #334155;
}

.dark :deep(.el-textarea__inner::placeholder) {
  color: #94a3b8 !important;
}

.dark :deep(.el-checkbox__label) {
  color: #e2e8f0;
}

.dark :deep(.el-switch) {
  --el-switch-on-color: #3b82f6;
  --el-switch-off-color: #475569;
}

.dark :deep(.el-select-dropdown) {
  background: #334155;
  border: 1px solid #475569;
}

.dark :deep(.el-select-dropdown__item) {
  color: #f1f5f9;
}

.dark :deep(.el-select-dropdown__item:hover) {
  background: #475569;
  color: #f1f5f9;
}

.dark :deep(.el-select-dropdown__item.selected) {
  background: #3b82f6;
  color: #ffffff;
}

.dark :deep(.el-picker-panel) {
  background: #334155;
  border: 1px solid #475569;
}

.dark :deep(.el-picker-panel__content) {
  color: #f1f5f9;
}

.dark :deep(.el-date-table th) {
  color: #94a3b8;
}

.dark :deep(.el-date-table td) {
  color: #f1f5f9;
}

.dark :deep(.el-date-table td.available:hover) {
  background: #475569;
}

.dark :deep(.el-date-table td.current) {
  background: #3b82f6;
  color: #ffffff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-card {
    margin-bottom: 1rem;
  }

  .card-header {
    padding: 0.75rem 1rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  :deep(.ql-editor) {
    min-height: 250px;
  }

  .upload-dragger {
    height: 100px;
  }

  .upload-placeholder {
    padding: 15px;
  }

  .upload-icon {
    font-size: 24px;
  }

  .upload-text p {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 0.75rem;
  }

  :deep(.ql-editor) {
    min-height: 200px;
  }

  .upload-dragger {
    height: 80px;
  }

  .upload-placeholder {
    padding: 10px;
  }

  .upload-icon {
    font-size: 20px;
  }
}

/* Simple error message styling - like MenuFormModal */
:deep(.el-form-item__error) {
  margin-top: 8px !important;
  margin-bottom: 0 !important;
  padding: 6px 0 !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  color: #ef4444 !important;
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: static !important;
  display: block !important;
  width: 100% !important;
  clear: both !important;
}

/* Dark mode error styling */
.dark :deep(.el-form-item__error) {
  color: #f87171 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Form item spacing */
:deep(.el-form-item) {
  margin-bottom: 28px !important;
}

/* Help text styling */
.form-help {
  margin-top: 6px !important;
  font-size: 12px !important;
  color: #6b7280 !important;
  line-height: 1.4 !important;
}

/* Focus states for cards */
.bg-white:focus-within {
  @apply border-blue-500 ring-2 ring-blue-500/20;
}

.dark .bg-slate-900:focus-within {
  @apply border-blue-400 ring-2 ring-blue-400/20;
}

/* PostCreate specific styles */
.post-create-wrapper {
  @apply min-h-screen;
}

/* Form card base styles (Tailwind classes applied in template) */
.form-card-base {
  @apply mb-8 overflow-hidden rounded-2xl border-2 border-gray-200 bg-white shadow-sm transition-all duration-300 hover:-translate-y-1 hover:border-blue-500 hover:shadow-lg dark:border-slate-600 dark:bg-slate-900 dark:hover:border-blue-400;
}

/* Section header base styles */
.section-header-base {
  @apply relative border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 dark:border-slate-600 dark:from-slate-800 dark:to-slate-700;
}

/* Section title base styles */
.section-title-base {
  @apply flex items-center gap-3 text-xl font-semibold text-gray-900 dark:text-slate-100;
}

/* Content area base styles */
.card-content-base {
  @apply p-8 pt-10;
}

/* Accent stripe variants */
.accent-stripe-blue {
  @apply absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-blue-500 to-blue-600;
}

.accent-stripe-green {
  @apply absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-green-500 to-green-600;
}

.accent-stripe-orange {
  @apply absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-orange-500 to-orange-600;
}

.accent-stripe-purple {
  @apply absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-purple-500 to-purple-600;
}

/* Animation cho form cards */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-card {
  animation: slideInUp 0.3s ease-out;
}

.form-card:nth-child(1) {
  animation-delay: 0s;
}

.form-card:nth-child(2) {
  animation-delay: 0.1s;
}

.form-card:nth-child(3) {
  animation-delay: 0.2s;
}

.form-card:nth-child(4) {
  animation-delay: 0.3s;
}
