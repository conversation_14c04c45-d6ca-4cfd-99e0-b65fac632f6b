import apiAxios from '@/utils/configs/axios.config.js'

// Helper function to clean empty parameters
const cleanParams = (params) => {
  const cleaned = {}
  Object.keys(params).forEach((key) => {
    // Keep 0 values but filter out empty strings, null, and undefined
    if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
      cleaned[key] = params[key]
    }
  })
  return cleaned
}

const categoriesApi = {
  getCategories(params = {}) {
    // If no meaningful parameters, make a simple request without params
    const cleanedParams = cleanParams(params)
    const hasParams = Object.keys(cleanedParams).length > 0
    return apiAxios({
      method: 'get',
      url: 'cms/categories',
      ...(hasParams && { params: cleanedParams }),
    })
  },

  createCategory(categoryData) {
    return apiAxios({
      method: 'post',
      url: 'cms/categories',
      data: categoryData,
    })
  },

  getCategoryById(id) {
    return apiAxios({
      method: 'get',
      url: `cms/categories/${id}`,
    })
  },

  updateCategory(id, categoryData) {
    // If categoryData is FormData, append _method, else convert to FormData
    let dataToSend
    if (categoryData instanceof FormData) {
      categoryData.append('_method', 'PUT')
      dataToSend = categoryData
    } else {
      dataToSend = new FormData()
      Object.entries(categoryData).forEach(([key, value]) => {
        dataToSend.append(key, value)
      })
      dataToSend.append('_method', 'PUT')
    }
    return apiAxios({
      method: 'post', // Method spoofing: always POST
      url: `cms/categories/${id}`,
      data: dataToSend,
    })
  },

  deleteCategory(id) {
    return apiAxios({
      method: 'delete',
      url: `cms/categories/${id}`,
    })
  },

  updateCategoryStatus(id, field, value) {
    return apiAxios({
      method: 'put',
      url: `cms/categories/${id}`,
      data: { [field]: value },
    })
  },
}

export default categoriesApi
