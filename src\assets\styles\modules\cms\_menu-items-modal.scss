.menu-items-modal :deep(.el-dialog__body) {
  padding: 0;
  height: 600px;
}

.menu-items-modal :deep(.el-dialog) {
  max-width: none;
  margin: 0 auto;
  z-index: 100001 !important;
}

.menu-items-modal :deep(.el-overlay) {
  z-index: 100000 !important;
}

.menu-items-container {
  display: flex;
  height: 600px;
  gap: 1px;
  background-color: #e5e7eb;
}

.tree-panel {
  flex: 2;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.form-panel {
  flex: 1;
  background-color: white;
  display: flex;
  flex-direction: column;
  min-width: 400px;
}

// Dark mode for panels
@media (prefers-color-scheme: dark) {
  .tree-panel,
  .form-panel {
    background-color: #1f2937;
  }
}

html.dark .tree-panel,
html.dark .form-panel {
  background-color: #1f2937;
}

.panel-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  flex-shrink: 0;
}

// Dark mode for panel header
@media (prefers-color-scheme: dark) {
  .panel-header {
    border-bottom-color: #374151;
    background-color: #111827;
  }
}

html.dark .panel-header {
  border-bottom-color: #374151;
  background-color: #111827;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-weight: 500;
  color: #111827;
  margin: 0;
}

// Dark mode for panel title
@media (prefers-color-scheme: dark) {
  .panel-title {
    color: #f9fafb;
  }
}

html.dark .panel-title {
  color: #f9fafb;
}

.panel-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.empty-text {
  margin: 0.5rem 0 1rem 0;
  font-size: 0.875rem;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.items-count {
  text-align: center;
  padding: 0.75rem;
  color: #9ca3af;
  font-size: 0.75rem;
  border-top: 1px solid #f3f4f6;
  margin-top: 1rem;
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .loading-more {
    color: #9ca3af;
  }

  .items-count {
    color: #6b7280;
    border-top-color: #374151;
  }
} 