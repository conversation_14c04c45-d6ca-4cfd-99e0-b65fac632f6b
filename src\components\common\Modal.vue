<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :draggable="draggable"
    :center="center"
    :fullscreen="fullscreen"
    :destroy-on-close="destroyOnClose"
    @close="close"
    @open="handleOpen"
    @closed="handleClosed"
    @opened="handleOpened"
    class="custom-dialog"
    :data-size="size !== 'default' ? size : null"
    :data-height="height !== 'auto' ? height : null"
  >
    <slot name="body"></slot>

    <template #footer v-if="$slots.footer">
      <slot name="footer"></slot>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElDialog } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: [String, Number],
    default: '50%',
  },
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
  closeOnPressEscape: {
    type: Boolean,
    default: true,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  draggable: {
    type: Boolean,
    default: false,
  },
  center: {
    type: Boolean,
    default: false,
  },
  fullscreen: {
    type: Boolean,
    default: false,
  },
  destroyOnClose: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['auto', 'compact', 'default', 'large', 'full'].includes(value),
  },
  height: {
    type: String,
    default: 'auto',
    validator: (value) => ['auto', 'compact', 'full'].includes(value),
  },
})

const emit = defineEmits(['update:modelValue', 'close', 'open', 'closed', 'opened'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const close = () => {
  emit('close')
}

const handleOpen = () => emit('open')
const handleClosed = () => emit('closed')
const handleOpened = () => emit('opened')
</script>
