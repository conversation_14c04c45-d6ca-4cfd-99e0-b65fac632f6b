<template>
  <!-- Modal chỉnh sửa/thêm mới ảnh -->
  <el-dialog
    :model-value="isVisible"
    :title="formData.id ? 'Chỉnh sửa ảnh' : 'Thêm mới ảnh'"
    width="1000px"
    @close="close"
  >
    <div class="p-6">
      <!-- Form chỉnh sửa/thêm mới ảnh -->
      <el-form ref="formRef" :model="formData" label-position="top">
        <!-- Tiêu đề ảnh -->
        <el-form-item
          label="Tiêu đề"
          prop="title"
          :rules="[{ required: true, message: 'Tiêu đề là bắt buộc', trigger: 'blur' }]"
        >
          <el-input v-model="formData.title" placeholder="Nhập tiêu đề ảnh" />
        </el-form-item>

        <!-- <PERSON><PERSON> tả ảnh (alt_text) -->
        <el-form-item
          style="margin-top: 26px"
          label="<PERSON><PERSON> tả ảnh (alt_text)"
          prop="alt_text"
          :rules="[{ required: true, message: '<PERSON>ô tả là bắt buộc', trigger: 'blur' }]"
        >
          <el-input v-model="formData.alt_text" placeholder="Nhập mô tả ảnh (SEO alt)" />
        </el-form-item>

        <!-- Chọn ảnh -->
        <el-form-item
          style="margin-top: 26px"
          label="Chọn ảnh"
          prop="image_url"
          :rules="[{ required: true, message: 'Ảnh là bắt buộc', trigger: 'blur' }]"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="image-input-container">
                <div class="file-upload-wrapper">
                  <el-button type="primary" size="small" @click="triggerFileInput"> Chọn ảnh từ máy tính </el-button>
                  <input
                    ref="fileInputRef"
                    type="file"
                    accept="image/*"
                    class="hidden-file-input"
                    @change="handleFileChange"
                  />
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div v-if="imagePreview" class="image-preview-container">
                <img :src="imagePreview" alt="Preview" class="image-preview" />
              </div>
              <div v-else class="no-preview">
                <el-icon><Picture /></el-icon>
                <span>Chưa có ảnh preview</span>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <!-- Đường dẫn và target -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Đường dẫn (Link)" prop="link" style="margin-top: 26px">
              <el-input v-model="formData.link" placeholder="https://example.com" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Target" prop="target" style="margin-top: 26px">
              <el-select v-model="formData.target" class="w-full">
                <el-option label="Mở ở tab hiện tại" value="_self" />
                <el-option label="Mở ở tab mới" value="_blank" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- Thứ tự hiển thị và trạng thái -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="Thứ tự hiển thị (order)"
              prop="order"
              :rules="[{ required: true, message: 'Bắt buộc', trigger: 'blur' }]"
              style="margin-top: 26px"
            >
              <el-input-number v-model="formData.order" :min="1" class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Trạng thái" prop="status" style="margin-top: 26px">
              <el-select v-model="formData.status" class="w-full">
                <el-option label="Hoạt động" :value="1" />
                <el-option label="Không hoạt động" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Footer modal -->

    <ButtonModalCommon
      :loading="loading"
      :can-submit="true"
      cancel-text="Hủy"
      :submit-text="formData.id ? 'Cập nhật' : 'Thêm mới'"
      @cancel="close"
      @submit="submitForm"
    />
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElRow,
  ElCol,
  ElInputNumber,
  ElIcon,
} from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import ButtonModalCommon from '~/components/common/ButtonModalCommon.vue'
import { template } from 'lodash'

// ===== PROPS & EMITS =====

/**
 * Props của component
 */
const props = defineProps({
  /** @type {Boolean} Điều khiển hiển thị modal */
  isVisible: Boolean,

  /** @type {Object|null} Dữ liệu ảnh (null: thêm mới, object: chỉnh sửa) */
  itemData: Object,
})

/**
 * Events được emit từ component
 */
const emit = defineEmits(['close', 'save-item'])

// ===== REACTIVE STATE =====

/** @type {import('vue').Ref<Object|null>} Tham chiếu đến form để validate */
const formRef = ref(null)

/** @type {import('vue').Ref<HTMLInputElement|null>} Tham chiếu đến input file */
const fileInputRef = ref(null)

/** @type {import('vue').Ref<string>} URL preview ảnh */
const imagePreview = ref('')

/**
 * Dữ liệu form với giá trị mặc định
 * @type {import('vue').Ref<Object>}
 */
const formData = ref({
  id: null, // ID ảnh (null nếu thêm mới)
  image_url: '', // File ảnh hoặc URL ảnh
  alt_text: '', // Mô tả ảnh (SEO alt text)
  title: '', // Tiêu đề ảnh
  link: '', // Đường dẫn khi click vào ảnh
  target: '_self', // Target mở link (_self hoặc _blank)
  status: 1, // Trạng thái (1: hoạt động, 0: không hoạt động)
  order: 1, // Thứ tự hiển thị
})

// ===== WATCHERS =====

/**
 * Theo dõi thay đổi của prop itemData để cập nhật form
 * - Nếu có dữ liệu: mode chỉnh sửa
 * - Nếu null: mode thêm mới
 */
watch(
  () => props.itemData,
  (newItem) => {
    if (newItem) {
      // Chỉnh sửa: Copy dữ liệu từ prop vào form
      formData.value = { ...newItem }

      // Cập nhật preview nếu có image_url
      if (newItem.image_url) {
        imagePreview.value = newItem.image_url
      } else {
        imagePreview.value = ''
      }
    } else {
      // Thêm mới: Reset về giá trị mặc định
      formData.value = {
        id: null,
        image_url: '',
        alt_text: '',
        title: '',
        link: '',
        target: '_self',
        status: 1,
        order: 1,
      }
      imagePreview.value = ''
    }

    // Xóa các validation errors
    formRef.value?.clearValidate()
  },
  { immediate: true, deep: true },
)

// ===== METHODS =====

/**
 * Kích hoạt input file khi nhấn nút chọn ảnh
 */
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

/**
 * Xử lý khi người dùng chọn file ảnh
 * - Đọc file và tạo URL preview
 * - Cập nhật image_url với File object
 * @param {Event} event - Sự kiện change của input file
 */
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // Kiểm tra file có phải là ảnh không
  if (!file.type.startsWith('image/')) {
    // Có thể thêm thông báo lỗi ở đây
    return
  }

  // Lưu file object vào formData
  formData.value.image_url = file

  // Tạo URL preview
  imagePreview.value = URL.createObjectURL(file)

  // Reset input để có thể chọn lại cùng một file
  event.target.value = ''
}

/**
 * Đóng modal và thông báo cho component cha
 */
const close = () => {
  emit('close')
}

/**
 * Xử lý submit form
 * - Validate trước khi gửi dữ liệu
 * - Emit sự kiện save-item với dữ liệu đã validate
 */
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid) => {
    if (valid) {
      emit('save-item', formData.value)
    }
  })
}
</script>

<style scoped>
/* Kiểu dáng tùy chỉnh */
.w-full {
  width: 100%;
}

.image-input-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.hidden-file-input {
  display: none;
}

.file-upload-wrapper {
  margin-top: 5px;
}

.image-preview-container {
  width: 100%;
  height: 150px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.no-preview {
  width: 100%;
  height: 150px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.no-preview .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* Footer Button Styles */
.footer-btn {
  min-width: 80px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
  color: #666 !important;
}

.cancel-btn:hover {
  background: #e6e6e6 !important;
  border-color: #bfbfbf !important;
  color: #333 !important;
}

.submit-btn {
  background: #1890ff !important;
  border: 1px solid #1890ff !important;
  color: #fff !important;
}

.submit-btn:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}
</style>
