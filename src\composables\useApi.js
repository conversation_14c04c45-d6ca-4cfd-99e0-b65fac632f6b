import { ref } from 'vue'
import axiosConfig from '@/utils/configs/axios.config.js'

export function useApi() {
  const loading = ref(false)
  const error = ref(null)
  const status = ref(null)

  const callApi = async ({ method = 'get', url, data = null, params = null }) => {
    loading.value = true
    error.value = null
    status.value = null

    try {
      const response = await axiosConfig({ method, url, data, params })
      return { data: response.data, error: null, status: response.status }
    } catch (err) {
      status.value = err?.status || err?.response?.status || 500
      error.value = err?.message || err?.response?.data || err.message || 'Unknown error!'

      // Log lại lỗi
      console.error(`[API ERROR]`, error.value)

      // Trả về cả error và status để xử lý bên ngoài
      return {
        data: null,
        error: error.value,
        status: status.value,
      }
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    status,
    callApi,
  }
}
