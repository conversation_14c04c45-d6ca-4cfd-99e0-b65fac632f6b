/**
 * Statistics Management Composable
 * Handles all statistics-related business logic
 */

import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { overviewApi } from '@/utils/apis/index.js'
import { extractApiResponse } from '@/utils/helpers/response.helper.js'

// Global message tracking to prevent spam
const messageHistory = new Map()
const MESSAGE_COOLDOWN = 2000 // 2 seconds

const preventMessageSpam = (message) => {
  const now = Date.now()
  const lastTime = messageHistory.get(message) || 0

  if (now - lastTime > MESSAGE_COOLDOWN) {
    messageHistory.set(message, now)
    return true
  }
  return false
}

export function useStatistics() {
  // State
  const loading = ref(false)
  const dailyStats = ref([])
  const overviewStats = ref({})
  const accountIPStats = ref({})
  const revenueStats = ref({})

  // Message helper with spam prevention
  const showMessage = (type, message) => {
    if (preventMessageSpam(message)) {
      ElMessage[type](message)
    } else {
      console.log('Message spam detected:', message)
    }
  }

  // Get daily statistics - specifically for daily endpoint
  const fetchDailyStatistics = async (params = {}) => {
    try {
      loading.value = true

      
      const response = await overviewApi.getDailyStatistics(params)
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        dailyStats.value = data?.daily_stats || []
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu thống kê')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching daily statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu thống kê hàng ngày'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Get weekly statistics
  const fetchWeeklyStatistics = async (params = {}) => {
    try {
      loading.value = true
      
      const response = await overviewApi.getWeeklyStatistics(params)
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu thống kê tuần')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching weekly statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu thống kê tuần'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Get monthly statistics
  const fetchMonthlyStatistics = async (params = {}) => {
    try {
      loading.value = true
      
      const response = await overviewApi.getMonthlyStatistics(params)
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu thống kê tháng')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching monthly statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu thống kê tháng'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Get overview statistics
  const fetchOverviewStatistics = async (params = {}) => {
    try {
      loading.value = true
      
      const response = await overviewApi.getOverviewStatistics(params)
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        overviewStats.value = data || {}
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu tổng quan')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching overview statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu tổng quan'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Get total overview statistics (all time)
  const fetchTotalOverviewStatistics = async () => {
    try {
      loading.value = true
      
      const response = await overviewApi.getTotalOverviewStatistics()
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu tổng quan')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching total overview statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu tổng quan'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Get account and IP statistics for charts
  const fetchAccountIPStatistics = async (params = {}) => {
    try {
      loading.value = true
      
      const response = await overviewApi.getAccountIPStatistics(params)
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        accountIPStats.value = data || {}
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu tài khoản và IP')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching account IP statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu tài khoản và IP'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Get revenue statistics for charts
  const fetchRevenueStatistics = async (params = {}) => {
    try {
      loading.value = true
      
      const response = await overviewApi.getRevenueStatistics(params)
      
      // Sử dụng helper để tránh truy cập nhiều cấp
      const { success, data, message } = extractApiResponse(response)
      
      if (success) {
        revenueStats.value = data || {}
        return {
          success: true,
          data,
          message
        }
      } else {
        showMessage('error', message || 'Lỗi khi tải dữ liệu doanh thu')
        return {
          success: false,
          data: null,
          message
        }
      }
    } catch (error) {
      console.error('Error fetching revenue statistics:', error)
      const errorMessage = error.message || 'Lỗi khi tải dữ liệu doanh thu'
      showMessage('error', errorMessage)
      return {
        success: false,
        data: null,
        message: errorMessage
      }
    } finally {
      loading.value = false
    }
  }

  // Utility function to format date for API
  const formatDateForAPI = (date) => {
    if (!date) return null
    if (typeof date === 'string') return date
    if (date instanceof Date) {
      return date.toISOString().split('T')[0]
    }
    return null
  }

  // Utility function to format chart data from API response
  const formatChartDataFromAPI = (apiData) => {
    if (!apiData || !apiData?.daily_stats) {
      return {
        dates: [],
        accounts: [],
        ips: [],
        cloneRatio: []
      }
    }

    const dates = []
    const accounts = []
    const ips = []
    const cloneRatio = []

    apiData.daily_stats.forEach(stat => {
      // Format date for display (DD/MM)
      const date = new Date(stat?.date)
      const dateLabel = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`
      
      dates.push(dateLabel)
      accounts.push(stat?.registered_accounts || 0)
      ips.push(stat?.registered_ips || 0)
      cloneRatio.push(stat?.clone_coefficient || 0)
    })

    return {
      dates,
      accounts,
      ips,
      cloneRatio
    }
  }

  // Utility function to format revenue chart data from API response
  const formatRevenueChartDataFromAPI = (apiData) => {
    if (!apiData || !apiData?.daily_revenue) {
      return {
        dates: [],
        revenues: [],
        totalRevenue: 0
      }
    }

    const dates = []
    const revenues = []

    apiData.daily_revenue.forEach(stat => {
      // Format date for display (DD/MM)
      const date = new Date(stat?.date)
      const dateLabel = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`
      
      dates.push(dateLabel)
      revenues.push(stat?.revenue || 0)
    })

    return {
      dates,
      revenues,
      totalRevenue: apiData?.total_revenue || 0
    }
  }

  // Date range presets for quick selection
  const getDateRangePresets = () => [
    {
      text: '7 ngày',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 7)
        return [start, end]
      }
    },
    {
      text: '30 ngày',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 30)
        return [start, end]
      }
    },
    {
      text: '60 ngày',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 60)
        return [start, end]
      }
    },
    {
      text: '90 ngày',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 90)
        return [start, end]
      }
    },
    {
      text: 'Tháng này',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(1)
        return [start, end]
      }
    },
    {
      text: 'Tháng trước',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setMonth(start.getMonth() - 1, 1)
        end.setDate(0)
        return [start, end]
      }
    }
  ]

  return {
    // State
    loading,
    dailyStats,
    overviewStats,
    accountIPStats,
    revenueStats,

    // API methods
    fetchDailyStatistics,
    fetchWeeklyStatistics,
    fetchMonthlyStatistics,
    fetchOverviewStatistics,
    fetchTotalOverviewStatistics,
    fetchAccountIPStatistics,
    fetchRevenueStatistics,

    // Utility functions
    formatDateForAPI,
    formatChartDataFromAPI,
    formatRevenueChartDataFromAPI,
    getDateRangePresets,
  }
}
