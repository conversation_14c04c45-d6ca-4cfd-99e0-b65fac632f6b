<template>
  <!-- Trang quản lý trang tĩnh -->
  <div class="p-6">
    <PageBreadcrumb :pageTitle="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý CMS', to: '/cms' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- <PERSON><PERSON> tìm kiếm + nút tạo -->
      <div class="mb-4 flex items-center justify-between gap-3">
        <el-input
          v-model="filters.search"
          placeholder="Tìm kiếm tiêu đề..."
          clearable
          class="search-input max-w-[500px]"
        >
          <template #prefix>
            <el-icon style="cursor: pointer"><Search /></el-icon>
          </template>
        </el-input>

        <ButtonCommon text="Tạo trang" type="primary" size="medium" :icon="PlusIcon" @click="handleCreate" />
      </div>

      <!-- <PERSON>ảng danh sách trang -->
      <div
        class="flex-1 overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
      >
        <el-table
          :data="pages"
          style="width: 100%"
          v-loading="loading"
          @sort-change="handleSortChange"
          :header-cell-style="getTableHeaderStyle"
        >
          <el-table-column prop="title" label="Tiêu đề" sortable min-width="200">
            <template #default="scope">
              <div class="flex items-center px-3 py-4">
                <span class="font-medium text-gray-900 dark:text-white">{{ scope.row.title }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="slug" label="Đường dẫn" sortable min-width="150" />
          <el-table-column prop="status" label="Trạng thái" align="center" width="150">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? 'Kích hoạt' : 'Vô hiệu' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="Cập nhật" sortable width="180">
            <template #default="scope">
              {{ formatDate(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="Người cập nhật" min-width="160">
            <template #default="scope">
              <div class="flex items-center">
                <el-avatar
                  v-if="scope.row.updated_by?.avatar"
                  :src="scope.row.updated_by.avatar"
                  :size="24"
                  class="mr-2"
                />
                <span>{{ scope.row.updated_by?.name || 'N/A' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="Hành động" width="180" align="center">
            <template #default="scope">
              <ActionButtons :show-view="false" @edit="handleEdit(scope.row)" @delete="handleDelete(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- Phân trang - Sử dụng component tái sử dụng -->
      <div class="mt-2">
        <Pagination v-model:pagination="pagination" @page-change="onChangePage" @per-page-change="onChangePerPage" />
      </div>
    </div>

    <!-- Modal tạo/sửa -->
    <StaticPageFormModal v-model:visible="modal.visible" :page="modal.page" @saved="onModalSaved" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { PlusIcon } from '@/components/icons/index.js'
import { SettingsIcon, TrashIcon } from '@/components/icons/index.js'
import StaticPageFormModal from '@/components/modules/cms/static/StaticPageFormModal.vue'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import Pagination from '@/components/common/Pagination.vue'
import { useStaticPage } from '@/composables/modules/cms'
import ButtonCommon from '~/components/common/ButtonCommon.vue'
import ActionButtons from '~/components/common/ActionButtons.vue'

// State
const currentPageTitle = ref('Quản lý trang tĩnh')
const { loading, pagination, filters, sort, fetchStaticPages, filterStaticPagesByTitle, deleteStaticPage } =
  useStaticPage()

// Modal state
const modal = reactive({ visible: false, page: null })

// Computed property - lọc theo title (realtime)
const pages = computed(() => {
  return filterStaticPagesByTitle(filters.search)
})

// Format ngày
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Fetch danh sách trang tĩnh với pagination (không filter qua API)
const fetchPages = async () => {
  const params = {
    page: pagination.current_page,
    limit: pagination.per_page,
  }
  await fetchStaticPages(params)
}

// Đổi số lượng/trang
const onChangePerPage = (perPage) => {
  pagination.per_page = perPage
  fetchPages()
}

// Chuyển trang
const onChangePage = (page) => {
  pagination.current_page = page
  fetchPages()
}

// Đổi sort
const handleSortChange = (s) => {
  sort.prop = s.prop
  sort.order = s.order === 'ascending' ? 'asc' : 'desc'
  pagination.current_page = 1
  fetchPages()
}

// Khi search thay đổi, reset về trang 1
watch(
  () => filters.search,
  () => {
    pagination.current_page = 1
  },
  { immediate: true },
)

const handleCreate = () => {
  modal.page = null
  modal.visible = true
}

const handleEdit = (page) => {
  modal.page = { ...page }
  modal.visible = true
}

const handleDelete = (page) => {
  ElMessageBox.confirm(`Xoá trang "${page.title}"?`, 'Xác nhận', { type: 'warning' })
    .then(async () => {
      await deleteStaticPage(page.id)
      fetchPages()
    })
    .catch(() => null)
}

const onModalSaved = () => {
  fetchPages()
}

// Chỉ gọi API 1 lần khi mounted để lấy danh sách
onMounted(fetchPages)

// Enhanced table styles based on existing patterns
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}
</script>

<style scoped>
/* Styles có thể được thêm vào đây nếu cần */
.search-input :deep(.el-input__wrapper) {
  height: 38px;
  display: flex;
  align-items: center;
}

.search-input :deep(.el-input__inner) {
  height: 100%;
}
</style>
