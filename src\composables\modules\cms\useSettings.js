import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { settingsApi } from '@/utils/apis/index.js'
import { 
  isProtectedSetting, 
  getProtectedSettingDeletionMessage 
} from '@/utils/configs/protected-settings.config.js'

export function useSettings() {
  // State
  const loading = ref(false)
  const settings = ref({})
  const currentSetting = ref(null)
  const pagination = ref({
    current_page: 1,
    per_page: 50,
    total: 0,
    last_page: 1,
    from: 0,
    to: 0,
  })
  const originalSettings = ref({})
  const searchParams = reactive({
    search: '',
    group: '',
    type: '',
    page: 1,
    per_page: 50,
  })

  // Methods
  const fetchSettings = async (params = {}) => {
    try {
      loading.value = true
      const mergedParams = { ...searchParams, ...params }
      const response = await settingsApi.getSettings(mergedParams)

      if (response.data.success) {
        const settingsData = response.data.data.data || []
        pagination.value = response.data.data.pagination || { total: 0 }

        // Group settings by group for better organization
        const groupedSettings = {}
        settingsData.forEach((setting) => {
          const group = setting.group || 'general'
          if (!groupedSettings[group]) {
            groupedSettings[group] = []
          }
          groupedSettings[group].push(setting)
        })

        settings.value = groupedSettings
        originalSettings.value = JSON.parse(JSON.stringify(groupedSettings))

        return response.data
      } else {
        throw new Error(response.data.message || 'Không thể tải danh sách cài đặt')
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra khi tải cấu hình'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSettings = async (settingsToUpdate) => {
    try {
      loading.value = true
      const response = await settingsApi.updateSettings(settingsToUpdate)

      if (response.data.success) {
        await fetchSettings()
        ElMessage.success(response.data.message || 'Cập nhật cấu hình thành công')
        return response.data
      } else {
        throw new Error(response.data.message || 'Không thể cập nhật cài đặt')
      }
    } catch (error) {
      console.error('Error updating settings:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật cấu hình'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createSetting = async (settingData) => {
    try {
      loading.value = true
      const response = await settingsApi.createSetting(settingData)

      if (response.data.success) {
        await fetchSettings()
        ElMessage.success(response.data.message || 'Thêm cấu hình thành công')
        return response.data.data
      } else {
        // If response is not successful, check for detailed errors first
        if (response.data.errors) {
          const errors = response.data.errors
          const errorMessages = Object.values(errors).flat()
          throw new Error(errorMessages.join(', '))
        } else {
          throw new Error(response.data.message || 'Không thể tạo cài đặt')
        }
      }
    } catch (error) {
      console.error('Error creating setting:', error)

      // Handle validation errors - prioritize detailed error messages
      if (error.errors) {
        // Check if error has errors property directly (from axios interceptor or custom error)
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.errors) {
        // Handle validation errors from response
        const errors = error.response.data.errors
        const errorMessages = Object.values(errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else {
        const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra khi thêm cấu hình'
        ElMessage.error(errorMessage)
      }
      throw error
    } finally {
      loading.value = false
    }
  }

  const getSettingById = async (id) => {
    try {
      loading.value = true
      const response = await settingsApi.getSettingById(id)

      if (response.data.success) {
        currentSetting.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Không thể tải thông tin cài đặt')
      }
    } catch (error) {
      console.error('Error fetching setting:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Không thể tải thông tin cài đặt'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSetting = async (id, settingData) => {
    try {
      loading.value = true
      const response = await settingsApi.updateSetting(id, settingData)

      if (response.data.success) {
        await fetchSettings()
        ElMessage.success(response.data.message || 'Cập nhật cấu hình thành công')
        return response.data.data
      } else {
        // If response is not successful, check for detailed errors first
        if (response.data.errors) {
          const errors = response.data.errors
          const errorMessages = Object.values(errors).flat()
          throw new Error(errorMessages.join(', '))
        } else {
          throw new Error(response.data.message || 'Không thể cập nhật cài đặt')
        }
      }
    } catch (error) {
      console.error('Error updating setting:', error)

      // Handle validation errors - prioritize detailed error messages
      if (error.errors) {
        // Check if error has errors property directly (from axios interceptor or custom error)
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.errors) {
        // Handle validation errors from response
        const errors = error.response.data.errors
        const errorMessages = Object.values(errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else {
        const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật cấu hình'
        ElMessage.error(errorMessage)
      }
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteSetting = async (id) => {
    try {
      // Tìm setting để kiểm tra bảo vệ
      let settingToDelete = null
      Object.keys(settings.value).forEach((group) => {
        const setting = settings.value[group].find((s) => s.id === id)
        if (setting) {
          settingToDelete = setting
        }
      })

      if (!settingToDelete) {
        throw new Error('Không tìm thấy cài đặt')
      }

      // Kiểm tra xem setting có được bảo vệ không (sử dụng cả key và name)
      if (isProtectedSetting(settingToDelete.key, settingToDelete.name)) {
        const errorMessage = getProtectedSettingDeletionMessage(settingToDelete.key)
        ElMessage.error(errorMessage)
        return false
      }

      await ElMessageBox.confirm(
        'Bạn có chắc chắn muốn xóa cài đặt này? Hành động này không thể hoàn tác.',
        'Xác nhận xóa',
        {
          confirmButtonText: 'Xóa',
          cancelButtonText: 'Hủy',
          type: 'warning',
        },
      )

      loading.value = true
      const response = await settingsApi.deleteSetting(id)

      if (response.data.success) {
        await fetchSettings()
        ElMessage.success(response.data.message || 'Xóa cài đặt thành công')
        return response.data
      } else {
        throw new Error(response.data.message || 'Không thể xóa cài đặt')
      }
    } catch (error) {
      if (error === 'cancel') {
        return // User cancelled
      }
      console.error('Error deleting setting:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra khi xóa cấu hình'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  // Search and filter
  const searchSettings = async (searchTerm) => {
    searchParams.search = searchTerm
    await fetchSettings()
  }

  const filterByGroup = async (group) => {
    searchParams.group = group
    await fetchSettings()
  }

  const filterByType = async (type) => {
    searchParams.type = type
    await fetchSettings()
  }

  const resetFilters = async () => {
    searchParams.search = ''
    searchParams.group = ''
    searchParams.type = ''
    searchParams.page = 1
    await fetchSettings()
  }

  // Helper functions
  const getChangedSettings = () => {
    const changes = []

    Object.keys(settings.value).forEach((group) => {
      if (settings.value[group] && originalSettings.value[group]) {
        settings.value[group].forEach((setting) => {
          const original = originalSettings.value[group].find((s) => s.key === setting.key)
          if (original && original.value !== setting.value) {
            changes.push({
              id: setting.id,
              key: setting.key,
              value: setting.value,
              name: setting.name,
              type: setting.type,
              group: group,
            })
          }
        })
      }
    })

    return changes
  }

  const hasChanges = () => {
    return getChangedSettings().length > 0
  }

  const resetChanges = () => {
    settings.value = JSON.parse(JSON.stringify(originalSettings.value))
  }

  const handleSettingChange = (settingKey, newValue) => {
    // Find and update the setting in the current state
    Object.keys(settings.value).forEach((group) => {
      const setting = settings.value[group].find((s) => s.key === settingKey)
      if (setting) {
        setting.value = newValue
      }
    })
  }

  const getAvailableGroups = () => {
    return Object.keys(settings.value)
  }

  const getTypeOptions = () => [
    { value: '', label: 'Tất cả loại' },
    { value: 'text', label: 'Text' },
    { value: 'textarea', label: 'Textarea' },
    { value: 'number', label: 'Number' },
    { value: 'image', label: 'Image' },
    { value: 'file', label: 'File' },
    { value: 'select', label: 'Select' },
    { value: 'checkbox', label: 'Checkbox' },
    { value: 'radio', label: 'Radio' },
  ]

  return {
    // State
    loading,
    settings,
    currentSetting,
    pagination,
    searchParams,

    // Methods
    fetchSettings,
    createSetting,
    getSettingById,
    updateSetting,
    deleteSetting,

    updateSettings,

    // Search and filter
    searchSettings,
    filterByGroup,
    filterByType,
    resetFilters,

    // Helpers
    getChangedSettings,
    hasChanges,
    resetChanges,

    handleSettingChange,
    getAvailableGroups,
    getTypeOptions,
  }
}
