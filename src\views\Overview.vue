<template>
  <div class="overview-wrapper p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-800">Tổng quan hệ thống</h1>
      <p class="mt-1 text-gray-500">Chào mừng trở lại, Sếp!</p>
    </div>

    <!-- Tổng quan chung (All time) -->
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <!-- Tổng tài khoản -->
      <el-card shadow="hover" class="overview-box total-overview">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-base text-gray-700 font-bold mb-2 highlight-title">Tổng tài khoản</div>
            <div class="text-3xl font-bold text-blue-600">{{ totalOverviewData.totalAccounts || 0 }}</div>
          </div>
          <div class="text-blue-500 opacity-60">
            <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <div class="text-xs text-gray-400 mt-2">Từ khi bắt đầu hoạt động</div>
      </el-card>

      <!-- Tổng IP -->
      <el-card shadow="hover" class="overview-box total-overview">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-base text-gray-700 font-bold mb-2 highlight-title">Tổng IP đăng ký</div>
            <div class="text-3xl font-bold text-yellow-600">{{ totalOverviewData.totalIPs || 0 }}</div>
          </div>
          <div class="text-yellow-500 opacity-60">
            <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
        </div>
        <div class="text-xs text-gray-400 mt-2">Địa chỉ IP duy nhất</div>
      </el-card>

      <!-- Tổng doanh thu -->
      <el-card shadow="hover" class="overview-box total-overview">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-base text-gray-700 font-bold mb-2 highlight-title">Tổng doanh thu</div>
            <div class="text-3xl font-bold text-green-600">{{ formatCurrency(totalOverviewData.totalRevenue || 0) }}</div>
          </div>
          <div class="text-green-500 opacity-60">
            <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <div class="text-xs text-gray-400 mt-2">Tất cả giao dịch thành công</div>
      </el-card>

      <!-- Tổng giao dịch -->
      <el-card shadow="hover" class="overview-box total-overview">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-base text-gray-700 font-bold mb-2 highlight-title">Tổng giao dịch</div>
            <div class="text-3xl font-bold text-purple-600">{{ totalOverviewData.totalTransactions || 0 }}</div>
          </div>
          <div class="text-purple-500 opacity-60">
            <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
              <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        <div class="text-xs text-gray-400 mt-2">Giao dịch đã hoàn thành</div>
      </el-card>
    </div>

    <!-- Tiêu đề cho phần thống kê theo thời gian -->
    <div class="mb-4">
      <h2 class="text-xl font-semibold text-gray-700">Thống kê theo thời gian</h2>
    </div>

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Tài khoản đăng ký -->
      <el-card shadow="hover" class="overview-box">
        <div class="flex justify-between items-start mb-4">
          <div class="text-base text-gray-700 font-bold highlight-title">Số tài khoản đăng ký</div>
          <el-select v-model="usersTimeRange" @change="handleUsersAnalyticsChange" size="small" style="width: 120px" class="time-selector">
            <el-option 
              v-for="option in timeRangeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </div>
        <div class="text-2xl font-bold text-blue-600 mb-2 text-right">{{ analyticsData.users.current }}</div>
        <div class="flex items-center text-sm justify-end" :class="getGrowthClass(analyticsData.users.growth)">
          <el-tooltip :content="getTooltipContent(analyticsData.users, 'users')" placement="top">
            <component :is="getGrowthIcon(analyticsData.users.growth)" class="mr-1 w-4 h-4 cursor-pointer" />
          </el-tooltip>
          <span>{{ formatGrowthRate(analyticsData.users.growth) }}%</span>
        </div>
      </el-card>

      <!-- IP đăng ký -->
      <el-card shadow="hover" class="overview-box">
        <div class="flex justify-between items-start mb-4">
          <div class="text-base text-gray-700 font-bold highlight-title">Số IP đăng ký</div>
          <el-select v-model="ipsTimeRange" @change="handleIpsAnalyticsChange" size="small" style="width: 120px" class="time-selector">
            <el-option 
              v-for="option in timeRangeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </div>
        <div class="text-2xl font-bold text-yellow-600 mb-2 text-right">{{ analyticsData.ips.current }}</div>
        <div class="flex items-center text-sm justify-end" :class="getGrowthClass(analyticsData.ips.growth)">
          <el-tooltip :content="getTooltipContent(analyticsData.ips, 'ips')" placement="top">
            <component :is="getGrowthIcon(analyticsData.ips.growth)" class="mr-1 w-4 h-4 cursor-pointer" />
          </el-tooltip>
          <span>{{ formatGrowthRate(analyticsData.ips.growth) }}%</span>
        </div>
      </el-card>

      <!-- Doanh thu -->
      <el-card shadow="hover" class="overview-box">
        <div class="flex justify-between items-start mb-4">
          <div class="text-base text-gray-700 font-bold highlight-title">Doanh thu</div>
          <el-select v-model="revenueTimeRange" @change="handleRevenueAnalyticsChange" size="small" style="width: 120px" class="time-selector">
            <el-option 
              v-for="option in timeRangeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </div>
        <div class="text-2xl font-bold text-green-600 mb-2 text-right">{{ formatCurrency(analyticsData.revenue.current) }}</div>
        <div class="flex items-center text-sm justify-end" :class="getGrowthClass(analyticsData.revenue.growth)">
          <el-tooltip :content="getTooltipContent(analyticsData.revenue, 'revenue')" placement="top">
            <component :is="getGrowthIcon(analyticsData.revenue.growth)" class="mr-1 w-4 h-4 cursor-pointer" />
          </el-tooltip>
          <span>{{ formatGrowthRate(analyticsData.revenue.growth) }}%</span>
        </div>
      </el-card>

      <!-- Giao dịch -->
      <el-card shadow="hover" class="overview-box">
        <div class="flex justify-between items-start mb-4">
          <div class="text-base text-gray-700 font-bold highlight-title">Giao dịch thành công</div>
          <el-select v-model="transactionsTimeRange" @change="handleTransactionsAnalyticsChange" size="small" style="width: 120px" class="time-selector">
            <el-option 
              v-for="option in timeRangeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </div>
        <div class="text-2xl font-bold text-purple-600 mb-2 text-right">{{ analyticsData.transactions.current }}</div>
        <div class="flex items-center text-sm justify-end" :class="getGrowthClass(analyticsData.transactions.growth)">
          <el-tooltip :content="getTooltipContent(analyticsData.transactions, 'transactions')" placement="top">
            <component :is="getGrowthIcon(analyticsData.transactions.growth)" class="mr-1 w-4 h-4 cursor-pointer" />
          </el-tooltip>
          <span>{{ formatGrowthRate(analyticsData.transactions.growth) }}%</span>
        </div>
      </el-card>

    </div>

    <!-- Biểu đồ thống kê tài khoản và IP -->
    <div class="mt-8">
      <AccountIPChart height="500px" />
    </div>
    <div class="mt-8">
      <RevenueChart height="500px" />
    </div>

    <!-- Top Tables Section -->
    <div class="mt-8">
      <TopTablesSection />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElTooltip } from 'element-plus'
import { TrendingUpIcon, TrendingDownIcon, TrendingFlatIcon } from '@/components/icons/index.js'
import { AccountIPChart, TopTablesSection } from '@/components/modules/analytics/index.js'
import RevenueChart from '@/components/modules/analytics/RevenueChart.vue'
import { useOverview } from '@/composables/useOverview.js'
import { useOverviewAnalytics } from '@/composables/useOverviewAnalytics.js'
import { 
  TIME_PERIOD, 
  TIME_PERIOD_OPTIONS
} from '@/utils/configs/constant.config.js'

// Use composables
const { fetchOverview } = useOverview()
const { 
  analyticsData, 
  loadUsersAnalytics, 
  loadIpsAnalytics, 
  loadRevenueAnalytics, 
  loadTransactionsAnalytics,
  loadAllAnalytics 
} = useOverviewAnalytics()

// Time range options from constants
const timeRangeOptions = TIME_PERIOD_OPTIONS

// Total overview data (all time)
const totalOverviewData = ref({
  totalAccounts: 0,
  totalIPs: 0,
  totalRevenue: 0,
  totalTransactions: 0
})

// State riêng biệt cho từng box (using constants)
const usersTimeRange = ref(TIME_PERIOD.TODAY)
const ipsTimeRange = ref(TIME_PERIOD.TODAY)
const revenueTimeRange = ref(TIME_PERIOD.TODAY)
const transactionsTimeRange = ref(TIME_PERIOD.TODAY)

// Format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format growth rate
const formatGrowthRate = (rate) => {
  if (rate === null || rate === undefined || isNaN(rate)) return '0.0'
  return Math.abs(rate).toFixed(1)
}

// Get growth icon based on rate
const getGrowthIcon = (rate) => {
  if (rate > 0) return TrendingUpIcon
  if (rate < 0) return TrendingDownIcon
  return TrendingFlatIcon
}

// Get growth class based on rate
const getGrowthClass = (rate) => {
  if (rate > 0) return 'text-green-600'
  if (rate < 0) return 'text-red-600'
  return 'text-gray-500'
}

// Get tooltip content based on data type and time range
const getTooltipContent = (data, type) => {
  const timeRangeLabels = {
    [TIME_PERIOD.TODAY]: 'hôm qua',
    [TIME_PERIOD.YESTERDAY]: 'hôm kia',
    [TIME_PERIOD.LAST_7_DAYS]: '7 ngày trước đó',
    [TIME_PERIOD.LAST_30_DAYS]: '30 ngày trước đó',
    [TIME_PERIOD.THIS_MONTH]: 'tháng trước',
    [TIME_PERIOD.LAST_MONTH]: 'tháng trước đó'
  }
  
  const currentTimeRange = getCurrentTimeRange(type)
  const previousLabel = timeRangeLabels[currentTimeRange] || 'kỳ trước'
  
  let previousValue = data.previous
  let unit = ''
  
  if (type === 'revenue') {
    previousValue = formatCurrency(data.previous)
  } else {
    unit = type === 'users' || type === 'ips' || type === 'transactions' ? '' : ''
  }
  
  return `Giá trị ${previousLabel}: ${previousValue}${unit}`
}

// Get current time range for specific data type
const getCurrentTimeRange = (type) => {
  switch (type) {
    case 'users': return usersTimeRange.value
    case 'ips': return ipsTimeRange.value
    case 'revenue': return revenueTimeRange.value
    case 'transactions': return transactionsTimeRange.value
    default: return TIME_PERIOD.TODAY
  }
}

// Load analytics cho từng box riêng biệt (using composable)
const handleUsersAnalyticsChange = () => {
  loadUsersAnalytics(usersTimeRange.value)
}

const handleIpsAnalyticsChange = () => {
  loadIpsAnalytics(ipsTimeRange.value)
}

const handleRevenueAnalyticsChange = () => {
  loadRevenueAnalytics(revenueTimeRange.value)
}

const handleTransactionsAnalyticsChange = () => {
  loadTransactionsAnalytics(transactionsTimeRange.value)
}

// Load tất cả dữ liệu ban đầu
const loadInitialAnalytics = () => {
  loadAllAnalytics({
    users: usersTimeRange.value,
    ips: ipsTimeRange.value,
    revenue: revenueTimeRange.value,
    transactions: transactionsTimeRange.value
  })
}

// Load total overview data
const loadTotalOverview = async () => {
  try {
    const result = await fetchOverview()
    
    if (result.success && result.data) {
      totalOverviewData.value = {
          totalAccounts: result.data.totalAccounts || 0,
          totalIPs: result.data.totalUniqueIPs || 0,
          totalRevenue: result.data.totalRevenue || 0,
          totalTransactions: result.data.totalSuccessfulTransactions || 0
      }
    } else {
      
    }
  } catch (error) {
    console.error('Error loading total overview:', error)
  }
}


onMounted(() => {
  setTimeout(() => {
    loadTotalOverview() // Load total overview first
    loadInitialAnalytics()  // Then load time-based analytics
  }, 500)
})
</script>

<style lang="scss" scoped>
.overview-box {
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  min-height: 140px;
  
  // Dark mode support
  .dark & {
    background-color: #1f2937;
    border-color: #374151;
  }
}

.total-overview {
  min-height: 160px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  
  .dark & {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
}

.overview-box :deep(.el-card__body) {
  padding: 1rem;
}

// Time selector positioning
.time-selector {
  position: relative;
  top: -10px;
  right: -10px;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-60 {
  opacity: 0.6;
}

.mb-8 {
  margin-bottom: 2rem;
}

.font-bold {
  font-weight: 700;
}

.text-green-600 {
  color: #059669;
}

.text-red-600 {
  color: #dc2626;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-blue-600 {
  color: #2563eb;
}

.text-yellow-600 {
  color: #d97706;
}

.text-purple-600 {
  color: #9333ea;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

// Icon tăng trưởng styling
svg {
  transition: all 0.2s ease-in-out;
}

// SVG icons in overview cards
.total-overview {
  svg {
    transition: all 0.3s ease-in-out;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    
    &:hover {
      transform: scale(1.1);
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
    }
  }
}

.text-green-600 svg {
  transform: rotate(0deg);
}

.text-red-600 svg {
  transform: rotate(0deg);
}

.text-gray-500 svg {
  transform: rotate(0deg);
}

// El-select styling
:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      border-color: #9ca3af;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

// Dark mode support for text colors
.dark {
  .text-gray-800 {
    color: #f9fafb;
  }
  
  .text-gray-700 {
    color: #d1d5db;
  }
  
  .text-gray-500 {
    color: #9ca3af;
  }
  
  .text-gray-400 {
    color: #6b7280;
  }
  
  .text-blue-600 {
    color: #60a5fa;
  }
  
  .text-yellow-600 {
    color: #fbbf24;
  }
  
  .text-green-600 {
    color: #34d399;
  }
  
  .text-purple-600 {
    color: #a78bfa;
  }
  
  .text-red-600 {
    color: #f87171;
  }
}

</style>
