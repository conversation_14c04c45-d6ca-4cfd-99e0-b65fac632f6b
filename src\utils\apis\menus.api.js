import apiAxios from '@/utils/configs/axios.config.js'

const menusApi = {
  // Menu CRUD operations
  getMenus(params = {}) {
    return apiAxios({
      method: 'get',
      url: 'cms/menus',
      params,
    })
  },

  createMenu(menuData) {
    return apiAxios({
      method: 'post',
      url: 'cms/menus',
      data: menuData,
    })
  },

  getMenuById(id) {
    return apiAxios({
      method: 'get',
      url: `cms/menus/${id}`,
    })
  },

  updateMenu(id, menuData) {
    return apiAxios({
      method: 'put',
      url: `cms/menus/${id}`,
      data: menuData,
    })
  },

  deleteMenu(id) {
    return apiAxios({
      method: 'delete',
      url: `cms/menus/${id}`,
    })
  },

  // Get menu with its items (includes all menu items in tree structure)
  getMenuWithItems(id) {
    return apiAxios({
      method: 'get',
      url: `cms/menus/${id}`,
    })
  },

  // Menu Item CRUD operations - Updated to use new API endpoints
  getMenuItems(menuId, params = {}) {
    const cleanedParams = {}
    Object.keys(params).forEach((key) => {
      if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
        cleanedParams[key] = params[key]
      }
    })

    return apiAxios({
      method: 'get',
      url: `cms/menus/${menuId}/items`,
      params: cleanedParams,
    })
  },

  createMenuItem(menuId, itemData) {

    // Nếu có file meta_image thì gửi form-data
    if (itemData && itemData.meta_image instanceof File) {
      const formData = new FormData()
      Object.entries(itemData).forEach(([key, value]) => {
        if (key === 'meta_image' && value instanceof File) {
          formData.append('meta_image', value)
        } else {
          formData.append(key, value)
        }
      })
      return apiAxios({
        method: 'post',
        url: `cms/menus/${menuId}/items`,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // Không có file, gửi JSON như cũ
      return apiAxios({
        method: 'post',
        url: `cms/menus/${menuId}/items`,
        data: itemData,
      })
    }
  },

  getMenuItemById(itemId, menuId) {
    return apiAxios({
      method: 'get',
      url: `cms/menus/${menuId}/items/${itemId}`,
    })
  },

  updateMenuItem(itemId, menuId, itemData) {
    // Nếu có file meta_image thì gửi form-data với method spoofing
    if (itemData && itemData.meta_image instanceof File) {
      const formData = new FormData()
      Object.entries(itemData).forEach(([key, value]) => {
        if (key === 'meta_image' && value instanceof File) {
          formData.append('meta_image', value)
        } else {
          formData.append(key, value)
        }
      })
      formData.append('_method', 'PUT') // Method spoofing
      return apiAxios({
        method: 'post', // Gửi POST thay vì PUT
        url: `cms/menus/${menuId}/items/${itemId}`,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // Không có file, gửi JSON như cũ
      return apiAxios({
        method: 'put',
        url: `cms/menus/${menuId}/items/${itemId}`,
        data: itemData,
      })
    }
  },

  deleteMenuItem(itemId, menuId) {
    return apiAxios({
      method: 'delete',
      url: `cms/menus/${menuId}/items/${itemId}`,
    })
  },

  // Get linkable data for menu items
  getDataLinkables(type) {
    const url = 'cms/data-linkables'
    const requestParams = { type }
       
    return apiAxios({
      method: 'get',
      url: url,
      params: requestParams,
    })
  },
}

export default menusApi
