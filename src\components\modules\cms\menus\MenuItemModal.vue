<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Sửa Menu Item' : 'Thêm Menu Item'"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="menu-item-modal"
  >
    <div class="modal-content">
      <MenuItemForm
        :item="item"
        :menu-id="menuId"
        :menu-items="menuItems"
        :current-menu="currentMenu"
        :linkable-types="linkableTypes"
        :create-menu-item="createMenuItem"
        :update-menu-item="updateMenuItem"
        :loading="loading"
        @success="handleSuccess"
        @cancel="handleCancel"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import MenuItemForm from './MenuItemForm.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { useFormValidation } from '@/composables/useFormValidation.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: null,
  },
  menuId: {
    type: [Number, String],
    required: true,
  },
  menuItems: {
    type: Array,
    default: () => [],
  },
  currentMenu: {
    type: Object,
    default: null,
  },
  linkableTypes: {
    type: Array,
    default: () => [],
  },
  createMenuItem: {
    type: Function,
    required: true,
  },
  updateMenuItem: {
    type: Function,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Form validation composable (có thể truyền formRef cho MenuItemForm nếu muốn dùng chung validate)
const { formRef, clearAllValidation, validateForm, handleInputChange, handleFieldBlur, autoGenerateField } = useFormValidation()

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isEdit = computed(() => !!props.item)

// Methods
const handleClose = () => {
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('update:visible', false)
}

const handleSuccess = () => {
  emit('success')
  emit('update:visible', false)
}
</script>

<style scoped>
.menu-item-modal :deep(.el-dialog__body) {
  padding: 0;
}

.modal-content {
  padding: 20px;
}
</style>
