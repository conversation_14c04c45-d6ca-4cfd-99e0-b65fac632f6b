<template>
  <div class="space-y-6 p-6">
    <h2 class="text-2xl font-bold">ButtonCommon Tooltip Examples</h2>

    <!-- Icon-only buttons with tooltip -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Icon-only Buttons với Tooltip</h3>

      <div class="flex gap-3">
        <!-- Edit button -->
        <ButtonCommon :icon="EditIcon" type="info" size="small" rounded tooltip="Chỉnh sửa" />

        <!-- Delete button -->
        <ButtonCommon :icon="TrashIcon" type="danger" size="small" rounded tooltip="Xóa" />

        <!-- View button -->
        <ButtonCommon :icon="EyeIcon" type="default" size="small" rounded tooltip="Xem chi tiết" />

        <!-- Add button -->
        <ButtonCommon :icon="PlusIcon" type="primary" size="small" rounded tooltip="Thêm mới" />
      </div>
    </div>

    <!-- Buttons với text (không có tooltip) -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Buttons với Text (không tooltip)</h3>

      <div class="flex gap-3">
        <!-- Button có cả icon và text -->
        <ButtonCommon :icon="EditIcon" type="info" size="small" text="Sửa" />

        <ButtonCommon :icon="TrashIcon" type="danger" size="small" text="Xóa" />
      </div>
    </div>

    <!-- Loading states -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Loading States (tooltip tắt khi loading)</h3>

      <div class="flex gap-3">
        <ButtonCommon :icon="EditIcon" type="info" size="small" rounded tooltip="Đang tải..." :loading="true" />

        <ButtonCommon
          :icon="TrashIcon"
          type="danger"
          size="small"
          rounded
          tooltip="Xóa"
          :loading="false"
          @click="handleDelete"
        />
      </div>
    </div>

    <!-- Disabled states -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Disabled States (tooltip tắt khi disabled)</h3>

      <div class="flex gap-3">
        <ButtonCommon
          :icon="EditIcon"
          type="info"
          size="small"
          rounded
          tooltip="Không thể chỉnh sửa"
          :disabled="true"
        />

        <ButtonCommon :icon="TrashIcon" type="danger" size="small" rounded tooltip="Xóa" :disabled="false" />
      </div>
    </div>
  </div>
</template>

<script setup>
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { EditIcon, TrashIcon, EyeIcon, PlusIcon } from '@/components/icons/index.js'
</script>

<style scoped>
/* Styling cho demo */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
