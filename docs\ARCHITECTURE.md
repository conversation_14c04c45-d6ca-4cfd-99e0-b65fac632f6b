# 🏗️ Kiến trúc dự án & Quy tắc phát triển

## 📋 Mục lục

1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON><PERSON> trú<PERSON> thư mục](#cấu-trúc-thư-mục)
3. [<PERSON><PERSON><PERSON> trúc Component](#kiến-trúc-component)
4. [Pattern Composables](#pattern-composables)
5. [Quy tắc đặt tên](#quy-tắc-đặt-tên)
6. [Tổ chức code](#tổ-chức-code)
7. [Xử lý lỗi](#xử-lý-lỗi)
8. [Hướng dẫn Testing](#hướng-dẫn-testing)
9. [Best Practices](#best-practices)
10. [Ví dụ & Templates](#ví-dụ--templates)

---

## 🎯 Tổng quan

Tài liệu này định nghĩa các pattern kiến trúc, chuẩn coding và quy tắc phát triển cho ứng dụng Vue.js admin. Tất cả developer ph<PERSON><PERSON> tuân thủ các hướng dẫn này để đảm bả<PERSON> t<PERSON>h nh<PERSON>t quán, khả năng bảo trì và mở rộng.

### Nguyên tắc cốt lõi

- **Tách biệt mối quan tâm (Separation of Concerns)**: UI components chỉ xử lý presentation, composables xử lý business logic
- **Thống nhất đặt tên (Unified Naming)**: Tên trường khớp chính xác với backend (không có lớp mapping)
- **Components tái sử dụng**: Tạo generic components cho các pattern phổ biến
- **Testing khuyến khích**: Nên có test coverage cho composables quan trọng (optional)
- **Patterns nhất quán**: Sử dụng patterns đã thiết lập cho tất cả features

---

## 📁 Cấu trúc thư mục

```
src/
├── components/
│   ├── common/          # UI components có thể tái sử dụng
│   │   ├── Modal.vue
│   │   ├── FormField.vue
│   │   ├── PasswordInput.vue
│   │   ├── AvatarUpload.vue
│   │   └── ...
│   ├── pages/           # Components dành riêng cho từng page
│   │   ├── profile/
│   │   ├── users/
│   │   └── ...
│   └── modules/         # Components dành riêng cho từng tính năng
│       ├── users/
│       ├── cms/
│       └── ...
├── composables/         # Business logic & quản lý state
│   ├── modules/
│   │   ├── users/
│   │   │   ├── index.js
│   │   │   ├── useUsers.js
│   │   │   ├── useUsers.test.js
│   │   │   └── ...
│   │   └── cms/
│   ├── useChangePassword.js
│   ├── useUpdateProfile.js
│   └── ...
├── services/            # API services
│   ├── api/
│   └── modules/
├── utils/               # Utility functions
│   ├── apis/
│   ├── configs/
│   └── helpers/
└── views/               # Route components
    ├── Auth/
    ├── modules/
    └── ...
```

---

## 🧩 Kiến trúc Component

### 1. Phân loại Components

#### **Common Components** (`src/components/common/`)

**Mục đích**: UI components có thể tái sử dụng, không chứa business logic.

**Nhiệm vụ**:

- Xử lý presentation logic
- Nhận props để cấu hình
- Emit events cho interactions
- Độc lập với business domain

**Ví dụ**: `FormField`, `Modal`, `PasswordInput`, `AvatarUpload`

#### **Page Components** (`src/components/pages/`)

**Mục đích**: Components dành riêng cho từng page, sử dụng composables cho business logic.

**Nhiệm vụ**:

- Import và sử dụng composables
- Xử lý UI state và interactions
- Kết nối UI với business logic
- Sử dụng common components

#### **Module Components** (`src/components/modules/`)

**Mục đích**: Components dành riêng cho từng tính năng với domain knowledge.

**Nhiệm vụ**:

- Có thể chứa business logic đơn giản
- Nên sử dụng composables cho logic phức tạp
- Specific cho domain cụ thể

### 2. Cấu trúc Component chuẩn

👉 **Xem template đầy đủ**: [ComponentTemplate.vue](./examples/ComponentTemplate.vue)

**Cấu trúc scripts**:

```javascript
<script setup>
// 1. Imports
import { computed, watch, nextTick } from 'vue';

// 2. Props & Emits
const props = defineProps({...});
const emit = defineEmits([...]);

// 3. Composables
const { formData, errors, submitForm } = useFeature();

// 4. Computed properties
const isVisible = computed(() => ...);

// 5. Methods
const handleSubmit = () => ...;

// 6. Watchers
watch(() => props.value, (newValue) => ...);
</script>
```

---

## 🔧 Pattern Composables

### 1. Cấu trúc Composable chuẩn

**Mục đích**: Tách biệt business logic khỏi UI components.

**Nhiệm vụ**:

- Quản lý form state và validation
- Xử lý API calls
- Error handling
- Business logic calculations

👉 **Xem template đầy đủ**: [ComposableTemplate.js](./examples/ComposableTemplate.js)

**Cấu trúc cơ bản**:

```javascript
// Form field constants - thống nhất với backend
export const FORM_FIELDS = {
  FIELD_NAME: 'field_name'  // Tên trường khớp backend
};

export const useFeatureName = () => {
  // State
  const loading = ref(false);
  const formData = reactive({
    [FORM_FIELDS.FIELD_NAME]: ''
  });
  const errors = reactive({
    [FORM_FIELDS.FIELD_NAME]: ''
  });

  // Computed
  const canSubmit = computed(() => ...);

  // Methods
  const resetForm = () => ...;
  const validateForm = async () => ...;
  const submitForm = async () => ...;

  return {
    // State
    loading, formData, errors,
    // Computed
    canSubmit,
    // Methods
    resetForm, validateForm, submitForm,
    // Constants
    FORM_FIELDS
  };
};
```

### 2. Phân công nhiệm vụ rõ ràng

#### **Composable chịu trách nhiệm**:

- ✅ Form state management
- ✅ Validation logic
- ✅ API calls
- ✅ Error handling
- ✅ Business calculations
- ✅ Data transformations

#### **Component chịu trách nhiệm**:

- ✅ UI rendering
- ✅ Event handling
- ✅ User interactions
- ✅ Modal/dialog management
- ✅ Loading states display

---

## 🏷️ Quy tắc đặt tên

### 1. Thống nhất Field Names

**Quy tắc quan trọng nhất**: Tên trường frontend PHẢI khớp chính xác với backend - không có lớp mapping.

```javascript
// ✅ ĐÚNG - Tên trường khớp backend
export const FORM_FIELDS = {
  OLD_PASSWORD: 'old_password',
  NEW_PASSWORD: 'new_password',
  NEW_PASSWORD_CONFIRMATION: 'new_password_confirmation',
}

// ❌ SAI - Tên trường khác backend, cần mapping
export const FORM_FIELDS = {
  CURRENT_PASSWORD: 'current_password',
  NEW_PASSWORD: 'new_password',
  CONFIRM_PASSWORD: 'confirm_password',
}
```

### 2. Đặt tên Files

- **Components**: `PascalCase.vue`
- **Composables**: `camelCase.js`
- **Tests**: `camelCase.test.js`
- **Utilities**: `camelCase.helper.js`

### 3. Đặt tên Variables

```javascript
// ✅ ĐÚNG
const isVisible = ref(false);
const hasChanges = computed(() => ...);
const canSubmit = computed(() => ...);
const handleSubmit = async () => ...;

// ❌ SAI
const visible = ref(false);
const changes = computed(() => ...);
const submit = computed(() => ...);
const onSubmit = async () => ...;
```

---

## 📝 Tổ chức code

### 1. Tổ chức Imports

```javascript
// 1. Vue imports
import { ref, reactive, computed, watch } from 'vue'

// 2. Third-party imports
import { ElMessage, ElButton } from 'element-plus'
import * as yup from 'yup'

// 3. Common components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'

// 4. Composables
import { useFeatureComposable } from '@/composables/useFeatureComposable.js'

// 5. Services & Utils
import apiService from '@/services/api/feature.api.js'
import { validate } from '@/utils/helpers/validate.helper.js'
```

### 2. Tổ chức Script sections

**Thứ tự bắt buộc**:

1. Imports
2. Props & Emits
3. Composables
4. Local state
5. Computed properties
6. Methods
7. Watchers
8. Lifecycle hooks

---

## 🚨 Xử lý lỗi

### 1. Client-side Validation

```javascript
// Schema validation với yup
const validationSchema = yup.object().shape({
  [FORM_FIELDS.EMAIL]: yup.string().required('Email là bắt buộc').email('Email không đúng định dạng'),
})
```

### 2. Server Error Handling

```javascript
try {
  const response = await apiService.submitData(formData)
  return { success: true, data: response }
} catch (error) {
  // Xử lý validation errors (422)
  if (error.status === 422 && error.data?.errors) {
    Object.keys(error.data.errors).forEach((field) => {
      if (errors.hasOwnProperty(field)) {
        errors[field] = error.data.errors[field][0]
      }
    })
    return { success: false, message: 'Dữ liệu không hợp lệ' }
  }

  // Xử lý lỗi khác
  const errorMessage = error.message || 'Có lỗi xảy ra'
  ElMessage.error(errorMessage)
  return { success: false, message: errorMessage }
}
```

---

## 🧪 Hướng dẫn Testing

### 1. Yêu cầu Testing

**Khuyến khích (Optional)**:

- Nên có test coverage cho composables quan trọng
- Ưu tiên test các business logic phức tạp
- Bao gồm edge cases và error scenarios cho các tính năng critical
- Mock đúng external dependencies

👉 **Xem template đầy đủ**: [TestTemplate.test.js](./examples/TestTemplate.test.js)

### 2. Cấu trúc Test

```javascript
describe('useFeatureName', () => {
  describe('Khởi tạo (initialization)', () => {
    it('nên khởi tạo với giá trị mặc định đúng', () => {
      // Test implementation
    })
  })

  describe('Validation form', () => {
    it('nên validate các trường bắt buộc', async () => {
      // Test implementation
    })
  })

  describe('Form submission', () => {
    it('nên submit form thành công', async () => {
      // Test implementation
    })

    it('nên xử lý API errors', async () => {
      // Test implementation
    })
  })
})
```

---

## 🎯 Best Practices

### 1. Performance

- Sử dụng `computed` cho derived state
- Tránh reactivity không cần thiết
- Sử dụng `nextTick` cho DOM updates
- Debounce các operations tốn kém

### 2. Maintainability

- Giữ components nhỏ và tập trung
- Sử dụng JSDoc comments
- Tách reusable logic thành composables
- Tuân thủ patterns nhất quán

### 3. Security

- Validate tất cả user inputs
- Sanitize dữ liệu trước khi gọi API
- Xử lý dữ liệu nhạy cảm đúng cách
- Sử dụng HTTPS cho tất cả API calls

---

## 📚 Ví dụ & Templates

### 🎯 Templates (Mẫu chuẩn)

- [ComponentTemplate.vue](./examples/ComponentTemplate.vue) - Template component chuẩn
- [ComposableTemplate.js](./examples/ComposableTemplate.js) - Template composable chuẩn
- [TestTemplate.test.js](./examples/TestTemplate.test.js) - Template test chuẩn

### 🏗️ Real Examples (Ví dụ thực tế)

- [UserFormExample.vue](./examples/UserFormExample.vue) - Modal form quản lý người dùng

### 📖 Chi tiết Examples

👉 **Xem thêm**: [Examples Directory](./examples/README.md)

---

## ✅ Code Review Checklist

### Components

- [ ] Sử dụng composables cho business logic
- [ ] Tuân thủ component structure template
- [ ] Sử dụng common components khi có thể
- [ ] Có error handling phù hợp
- [ ] Bao gồm accessibility features

### Composables

- [ ] Export form field constants
- [ ] Có comprehensive error handling
- [ ] Bao gồm JSDoc comments
- [ ] Tuân thủ unified naming conventions
- [ ] Có file test tương ứng (optional, khuyến khích cho logic phức tạp)

### Tests (Optional)

- [ ] Có test coverage cho business logic quan trọng
- [ ] Bao gồm edge cases và error scenarios
- [ ] Mock external dependencies
- [ ] Sử dụng tên test mô tả rõ ràng
- [ ] Tuân thủ test structure template

### General

- [ ] Tuân thủ naming conventions
- [ ] Có import organization đúng
- [ ] Bao gồm documentation phù hợp
- [ ] Tuân thủ security best practices
- [ ] Nhất quán với patterns hiện tại

---

## 🔄 Workflow Development

1. **Phân tích requirement** → Xác định business logic cần thiết
2. **Tạo composable** → Implement business logic và state management
3. **Viết tests** → Đảm bảo logic hoạt động đúng (optional, khuyến khích cho logic phức tạp)
4. **Tạo component** → Implement UI sử dụng composable
5. **Integration** → Kết nối component với composable
6. **Review** → Kiểm tra theo checklist trên

---

## 🛠️ Migration Guide

### Cho Components hiện tại

1. **Tách Business Logic** → Chuyển form handling sang composables
2. **Cập nhật Field Names** → Sử dụng tên trường backend trực tiếp
3. **Thêm Tests** → Tạo test suites cho logic quan trọng (optional)
4. **Chuẩn hóa Structure** → Tuân thủ component template

### Cho Features mới

1. **Bắt đầu với Composable** → Định nghĩa form fields và validation
2. **Tạo Components** → Sử dụng common components
3. **Thêm Tests** → Test business logic phức tạp (optional)
4. **Review & Refine** → Tuân thủ code review checklist

---

## 📞 Hỗ trợ

Để được hỗ trợ về kiến trúc hoặc implementation:

1. Kiểm tra examples hiện có trong codebase
2. Xem lại tài liệu này
3. Thảo luận trong team
4. Tạo issue reports chi tiết

---

_Tài liệu này là living document và sẽ được cập nhật khi kiến trúc phát triển._
