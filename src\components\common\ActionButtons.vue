<template>
  <div class="flex justify-center gap-2">
    <!-- View Button -->
    <ButtonCommon v-if="showView" type="default" size="small" :icon="EyeIcon" @click="$emit('view')" class="view-btn">
      Xem
    </ButtonCommon>

    <!-- Edit Button -->
    <router-link v-if="showEdit && editTo" :to="editTo">
      <ButtonCommon type="default" size="small" :icon="EditIcon" class="edit-btn"> Sửa </ButtonCommon>
    </router-link>

    <ButtonCommon
      v-else-if="showEdit && !editTo"
      type="default"
      size="small"
      :icon="EditIcon"
      @click="$emit('edit')"
      class="edit-btn"
    >
      Sửa
    </ButtonCommon>

    <!-- Delete Button -->
    <ButtonCommon
      v-if="showDelete"
      type="default"
      size="small"
      :icon="TrashIcon"
      @click="$emit('delete')"
      class="delete-btn"
    >
      {{ deleteText }}
    </ButtonCommon>
  </div>
</template>

<script setup>
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { EyeIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'

// Props
defineProps({
  // Control which buttons to show
  showView: {
    type: Boolean,
    default: true,
  },
  showEdit: {
    type: Boolean,
    default: true,
  },
  showDelete: {
    type: Boolean,
    default: true,
  },

  // Edit button router link (optional)
  editTo: {
    type: [String, Object],
    default: null,
  },

  // Custom text for delete button
  deleteText: {
    type: String,
    default: 'Xóa',
  },
})

// Emits
defineEmits(['view', 'edit', 'delete'])
</script>

<style scoped>
/* View Button - Kiểu info outline với border và text màu xanh */
:deep(.view-btn) {
  background: #ffffff !important;
  background-image: none !important;
  border-color: #409eff !important;
  color: #409eff !important;
  font-size: 12px !important;
  height: 24px !important;
  min-height: 24px !important;
  padding: 5px 11px !important;
  border-radius: 4px !important;
  border-width: 1px !important;
  transition: all 0.1s !important;
}

:deep(.view-btn:hover) {
  background: #ecf5ff !important;
  background-image: none !important;
  border-color: #66b1ff !important;
  color: #66b1ff !important;
}

:deep(.view-btn:active) {
  background: #d9ecff !important;
  background-image: none !important;
  border-color: #337ecc !important;
  color: #337ecc !important;
}

/* Edit Button - Giống nút Mật khẩu trong UserList (type="warning") màu cam */
:deep(.edit-btn) {
  background: #e6a23c !important;
  background-image: none !important;
  border-color: #e6a23c !important;
  color: #ffffff !important;
  font-size: 12px !important;
  height: 24px !important;
  min-height: 24px !important;
  padding: 5px 11px !important;
  border-radius: 4px !important;
  border-width: 1px !important;
  transition: all 0.1s !important;
}

:deep(.edit-btn:hover) {
  background: #ebb563 !important;
  background-image: none !important;
  border-color: #ebb563 !important;
  color: #ffffff !important;
}

:deep(.edit-btn:active) {
  background: #b8751f !important;
  background-image: none !important;
  border-color: #b8751f !important;
  color: #ffffff !important;
}

/* Delete Button - Giống nút Xóa trong UserList (type="danger") màu đỏ */
:deep(.delete-btn) {
  background: #f56c6c !important;
  background-image: none !important;
  border-color: #f56c6c !important;
  color: #ffffff !important;
  font-size: 12px !important;
  height: 24px !important;
  min-height: 24px !important;
  padding: 5px 11px !important;
  border-radius: 4px !important;
  border-width: 1px !important;
  transition: all 0.1s !important;
}

:deep(.delete-btn:hover) {
  background: #f78989 !important;
  background-image: none !important;
  border-color: #f78989 !important;
  color: #ffffff !important;
}

:deep(.delete-btn:active) {
  background: #c45656 !important;
  background-image: none !important;
  border-color: #c45656 !important;
  color: #ffffff !important;
}

/* Icon styling giống UserList */
:deep(.view-btn svg),
:deep(.edit-btn svg),
:deep(.delete-btn svg) {
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
}

/* Remove focus outline and ring for all buttons */
:deep(.view-btn:focus),
:deep(.edit-btn:focus),
:deep(.delete-btn:focus),
:deep(.view-btn:focus-visible),
:deep(.edit-btn:focus-visible),
:deep(.delete-btn:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}
</style>
