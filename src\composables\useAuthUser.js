import { useAuthStore } from '@/state/index.js'
import { saveData, destroyData<PERSON>y<PERSON><PERSON> } from '@/utils/helpers/localStorage.helper.js'

export function useAuthUser() {
  const { setAuthUser, logout: storeLogout } = useAuthStore()

  function handleSetAuthUser(user) {
    // Update store first
    setAuthUser(user)

    // Then update localStorage
    if (user) {
      saveData('authUser', JSON.stringify(user))
    } else {
      destroyDataByKey('authUser')
    }
  }

  function handleLogout() {
    storeLogout()
  }

  return { handleSetAuthUser, handleLogout }
}
