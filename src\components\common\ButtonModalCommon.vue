<template>
  <div class="flex gap-4 border-t border-gray-200 pt-6 dark:border-gray-700">
    <el-button 
      type="default" 
      @click="handleCancel" 
      :disabled="loading" 
      class="flex-1" 
      size="large"
    >
      <el-icon class="mr-1"><Close /></el-icon>
      {{ cancelText }}
    </el-button>

    <el-button
      type="primary"
      :loading="loading"
      :disabled="!canSubmit"
      class="flex-1"
      size="large"
      @click="handleSubmit"
    >
      <el-icon v-if="!loading" class="mr-1"><Check /></el-icon>
      {{ loading ? loadingText : submitText }}
    </el-button>
  </div>
</template>

<script setup>
import { ElButton, ElIcon } from 'element-plus'
import { Close, Check } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  canSubmit: {
    type: Boolean,
    default: true,
  },
  cancelText: {
    type: String,
    default: 'Hủy',
  },
  submitText: {
    type: String,
    default: 'Lưu',
  },
  loadingText: {
    type: String,
    default: 'Đang lưu...',
  },
})

// Emits
const emit = defineEmits(['cancel', 'submit'])

// Methods
const handleCancel = () => {
  emit('cancel')
}

const handleSubmit = () => {
  emit('submit')
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
