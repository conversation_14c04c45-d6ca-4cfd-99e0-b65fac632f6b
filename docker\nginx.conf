# Cấu hình máy chủ Nginx
server {
    # Lắng nghe trên port 80
    listen 80;

    # Đặt thư mục gốc chứa các file tĩnh của ứng dụng
    root /usr/share/nginx/html;

    # File mặc định để phục vụ
    index index.html;

    # Cấu hình cho các request đến server
    location / {
        # Thử tìm file tương ứng với URI, nếu không tìm thấy thì thử thư mục, 
        # nếu vẫn không thấy thì trả về index.html.
        # Điều này rất quan trọng cho các Single Page Application (SPA) để client-side routing hoạt động.
        try_files $uri $uri/ /index.html;
    }

    # Bật nén gzip để giảm kích thước file truyền đi
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
}
