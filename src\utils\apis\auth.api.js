import apiAxios from '@/utils/configs/axios.config.js'

const authApi = {
  login(data) {
    return apiAxios({
      method: 'post',
      url: '/auth/login',
      data,
    })
  },

  getAuthUser() {
    return apiAxios({
      method: 'get',
      url: '/auth/me',
    })
  },

  logoutUser() {
    return apiAxios({
      method: 'post',
      url: '/auth/logout',
    })
  },

  // Update user profile
  updateProfile(data) {
    return apiAxios({
      method: 'post',
      url: '/auth/update-profile',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data,
    })
  },

  // Change password
  changePassword(data) {
    return apiAxios({
      method: 'put',
      url: '/auth/change-password',
      data,
    })
  },
}

export default authApi
