/**
 * Post Management Composable
 * Handles all post management business logic
 */

import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { postsApi, categoriesApi } from '@/utils/apis/index.js'

// Global message tracking to prevent spam
const messageHistory = new Map()
const MESSAGE_COOLDOWN = 2000 // 2 seconds

const preventMessageSpam = (message) => {
  const now = Date.now()
  const lastTime = messageHistory.get(message) || 0

  if (now - lastTime > MESSAGE_COOLDOWN) {
    messageHistory.set(message, now)
    return true
  }
  return false
}

export function usePosts() {
  // State
  const loading = ref(false)
  const posts = ref([])
  const currentPost = ref(null)
  const categories = ref([])

  // Message helper with spam prevention
  const showMessage = (type, message) => {
    if (preventMessageSpam(message)) {
      ElMessage[type](message)
    } else {
      console.log('Message spam detected:', message)
    }
  }

  const pagination = ref({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1,
    from: 0,
    to: 0,
  })

  const searchParams = reactive({
    title: '',
    status: '',
    category_id: '',
    is_hot: '',
    page: 1,
    limit: 15,
  })

  // Post CRUD operations
  const fetchPosts = async (params = {}) => {
    try {
      loading.value = true
      const mergedParams = { ...searchParams, ...params }

      const response = await postsApi.getPosts(mergedParams)

      if (response.data.success) {
        // Sắp xếp posts theo created_at từ mới nhất đến cũ nhất
        const sortedPosts = (response.data.data.data || []).sort((a, b) => {
          const dateA = new Date(a.created_at || a.createdAt || 0)
          const dateB = new Date(b.created_at || b.createdAt || 0)
          return dateB - dateA // Sắp xếp giảm dần (mới nhất trước)
        })
        
        posts.value = sortedPosts
        pagination.value = response.data.data.pagination || {}
        return response.data
      } else {
        throw new Error(response.data.message || 'Không thể tải danh sách bài viết')
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể tải danh sách bài viết')
      throw error
    } finally {
      loading.value = false
    }
  }

  const createPost = async (postData) => {
    try {
      loading.value = true

      // Đảm bảo dữ liệu Boolean được gửi đúng
      const formattedData = { ...postData }
      if (formattedData.is_hot !== undefined) {
        formattedData.is_hot = formattedData.is_hot ? 1 : 0
      }
      if (formattedData.show_on_homepage !== undefined) {
        formattedData.show_on_homepage = formattedData.show_on_homepage ? 1 : 0
      }

      const response = await postsApi.createPost(formattedData)

      if (response.data.success) {
        showMessage('success', response.data.message || 'Tạo bài viết thành công')
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Không thể tạo bài viết')
      }
    } catch (error) {
      console.error('Error creating post:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)
      } else {
        const errorMessage = message || 'Không thể tạo bài viết'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const getPostById = async (id) => {
    try {
      loading.value = true
      const response = await postsApi.getPostById(id)

      if (response.data.success) {
        currentPost.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Không thể tải thông tin bài viết')
      }
    } catch (error) {
      console.error('Error fetching post:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể tải thông tin bài viết')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updatePost = async (id, postData) => {
    try {
      loading.value = true

      // Đảm bảo dữ liệu Boolean được gửi đúng
      const formattedData = { ...postData }
      if (formattedData.is_hot !== undefined) {
        formattedData.is_hot = formattedData.is_hot ? 1 : 0
      }
      if (formattedData.show_on_homepage !== undefined) {
        formattedData.show_on_homepage = formattedData.show_on_homepage ? 1 : 0
      }

      const response = await postsApi.updatePost(id, formattedData)

      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Không thể cập nhật bài viết')
      }
    } catch (error) {
      console.error('Error updating post:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)
      } else {
        const errorMessage = message || 'Không thể cập nhật bài viết'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const deletePost = async (id) => {
    try {
      await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa bài viết này?', 'Xác nhận xóa', {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      })

      loading.value = true
      const response = await postsApi.deletePost(id)

      if (response.data.success) {
        await fetchPosts()
        ElMessage.success(response.data.message || 'Xóa bài viết thành công')
        return true
      } else {
        throw new Error(response.data.message || 'Không thể xóa bài viết')
      }
    } catch (error) {
      if (error === 'cancel') {
        // Người dùng hủy xóa - trả về null để phân biệt với lỗi
        return null
      } else {
        console.error('Error deleting post:', error)
        ElMessage.error(error.response?.data?.message || error.message || 'Không thể xóa bài viết')
        return false
      }
    } finally {
      loading.value = false
    }
  }

  const updatePostStatus = async (id, field, value) => {
    try {
      // Tạo object với chỉ field cần update
      const attributes = {
        [field]: value,
      }

      // Đảm bảo Boolean được gửi đúng format
      if (field === 'is_hot' || field === 'show_on_homepage') {
        attributes[field] = value ? 1 : 0
      }

      const response = await postsApi.updatePostAttributes(id, attributes)

      if (response.data.success) {
        // Update local data với dữ liệu từ server
        const postIndex = posts.value.findIndex((p) => p.id === id)
        if (postIndex !== -1) {
          // Chỉ update những field được gửi lên, giữ nguyên các field khác
          Object.keys(attributes).forEach((key) => {
            if (response.data.data[key] !== undefined) {
              posts.value[postIndex][key] = response.data.data[key]
            }
          })
        }
        showMessage('success', response.data.message || 'Cập nhật thuộc tính thành công')
      } else {
        throw new Error(response.data.message || 'Không thể cập nhật thuộc tính')
      }
    } catch (error) {
      console.error('Error updating post attributes:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)
      } else {
        const errorMessage = message || 'Không thể cập nhật thuộc tính'
        ElMessage.error(errorMessage)
      }

      throw error // Để component có thể handle loading state
    }
  }

  // Data fetching
  const fetchCategories = async () => {
    try {
      const response = await categoriesApi.getCategories()

      if (response.data.success) {
        categories.value = response.data.data.data || []
        return categories.value
      } else {
        throw new Error(response.data.message || 'Không thể tải danh sách danh mục')
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể tải danh sách danh mục')
      throw error
    }
  }

  // Utility functions
  const getStatusOptions = () => [
    { value: '', label: 'Tất cả trạng thái' },
    { value: 'published', label: 'Đã xuất bản' },
    { value: 'draft', label: 'Bản nháp' },
    { value: 'pending_review', label: 'Chờ duyệt' },
  ]

  const getStatusLabel = (status) => {
    const statusMap = {
      published: 'Đã xuất bản',
      draft: 'Bản nháp',
      pending_review: 'Chờ duyệt',
    }
    return statusMap[status] || status
  }

  const getStatusType = (status) => {
    const typeMap = {
      published: 'success',
      draft: 'warning',
      pending_review: 'info',
    }
    return typeMap[status] || 'info'
  }

  const getCategoryOptions = () => {
    const options = [{ value: '', label: 'Tất cả danh mục' }]
    categories.value.forEach((category) => {
      options.push({
        value: category.id,
        label: category.name,
      })
    })
    return options
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return {
    // State
    loading,
    posts,
    currentPost,
    categories,
    pagination,
    searchParams,

    // CRUD operations
    fetchPosts,
    createPost,
    getPostById,
    updatePost,
    deletePost,
    updatePostStatus,

    // Data fetching
    fetchCategories,

    // Utility functions
    getStatusOptions,
    getStatusLabel,
    getStatusType,
    getCategoryOptions,
    formatDate,
  }
}
