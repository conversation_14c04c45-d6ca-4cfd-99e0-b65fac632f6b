<template>
  <div class="menu-item-tree">
    <div v-for="item in localItems" :key="item.id" class="menu-item-node">
      <div class="menu-item-content" :class="{ selected: selectedItemId === item.id }" @click="selectItem(item)">
        <!-- Expand/Collapse Button -->
        <div class="expand-button" v-if="item.children && item.children.length > 0">
          <ButtonCommon
            :icon="isExpanded(item.id) ? ChevronDownIcon : ChevronRightIcon"
            type="text"
            size="small"
            variant="ghost"
            :tooltip="isExpanded(item.id) ? 'Thu gọn' : 'Mở rộng'"
            @click.stop="toggleExpand(item.id)"
          />
        </div>
        <div v-else class="expand-placeholder"></div>

        <!-- Item Info -->
        <div class="item-info">
          <div class="item-title">{{ item.title }}</div>
          <div class="item-meta">
            <span class="item-slug">{{ item.slug }}</span>
            <span class="item-type" v-if="item.type">{{ item.type }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="item-actions">
          <ButtonCommon
            :icon="EditIcon"
            type="primary"
            size="medium"
            variant="ghost"
            rounded
            tooltip="Chỉnh sửa"
            @click.stop="editItem(item)"
          />
          <ButtonCommon
            :icon="TrashIcon"
            type="danger"
            size="medium"
            variant="ghost"
            rounded
            tooltip="Xóa"
            @click.stop="deleteItem(item)"
          />
        </div>
      </div>

      <!-- Children with collapse/expand animation -->
      <transition name="slide-fade">
        <div v-if="item.children && item.children.length > 0 && isExpanded(item.id)" class="children">
          <DraggableMenuItemTree
            :items="item.children"
            :selected-item-id="selectedItemId"
            :level="level + 1"
            :expanded-items="expandedItems"
            @edit="$emit('edit', $event)"
            @delete="$emit('delete', $event)"
            @toggle-expand="$emit('toggle-expand', $event)"
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { TrashIcon, ChevronRightIcon, ChevronDownIcon, EditIcon } from '@/components/icons/index.js'
import { ElMessageBox } from 'element-plus'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import '@/assets/styles/modules/cms/_menu-item-tree.scss'

// Define component name for self-reference
defineOptions({
  name: 'DraggableMenuItemTree',
})

// Props
const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  selectedItemId: {
    type: [Number, String],
    default: null,
  },
  level: {
    type: Number,
    default: 0,
  },
  expandedItems: {
    type: Set,
    default: () => new Set(),
  },
})

// Emits
const emit = defineEmits(['edit', 'delete', 'toggle-expand'])

// Local reactive copy of items for draggable
const localItems = ref([...props.items])


// Watch for changes in props.items
watch(
  () => props.items,
  (newItems) => {
    localItems.value = [...newItems]
  },
  { deep: true },
)

// Methods
const selectItem = (item) => {
  emit('edit', item)
}

const editItem = (item) => {
  emit('edit', item)
}

const deleteItem = async (item) => {
  try {
    await ElMessageBox.confirm(`Bạn có chắc chắn muốn xóa menu item "${item.title}"?`, 'Xác nhận xóa', {
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy',
      type: 'warning',
    })
    emit('delete', item.id)
  } catch (error) {
    // User cancelled
  }
}

const toggleExpand = (itemId) => {
  emit('toggle-expand', itemId)
}

const isExpanded = (itemId) => {
  return props.expandedItems.has(itemId)
}
</script>
