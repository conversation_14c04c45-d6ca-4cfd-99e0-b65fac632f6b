/* ===== PERMISSION TABLE STYLES ===== */

/* Main container */
.permission-table {
  @apply relative flex h-full flex-col overflow-hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);

  .dark & {
    background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.4),
      0 2px 4px -1px rgba(0, 0, 0, 0.3);
  }

  /* Prevent page scroll on table container */
  .flex-1 {
    @apply relative overflow-hidden;
  }
}

/* Table wrapper */
.table-wrapper {
  @apply relative h-full overflow-hidden;
  max-height: 600px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: #ffffff;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  .dark & {
    border-color: #1e293b;
    background: rgba(15, 23, 42, 0.95);
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3);
  }
}

/* ===== TABLE CORE STYLES ===== */

.permission-tree-table {
  :deep(.el-table) {
    @apply rounded-xl;
    height: 100%;
    max-height: 600px;
    table-layout: fixed;
    background: #ffffff;
    border: none;
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif;

    .dark & {
      background: #0f172a;
      color: #f8fafc;
    }
  }

  /* Header wrapper */
  :deep(.el-table__header-wrapper) {
    @apply sticky top-0 z-20 w-full overflow-hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 2px solid #e5e7eb;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    .dark & {
      background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
      border-bottom-color: #1e293b;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.4),
        0 2px 4px -1px rgba(0, 0, 0, 0.3);
    }
  }

  /* Body wrapper */
  :deep(.el-table__body-wrapper) {
    @apply w-full overflow-y-auto;
    scrollbar-width: thin;
    scrollbar-color: #3b82f6 #f8fafc;
    max-height: 500px;
    height: 500px;
    background: #ffffff;
    border-radius: 0 0 12px 12px;

    .dark & {
      background: #0f172a;
      scrollbar-color: #60a5fa #0f172a;
    }
  }

  /* Header */
  :deep(.el-table__header) {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 2px solid #e5e7eb;

    .dark & {
      background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
      border-bottom-color: #1e293b;
    }
  }

  /* Header cells */
  :deep(.el-table__header-cell) {
    @apply relative p-3 font-semibold;
    background: transparent !important;
    border-bottom: 1px solid #e5e7eb !important;
    font-weight: 600 !important;
    color: #374151 !important;
    padding: 16px 20px !important;
    font-size: 14px !important;
    letter-spacing: 0.025em;
    transition: all 0.2s ease-in-out;

    .dark & {
      border-bottom-color: #1e293b !important;
      color: #f8fafc !important;
    }

    &:hover {
      background: rgba(59, 130, 246, 0.05) !important;
      transform: translateY(-1px);

      .dark & {
        background: rgba(96, 165, 250, 0.1) !important;
      }
    }
  }

  /* Hide default expand icon - Multiple selectors for complete coverage */
  :deep(.el-table__expand-icon),
  :deep(.el-table__expand-icon .el-icon),
  :deep(.el-table__expand-icon .el-icon svg),
  :deep(.el-table__expand-icon .el-icon i),
  :deep(.el-table__expand-icon .el-icon .el-icon-arrow-right),
  :deep(.el-table__expand-icon .el-icon .el-icon-arrow-down),
  :deep(.el-table__expand-icon .el-icon .el-icon-caret-right),
  :deep(.el-table__expand-icon .el-icon .el-icon-caret-bottom) {
    @apply pointer-events-none hidden;
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
  }

  /* Hide default expand column - Multiple selectors for complete coverage */
  :deep(.el-table__expand-column),
  :deep(.el-table__expand-column .cell),
  :deep(.el-table__expand-column .el-table__cell),
  :deep(.el-table__expand-column .el-table__expand-icon),
  :deep(.el-table__expand-column .el-table__expand-icon .el-icon) {
    @apply m-0 hidden w-0 max-w-0 min-w-0 border-none p-0;
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    background: none !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
  }

  /* Remove expand column spacing */
  :deep(.el-table__expand-column) {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
  }

  /* Ensure proper table layout without expand column */
  :deep(.el-table__body) {
    @apply w-full;
  }

  :deep(.el-table__header) {
    @apply w-full;
  }

  /* Fixed column styling */
  :deep(.el-table__fixed) {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    .dark & {
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
    }
  }

  /* Table rows */
  :deep(.el-table__row) {
    height: 56px;
    @apply relative;
    background: #ffffff;
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    contain: layout style;

    .dark & {
      background: #0f172a;
      border-bottom-color: #1e293b;
    }

    &:hover {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
      transform: translateY(-2px);
      box-shadow:
        0 8px 25px -5px rgba(0, 0, 0, 0.1),
        0 4px 10px -6px rgba(0, 0, 0, 0.05);
      z-index: 10;

      .dark & {
        background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
        box-shadow:
          0 8px 25px -5px rgba(0, 0, 0, 0.4),
          0 4px 10px -6px rgba(0, 0, 0, 0.3);
      }
    }
  }

  /* Table cells */
  :deep(.el-table__cell) {
    @apply p-2;
    padding: 16px 20px !important;
    background: transparent;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.2s ease-in-out;

    .dark & {
      border-bottom-color: #1e293b;
      color: #f8fafc;
    }
  }

  /* Prevent page scroll on row interactions */
  :deep(.el-table__row) {
    overflow-anchor: none;
  }

  :deep(.el-table__cell) {
    overflow-anchor: none;
  }

  /* Prevent page scroll when expanding */
  :deep(.el-table__body-wrapper) {
    overscroll-behavior: contain;
    overflow-anchor: none;
  }

  /* Prevent page scroll on table interactions */
  :deep(.el-table) {
    overflow-anchor: none;
  }

  /* Smooth expand/collapse transitions */
  :deep(.el-table__row) {
    @apply transition-all duration-300;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ensure no default expand behavior */
  :deep(.el-table__expand-icon) {
    @apply pointer-events-none hidden;
    display: none !important;
    pointer-events: none !important;
  }

  :deep(.el-table__expand-column) {
    @apply hidden w-0 p-0;
    display: none !important;
    width: 0 !important;
    padding: 0 !important;
  }

  /* Table performance optimizations */
  :deep(.el-table) {
    will-change: scroll-position;
    contain: layout style;
  }

  :deep(.el-table__body-wrapper) {
    will-change: scroll-position;
    contain: layout style paint;
    @apply relative z-10;
  }

  /* Smooth scrolling for table body */
  :deep(.el-table__body-wrapper) {
    @apply scroll-smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    overflow-anchor: none;
    isolation: isolate;
  }

  /* Fixed header shadow enhancement */
  :deep(.el-table__header-wrapper) {
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.05);

    .dark & {
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.2);
    }
  }
}

/* ===== EXPAND ICON STYLES ===== */

.expand-button-wrapper {
  @apply relative z-10 flex cursor-pointer items-center justify-center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  background: transparent;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:focus {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
  }

  &:hover {
    transform: scale(1.1);
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);

    .dark & {
      background: rgba(96, 165, 250, 0.2);
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.expand-icon {
  @apply cursor-pointer;
  font-size: 18px;
  color: #6b7280;
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.1);
    color: #3b82f6;
  }

  &.is-expanded {
    transform: rotate(90deg);
    color: #3b82f6;
  }

  .dark & {
    color: #9ca3af;

    &:hover {
      color: #60a5fa;
    }

    &.is-expanded {
      color: #60a5fa;
    }
  }
}

.expand-button-wrapper:hover .expand-icon {
  color: #3b82f6;

  .dark & {
    color: #60a5fa;
  }
}

/* ===== CHECKBOX STYLES ===== */

.permission-checkbox {
  :deep(.el-checkbox__input) {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  :deep(.el-checkbox__input.is-checked) {
    transform: scale(1.1);
  }

  :deep(.el-checkbox__input .el-checkbox__inner) {
    width: 20px;
    height: 20px;
    border-radius: 6px;
    border: 2px solid #d1d5db;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    }
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-color: #3b82f6;
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.3),
      0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
    border-color: #ffffff;
    width: 6px;
    height: 10px;
    left: 5px;
    top: 2px;
  }

  /* Dark mode checkbox styles */
  .dark & {
    :deep(.el-checkbox__input .el-checkbox__inner) {
      background: #374151;
      border-color: #6b7280;

      &:hover {
        border-color: #60a5fa;
        box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
      }
    }

    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
      background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
      border-color: #60a5fa;
      box-shadow:
        0 4px 12px rgba(96, 165, 250, 0.3),
        0 0 0 2px rgba(96, 165, 250, 0.1);
    }
  }

  /* Disabled checkbox styles for protected roles */
  :deep(.protected-role-checkbox) {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    user-select: none !important;

    * {
      pointer-events: none !important;
      cursor: not-allowed !important;
    }

    .el-checkbox__input {
      opacity: 0.3 !important;
      cursor: not-allowed !important;
      pointer-events: none !important;
      user-select: none !important;

      .el-checkbox__inner {
        background: #d1d5db !important;
        border-color: #9ca3af !important;
        cursor: not-allowed !important;
        pointer-events: none !important;
        user-select: none !important;
      }
    }

    .el-checkbox__label {
      color: #9ca3af !important;
      cursor: not-allowed !important;
      pointer-events: none !important;
      user-select: none !important;
    }

    .dark & {
      .el-checkbox__input .el-checkbox__inner {
        background: #1f2937 !important;
        border-color: #374151 !important;
      }
    }
  }

  /* Loading spinner for changing permissions */
  :deep(.el-checkbox__input.is-disabled:not(.protected-role-checkbox .el-checkbox__input)) {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      border: 2px solid #3b82f6;
      border-top: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
    }
  }

  @keyframes spin {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
}

/* Table transition animations */
.table-fade-enter-active,
.table-fade-leave-active {
  transition: opacity 0.2s ease;
}

.table-fade-enter-from,
.table-fade-leave-to {
  opacity: 0;
}

.table-fade-enter-to,
.table-fade-leave-from {
  opacity: 1;
}

/* Mobile checkbox optimizations */
@media (max-width: 768px) {
  .permission-checkbox {
    :deep(.el-checkbox__input) {
      @apply scale-90 transform;
    }

    :deep(.el-checkbox__input.is-checked) {
      @apply scale-95 transform;
    }
  }
}

/* Compact header styles */
.permission-table .sticky {
  min-height: 48px;
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .permission-table .sticky h3 {
    @apply text-sm;
    font-size: 13px;
  }

  .permission-table .sticky span {
    @apply text-xs;
    font-size: 11px;
  }

  .permission-tree-table :deep(.el-table__header-cell) {
    @apply text-xs;
    font-size: 11px !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .permission-table .sticky h3 {
    @apply text-base;
    font-size: 14px;
  }

  .permission-table .sticky span {
    @apply text-sm;
    font-size: 12px;
  }
}

/* Compact table styles */
.permission-tree-table :deep(.el-table__row) {
  height: 48px;
  background: #ffffff;
  border-bottom: 1px solid #f3f4f6;

  .dark & {
    background: #1e293b;
    border-bottom-color: #374151;
  }
}

.permission-tree-table :deep(.el-table__cell) {
  @apply p-2;
  padding: 8px 12px !important;
  background: transparent;
  color: #374151;

  .dark & {
    color: #f1f5f9;
  }
}

.permission-tree-table :deep(.el-table__header-cell) {
  @apply p-2;
  padding: 10px 12px !important;
  background: #f8fafc;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;

  .dark & {
    background: #1e293b;
    color: #f1f5f9;
    border-bottom-color: #374151;
  }
}

/* Responsive table height */
@media (max-width: 640px) {
  .table-wrapper {
    max-height: 350px;
  }

  .permission-tree-table :deep(.el-table) {
    max-height: 350px;
  }

  .permission-tree-table :deep(.el-table__body-wrapper) {
    max-height: 300px;
    height: 300px;
    @apply w-full;
  }

  .permission-tree-table :deep(.el-table__header-wrapper) {
    @apply w-full;
  }

  .permission-tree-table :deep(.el-table__header-cell) {
    @apply p-2 text-xs;
    padding: 8px 12px !important;
    font-size: 12px !important;
  }

  .permission-tree-table :deep(.el-table__cell) {
    @apply p-1;
    padding: 6px 12px !important;
  }

  /* Ensure expand column is hidden on mobile */
  .permission-tree-table :deep(.el-table__expand-column) {
    @apply hidden w-0 max-w-0 min-w-0;
    display: none !important;
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .table-wrapper {
    max-height: 400px;
  }

  .permission-tree-table :deep(.el-table) {
    max-height: 400px;
  }

  .permission-tree-table :deep(.el-table__body-wrapper) {
    max-height: 350px;
    height: 350px;
  }

  .permission-tree-table :deep(.el-table__header-cell) {
    @apply p-2 text-sm;
    padding: 10px 14px !important;
    font-size: 13px !important;
  }

  .permission-tree-table :deep(.el-table__cell) {
    @apply p-2;
    padding: 8px 14px !important;
  }
}

@media (min-width: 769px) and (max-width: 1023px) {
  .table-wrapper {
    max-height: 500px;
  }

  .permission-tree-table :deep(.el-table) {
    max-height: 500px;
  }

  .permission-tree-table :deep(.el-table__body-wrapper) {
    max-height: 450px;
    height: 450px;
  }
}

@media (min-width: 1024px) {
  .table-wrapper {
    max-height: 600px;
  }

  .permission-tree-table :deep(.el-table) {
    max-height: 600px;
  }

  .permission-tree-table :deep(.el-table__body-wrapper) {
    max-height: 500px;
    height: 500px;
  }
}

/* Mobile touch optimizations */
@media (max-width: 768px) {
  .permission-tree-table :deep(.el-table__body-wrapper) {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: auto;
  }

  .permission-tree-table :deep(.el-table__row) {
    touch-action: manipulation;
  }

  .expand-button-wrapper {
    touch-action: manipulation;
    min-height: 44px;
    min-width: 44px;
  }
}

/* ===== SCROLLBAR STYLES ===== */

.permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
}

.permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
  border-radius: 10px;
  margin: 4px 0;
  border: 1px solid rgba(0, 0, 0, 0.05);

  .dark & {
    background: linear-gradient(to bottom, #1e293b, #0f172a);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

.permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 2px 4px rgba(59, 130, 246, 0.2),
    0 0 0 1px rgba(59, 130, 246, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .dark & {
    background: linear-gradient(to bottom, #60a5fa, #3b82f6);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow:
      0 2px 4px rgba(96, 165, 250, 0.3),
      0 0 0 1px rgba(96, 165, 250, 0.1);
  }
}

.permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(to bottom, #2563eb, #1e40af);
  box-shadow:
    0 4px 8px rgba(59, 130, 246, 0.3),
    0 0 0 2px rgba(59, 130, 246, 0.2);
  transform: scaleX(1.2);

  .dark & {
    background: linear-gradient(to bottom, #3b82f6, #2563eb);
    box-shadow:
      0 4px 8px rgba(96, 165, 250, 0.4),
      0 0 0 2px rgba(96, 165, 250, 0.2);
  }
}

.permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:active) {
  background: linear-gradient(to bottom, #1d4ed8, #1e3a8a);
  box-shadow:
    0 2px 4px rgba(59, 130, 246, 0.4),
    0 0 0 3px rgba(59, 130, 246, 0.3);

  .dark & {
    background: linear-gradient(to bottom, #2563eb, #1d4ed8);
    box-shadow:
      0 2px 4px rgba(96, 165, 250, 0.5),
      0 0 0 3px rgba(96, 165, 250, 0.3);
  }
}

.permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-corner) {
  background: transparent;
}

/* Dark mode scrollbar */
.dark .permission-tree-table :deep(.el-table__body-wrapper) {
  scrollbar-color: #60a5fa #0f172a;
}

.dark .permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: linear-gradient(to bottom, #0f172a, #020617);
  @apply border border-gray-600/10;
}

.dark .permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: linear-gradient(to bottom, #60a5fa, #3b82f6);
  @apply border border-white/10;
  box-shadow: 0 2px 4px rgba(96, 165, 250, 0.3);
}

.dark .permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(to bottom, #3b82f6, #2563eb);
  box-shadow: 0 4px 8px rgba(96, 165, 250, 0.4);
  @apply scale-x-110 transform;
}

.dark .permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:active) {
  background: linear-gradient(to bottom, #2563eb, #1d4ed8);
  box-shadow: 0 2px 4px rgba(96, 165, 250, 0.5);
}

/* Responsive scrollbar */
@media (min-width: 768px) {
  .permission-tree-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
    width: 8px;
  }
}

/* Scrollbar animations */
@keyframes scrollbarGlow {
  0%,
  100% {
    box-shadow: 0 3px 6px rgba(59, 130, 246, 0.25);
  }
  50% {
    box-shadow:
      0 3px 6px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2);
  }
}

@keyframes darkScrollbarGlow {
  0%,
  100% {
    box-shadow: 0 3px 6px rgba(96, 165, 250, 0.35);
  }
  50% {
    box-shadow:
      0 3px 6px rgba(96, 165, 250, 0.5),
      0 0 20px rgba(96, 165, 250, 0.3);
  }
}

/* Animated scrollbar on scroll */
.permission-tree-table :deep(.el-table__body-wrapper:not(:hover)::-webkit-scrollbar-thumb) {
  animation: scrollbarGlow 3s ease-in-out infinite;
}

.dark .permission-tree-table :deep(.el-table__body-wrapper:not(:hover)::-webkit-scrollbar-thumb) {
  animation: darkScrollbarGlow 3s ease-in-out infinite;
}

/* Hide scrollbar when not needed */
.permission-tree-table :deep(.el-table__body-wrapper:not(:hover)::-webkit-scrollbar-thumb) {
  opacity: 0.7;
}

.permission-tree-table :deep(.el-table__body-wrapper:hover::-webkit-scrollbar-thumb) {
  opacity: 1;
}

/* Sticky header enhancement */
.sticky {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(248, 250, 252, 0.95);

  .dark & {
    background: rgba(15, 23, 42, 0.95);
  }
}

/* Unified header styling */
.permission-table .sticky {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e5e7eb;

  .dark & {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    border-bottom-color: #1e293b;
  }
}

/* Role info styling */
.role-info-container {
  @apply rounded-xl border border-blue-500/10 bg-white/80 p-3 backdrop-blur-sm;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  .dark & {
    background: rgba(15, 23, 42, 0.8);
    border-color: rgba(96, 165, 250, 0.2);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }
}

/* Loading animation for permission table */
.permission-tree-table {
  :deep(.el-loading-spinner) {
    @apply rounded-xl bg-white/90 p-5;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    .dark & {
      background: rgba(15, 23, 42, 0.9);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    }
  }

  :deep(.el-loading-text) {
    @apply text-sm font-medium;
    color: #3b82f6;
    margin-top: 8px;

    .dark & {
      color: #60a5fa;
    }
  }

  :deep(.el-loading-mask) {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);

    .dark & {
      background: rgba(15, 23, 42, 0.8);
    }
  }
}

/* Button hover effects */
:deep(.el-button) {
  @apply border-none font-medium transition-all duration-200 ease-in-out;
}

:deep(.el-button:hover) {
  @apply -translate-y-px transform;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-button:active) {
  @apply translate-y-0 transform;
}

/* Tooltip styles */
:deep(.el-tooltip__popper) {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  @apply rounded-lg border-none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  color: #f9fafb;

  .dark & {
    background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    color: #f1f5f9;
  }
}

/* Empty state animations */
@keyframes fadeInUp {
  from {
    transform: translateY(5px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Dark mode text and icon styles */
.dark .permission-table {
  color: #f1f5f9;
}

.dark .permission-table h1,
.dark .permission-table h2,
.dark .permission-table h3,
.dark .permission-table h4,
.dark .permission-table h5,
.dark .permission-table h6 {
  color: #f8fafc;
}

.dark .permission-table p,
.dark .permission-table span,
.dark .permission-table div {
  color: #e2e8f0;
}

.dark .permission-table .text-gray-500 {
  color: #9ca3af !important;
}

.dark .permission-table .text-gray-400 {
  color: #9ca3af !important;
}

.dark .permission-table .text-gray-300 {
  color: #d1d5db !important;
}

.dark .permission-table .text-gray-600 {
  color: #9ca3af !important;
}

.dark .permission-table .text-gray-700 {
  color: #d1d5db !important;
}

.dark .permission-table .text-gray-900 {
  color: #f8fafc !important;
}

.dark .permission-table .bg-white {
  background-color: #1e293b !important;
}

.dark .permission-table .bg-gray-50 {
  background-color: #0f172a !important;
}

.dark .permission-table .bg-gray-100 {
  background-color: #1e293b !important;
}

.dark .permission-table .border-gray-200 {
  border-color: #374151 !important;
}

.dark .permission-table .border-gray-300 {
  border-color: #4b5563 !important;
}

/* Optimized checkbox styles */
.permission-checkbox :deep(.el-checkbox__input) {
  @apply scale-100 transform;
}

.permission-checkbox :deep(.el-checkbox__input.is-checked) {
  @apply scale-105 transform;
}

@media (min-width: 768px) {
  .permission-tree-table :deep(.el-table__row) {
    height: 56px;
  }

  .permission-tree-table :deep(.el-table__cell) {
    @apply p-2;
    padding: 10px 16px !important;
  }

  .permission-tree-table :deep(.el-table__header-cell) {
    @apply p-3;
    padding: 12px 16px !important;
  }

  .expand-button-wrapper {
    @apply h-6 w-6 rounded-lg;
    width: 24px;
    height: 24px;
    border-radius: 6px;
  }

  .expand-icon {
    @apply text-base;
    font-size: 16px;
  }

  .permission-checkbox :deep(.el-checkbox__input.is-checked) {
    @apply scale-110 transform;
  }
}
