
@import 'tailwindcss';

@font-face {
  font-family: 'GitLab Sans';
  src: url('../fonts/GitLabSans.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'GitLab Sans';
  src: url('../fonts/GitLabSans-Italic.ttf') format('truetype');
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/* Add specific bold weight support */
@font-face {
  font-family: 'GitLab Sans';
  src: url('../fonts/GitLabSans-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Global hide Element Plus table expand icons */
.el-table .el-table__expand-icon,
.el-table .el-table__expand-icon .el-icon,
.el-table .el-table__expand-icon .el-icon svg,
.el-table .el-table__expand-icon .el-icon i,
.el-table .el-table__expand-icon .el-icon .el-icon-arrow-right,
.el-table .el-table__expand-icon .el-icon .el-icon-arrow-down,
.el-table .el-table__expand-icon .el-icon .el-icon-caret-right,
.el-table .el-table__expand-icon .el-icon .el-icon-caret-bottom,
.el-table .el-table__expand-icon .el-icon .el-icon-arrow,
.el-table .el-table__expand-icon .el-icon .el-icon-caret,
.el-table .el-table__expand-icon .el-icon .el-icon-arrow-left,
.el-table .el-table__expand-icon .el-icon .el-icon-arrow-up,
.el-table .el-table__expand-icon .el-icon .el-icon-caret-left,
.el-table .el-table__expand-icon .el-icon .el-icon-caret-top {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
  font-size: 0 !important;
  line-height: 0 !important;
  transform: scale(0) !important;
  transform-origin: center !important;
}

.el-table .el-table__expand-column,
.el-table .el-table__expand-column .cell,
.el-table .el-table__expand-column .el-table__cell,
.el-table .el-table__expand-column .el-table__expand-icon,
.el-table .el-table__expand-column .el-table__expand-icon .el-icon {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: none !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
  font-size: 0 !important;
  line-height: 0 !important;
}

@custom-variant dark (&:is(.dark *));

@theme {
  --font-*: initial;
  --font-gitlab-sans: 'GitLab Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans', sans-serif;

  --breakpoint-*: initial;
  --breakpoint-2xsm: 375px;
  --breakpoint-xsm: 425px;
  --breakpoint-3xl: 2000px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --text-title-2xl: 72px;
  --text-title-2xl--line-height: 90px;
  --text-title-xl: 60px;
  --text-title-xl--line-height: 72px;
  --text-title-lg: 48px;
  --text-title-lg--line-height: 60px;
  --text-title-md: 36px;
  --text-title-md--line-height: 44px;
  --text-title-sm: 30px;
  --text-title-sm--line-height: 38px;
  --text-theme-xl: 20px;
  --text-theme-xl--line-height: 30px;
  --text-theme-sm: 14px;
  --text-theme-sm--line-height: 20px;
  --text-theme-xs: 12px;
  --text-theme-xs--line-height: 18px;

  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #101828;

  --color-brand-25: #f2f7ff;
  --color-brand-50: #ecf3ff;
  --color-brand-100: #dde9ff;
  --color-brand-200: #c2d6ff;
  --color-brand-300: #9cb9ff;
  --color-brand-400: #7592ff;
  --color-brand-500: #465fff;
  --color-brand-600: #3641f5;
  --color-brand-700: #2a31d8;
  --color-brand-800: #252dae;
  --color-brand-900: #262e89;
  --color-brand-950: #161950;

  --color-blue-light-25: #f5fbff;
  --color-blue-light-50: #f0f9ff;
  --color-blue-light-100: #e0f2fe;
  --color-blue-light-200: #b9e6fe;
  --color-blue-light-300: #7cd4fd;
  --color-blue-light-400: #36bffa;
  --color-blue-light-500: #0ba5ec;
  --color-blue-light-600: #0086c9;
  --color-blue-light-700: #026aa2;
  --color-blue-light-800: #065986;
  --color-blue-light-900: #0b4a6f;
  --color-blue-light-950: #062c41;

  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f2f4f7;
  --color-gray-200: #e4e7ec;
  --color-gray-300: #d0d5dd;
  --color-gray-400: #98a2b3;
  --color-gray-500: #667085;
  --color-gray-600: #475467;
  --color-gray-700: #344054;
  --color-gray-800: #1d2939;
  --color-gray-900: #101828;
  --color-gray-950: #0c111d;
  --color-gray-dark: #1a2231;

  --color-orange-25: #fffaf5;
  --color-orange-50: #fff6ed;
  --color-orange-100: #ffead5;
  --color-orange-200: #fddcab;
  --color-orange-300: #feb273;
  --color-orange-400: #fd853a;
  --color-orange-500: #fb6514;
  --color-orange-600: #ec4a0a;
  --color-orange-700: #c4320a;
  --color-orange-800: #9c2a10;
  --color-orange-900: #7e2410;
  --color-orange-950: #511c10;

  --color-success-25: #f6fef9;
  --color-success-50: #ecfdf3;
  --color-success-100: #d1fadf;
  --color-success-200: #a6f4c5;
  --color-success-300: #6ce9a6;
  --color-success-400: #32d583;
  --color-success-500: #12b76a;
  --color-success-600: #039855;
  --color-success-700: #027a48;
  --color-success-800: #05603a;
  --color-success-900: #054f31;
  --color-success-950: #053321;

  --color-error-25: #fffbfa;
  --color-error-50: #fef3f2;
  --color-error-100: #fee4e2;
  --color-error-200: #fecdca;
  --color-error-300: #fda29b;
  --color-error-400: #f97066;
  --color-error-500: #f04438;
  --color-error-600: #d92d20;
  --color-error-700: #b42318;
  --color-error-800: #912018;
  --color-error-900: #7a271a;
  --color-error-950: #55160c;

  --color-warning-25: #fffcf5;
  --color-warning-50: #fffaeb;
  --color-warning-100: #fef0c7;
  --color-warning-200: #fedf89;
  --color-warning-300: #fec84b;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #93370d;
  --color-warning-900: #7a2e0e;
  --color-warning-950: #4e1d09;

  --color-theme-pink-500: #ee46bc;

  --color-theme-purple-500: #7a5af8;

  --shadow-theme-md: 0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --shadow-theme-lg: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  --shadow-theme-sm: 0px 1px 3px 0px rgba(16, 24, 40, 0.1), 0px 1px 2px 0px rgba(16, 24, 40, 0.06);
  --shadow-theme-xs: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  --shadow-theme-xl: 0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  --shadow-datepicker: -5px 0 0 #262d3c, 5px 0 0 #262d3c;
  --shadow-focus-ring: 0px 0px 0px 4px rgba(70, 95, 255, 0.12);
  --shadow-slider-navigation: 0px 1px 2px 0px rgba(16, 24, 40, 0.1), 0px 1px 3px 0px rgba(16, 24, 40, 0.1);
  --shadow-tooltip: 0px 4px 6px -2px rgba(16, 24, 40, 0.05), -8px 0px 20px 8px rgba(16, 24, 40, 0.05);

  --drop-shadow-4xl: 0 35px 35px rgba(0, 0, 0, 0.25), 0 45px 65px rgba(0, 0, 0, 0.15);

  --z-index-1: 1;
  --z-index-9: 9;
  --z-index-99: 99;
  --z-index-999: 999;
  --z-index-9999: 9999;
  --z-index-99999: 99999;
  --z-index-999999: 999999;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }
  
  /* Remove focus outline and ring for all buttons */
  button:focus,
  [role='button']:focus {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }
  
  /* Remove focus ring for all buttons */
  button:focus-visible,
  [role='button']:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }
  
  /* Remove focus ring for Element Plus buttons */
  .el-button:focus,
  .el-button:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }
  
  /* Remove focus ring for all buttons globally */
  *:focus {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }
  
  /* Remove focus ring for all elements */
  *:focus,
  *:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }
  
  /* Remove focus ring specifically for buttons */
  button:focus,
  button:focus-visible,
  [role='button']:focus,
  [role='button']:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }
  
  body {
    @apply font-gitlab-sans relative z-1 bg-gray-50 font-normal dark:bg-gray-900;
  }
  
  /* Enhanced font weight utilities */
  .font-bold {
    font-weight: 700;
  }
  
  .font-extrabold {
    font-weight: 800;
  }
  
  .font-black {
    font-weight: 900;
  }
}

@utility menu-item {
  @apply text-theme-sm relative flex w-full items-center gap-3 rounded-lg px-3 py-2 font-medium;
}

@utility menu-item-active {
  @apply bg-brand-50 text-brand-500 dark:bg-brand-500/[0.12] dark:text-brand-400;
}

@utility menu-item-inactive {
  @apply text-gray-700 group-hover:text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-white/5 dark:hover:text-gray-300;
}

@utility menu-item-icon {
  @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400;
}

@utility menu-item-icon-active {
  @apply text-brand-500 dark:text-brand-400;
}

@utility menu-item-icon-inactive {
  @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300;
}

@utility menu-item-arrow {
  @apply relative;
}

@utility menu-item-arrow-active {
  @apply text-brand-500 dark:text-brand-400 rotate-180;
}

@utility menu-item-arrow-inactive {
  @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300;
}

@utility menu-dropdown-item {
  @apply text-theme-sm relative flex items-center gap-3 rounded-lg px-3 py-2.5 font-medium;
}

@utility menu-dropdown-item-active {
  @apply bg-brand-50 text-brand-500 dark:bg-brand-500/[0.12] dark:text-brand-400;
}

@utility menu-dropdown-item-inactive {
  @apply text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-white/5;
}

@utility menu-dropdown-badge {
  @apply text-brand-500 dark:text-brand-400 block rounded-full px-2.5 py-0.5 text-xs font-medium uppercase;
}

@utility menu-dropdown-badge-active {
  @apply bg-brand-100 dark:bg-brand-500/20;
}

@utility menu-dropdown-badge-inactive {
  @apply bg-brand-50 group-hover:bg-brand-100 dark:bg-brand-500/15 dark:group-hover:bg-brand-500/20;
}

@utility no-scrollbar {
  /* Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@utility custom-scrollbar {
  &::-webkit-scrollbar {
    @apply size-1.5;
  }

  &::-webkit-scrollbar-track {
    @apply rounded-full;
  }

  &::-webkit-scrollbar-thumb {
    @apply rounded-full bg-gray-200 dark:bg-gray-700;
  }
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #344054;
}

@layer utilities {
  /* For Remove Date Icon */
  input[type='date']::-webkit-inner-spin-button,
  input[type='time']::-webkit-inner-spin-button,
  input[type='date']::-webkit-calendar-picker-indicator,
  input[type='time']::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }
}

.sidebar:hover {
  width: 290px;
}
.sidebar:hover .logo {
  display: block;
}
.sidebar:hover .logo-icon {
  display: none;
}
.sidebar:hover .sidebar-header {
  justify-content: space-between;
}
.sidebar:hover .menu-group-title {
  display: block;
}
.sidebar:hover .menu-group-icon {
  display: none;
}

.sidebar:hover .menu-item-text {
  display: inline;
}

.sidebar:hover .menu-item-arrow {
  display: block;
}

.sidebar:hover .menu-dropdown {
  display: flex;
}

.tableCheckbox:checked ~ span span {
  @apply opacity-100;
}
.tableCheckbox:checked ~ span {
  @apply border-brand-500 bg-brand-500;
}

/* third-party libraries CSS */
.apexcharts-legend-text {
  @apply !text-gray-700 dark:!text-gray-400;
}

.apexcharts-text {
  @apply !fill-gray-700 dark:!fill-gray-400;
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply !shadow-theme-sm gap-1 !rounded-lg !border-gray-200 p-3 dark:!border-gray-800 dark:!bg-gray-900;
}

.apexcharts-legend-text {
  @apply !pl-5 !text-gray-700 dark:!text-gray-400;
}
.apexcharts-tooltip-series-group {
  @apply !p-0;
}
.apexcharts-tooltip-y-group {
  @apply !p-0;
}
.apexcharts-tooltip-title {
  @apply !mb-0 !border-b-0 !bg-transparent !p-0 !text-[10px] !leading-4 !text-gray-800 dark:!text-white/90;
}
.apexcharts-tooltip-text {
  @apply !text-theme-xs !text-gray-700 dark:!text-white/90;
}
.apexcharts-tooltip-text-y-value {
  @apply !font-medium;
}

.apexcharts-gridline {
  @apply !stroke-gray-100 dark:!stroke-gray-800;
}
#chartTwo .apexcharts-datalabels-group {
  @apply !-translate-y-24;
}
#chartTwo .apexcharts-datalabels-group .apexcharts-text {
  @apply !fill-gray-800 !font-semibold dark:!fill-white/90;
}

#chartSixteen .apexcharts-legend {
  @apply !p-0 !pl-6;
}

.jvm-container {
  @apply !bg-gray-50 dark:!bg-gray-900;
}
.jvm-region.jvm-element {
  @apply hover:!fill-brand-500 dark:hover:!fill-brand-500 !fill-gray-300 dark:!fill-gray-700;
}
.jvm-marker.jvm-element {
  @apply !stroke-gray-200 dark:!stroke-gray-800;
}

.stocks-slider-outer .swiper-button-next:after,
.stocks-slider-outer .swiper-button-prev:after {
  @apply hidden;
}

.stocks-slider-outer .swiper-button-next,
.stocks-slider-outer .swiper-button-prev {
  @apply !static mt-0 h-8 w-9 rounded-full border border-gray-200 !text-gray-700 transition hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-800 dark:!text-gray-400;
}

.stocks-slider-outer .swiper-button-next.swiper-button-disabled,
.stocks-slider-outer .swiper-button-prev.swiper-button-disabled {
  @apply bg-white opacity-50 dark:bg-gray-900;
}

.stocks-slider-outer .swiper-button-next svg,
.stocks-slider-outer .swiper-button-prev svg {
  @apply !h-auto !w-auto;
}

.flatpickr-wrapper {
  @apply w-full;
}
.flatpickr-calendar {
  @apply dark:!shadow-theme-xl 2xsm:!w-auto mt-2 !rounded-xl !border !border-transparent bg-black !p-5 !text-gray-500 dark:!border-white/5 dark:!bg-gray-900 dark:!text-gray-400;
}
.flatpickr-time input {
  background-color: #f9fafb !important;
}
.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  @apply stroke-brand-500;
}
.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  @apply hidden;
}
.flatpickr-current-month .cur-month,
.flatpickr-current-month input.cur-year {
  @apply !h-auto !pt-0 !text-lg !font-medium !text-gray-800 dark:!text-white/90;
}

.flatpickr-prev-month,
.flatpickr-next-month {
  @apply !p-0;
}

.flatpickr-weekdays {
  @apply mt-6 mb-4 h-auto;
}

.flatpickr-weekday {
  @apply !text-theme-sm !font-medium !text-gray-500 dark:!text-gray-400;
}

.flatpickr-day {
  @apply !text-theme-sm !flex !items-center !font-medium !text-gray-800 dark:!text-white/90 dark:hover:!border-gray-300 dark:hover:!bg-gray-900;
}
.flatpickr-day.nextMonthDay,
.flatpickr-day.prevMonthDay {
  @apply !text-gray-400;
}
.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply !top-7 dark:!fill-white dark:!text-white;
}
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  @apply !left-7;
}
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  @apply !right-7;
}
span.flatpickr-weekday,
.flatpickr-months .flatpickr-month {
  @apply dark:!fill-white dark:!text-white;
}
.flatpickr-day.inRange {
  box-shadow:
    -5px 0 0 #f9fafb,
    5px 0 0 #f9fafb !important;
  @apply dark:shadow-datepicker!;
}
.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  @apply !border-gray-50 !bg-gray-50 dark:!border-0 dark:!border-white/5 dark:!bg-white/5;
}
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.selected,
.flatpickr-day.endRange {
  @apply !text-white dark:!text-white;
}
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #465fff;
  @apply !border-brand-500 !bg-brand-500 hover:!border-brand-500 hover:!bg-brand-500;
}
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #465fff;
}

.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply hover:fill-none!;
}
.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: none !important;
}

.flatpickr-calendar.static {
  @apply right-0;
}
.flatpickr-calendar.hasTime {
  width: 300px !important;
}
.flatpickr-calendar.hasTime .flatpickr-time {
  border: transparent !important;
}
.fc .fc-view-harness {
  @apply custom-scrollbar max-w-full overflow-x-auto;
}
.fc-dayGridMonth-view.fc-view.fc-daygrid {
  @apply min-w-[718px];
}
.fc .fc-scrollgrid-section > * {
  border-right-width: 0;
  border-bottom-width: 0;
}
.fc .fc-scrollgrid {
  border-left-width: 0;
}
.fc .fc-toolbar.fc-header-toolbar {
  @apply flex-col gap-4 px-6 pt-6 sm:flex-row;
}
.fc-button-group {
  @apply gap-2;
}
.fc-button-group .fc-button {
  @apply flex h-10 w-10 items-center justify-center !rounded-lg border border-gray-200 bg-transparent hover:border-gray-200 hover:bg-gray-50 focus:shadow-none active:!border-gray-200 active:!bg-transparent active:!shadow-none dark:border-gray-800 dark:hover:border-gray-800 dark:hover:bg-gray-900 dark:active:!border-gray-800;
}

.fc-button-group .fc-button.fc-prev-button:before {
  @apply mt-1 inline-block;
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.0068 6L9.75684 12.25L16.0068 18.5' stroke='%23344054' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.fc-button-group .fc-button.fc-next-button:before {
  @apply mt-1 inline-block;
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.50684 19L15.7568 12.75L9.50684 6.5' stroke='%23344054' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.dark .fc-button-group .fc-button.fc-prev-button:before {
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.0068 6L9.75684 12.25L16.0068 18.5' stroke='%2398A2B3' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.dark .fc-button-group .fc-button.fc-next-button:before {
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.50684 19L15.7568 12.75L9.50684 6.5' stroke='%2398A2B3' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.fc-button-group .fc-button .fc-icon {
  @apply hidden;
}
.fc-addEventButton-button {
  @apply !bg-brand-500 hover:!bg-brand-600 !rounded-lg !border-0 !px-4 !py-2.5 !text-sm !font-medium focus:!shadow-none;
}
.fc-toolbar-title {
  @apply !text-lg !font-medium text-gray-800 dark:text-white/90;
}
.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child {
  @apply rounded-lg bg-gray-100 p-0.5 dark:bg-gray-900;
}
.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button {
  @apply !h-auto !w-auto rounded-md !border-0 bg-transparent !px-5 !py-2 text-sm font-medium text-gray-500 hover:text-gray-700 focus:!shadow-none dark:text-gray-400;
}
.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button.fc-button-active {
  @apply bg-white text-gray-900 dark:bg-gray-800 dark:text-white;
}
.fc-theme-standard th {
  @apply !border-x-0 border-t !border-gray-200 bg-gray-50 !text-left dark:!border-gray-800 dark:bg-gray-900;
}
.fc-theme-standard td,
.fc-theme-standard .fc-scrollgrid {
  @apply !border-gray-200 dark:!border-gray-800;
}
.fc .fc-col-header-cell-cushion {
  @apply !px-5 !py-4 text-sm font-medium text-gray-400 uppercase;
}
.fc .fc-daygrid-day.fc-day-today {
  @apply bg-transparent;
}
.fc .fc-daygrid-day {
  @apply p-2;
}
.fc .fc-daygrid-day.fc-day-today .fc-scrollgrid-sync-inner {
  @apply rounded-sm bg-gray-100 dark:bg-white/[0.03];
}
.fc .fc-daygrid-day-number {
  @apply !p-3 text-sm font-medium text-gray-700 dark:text-gray-400;
}
.fc .fc-daygrid-day-top {
  @apply !flex-row;
}
.fc .fc-day-other .fc-daygrid-day-top {
  opacity: 1;
}
.fc .fc-day-other .fc-daygrid-day-top .fc-daygrid-day-number {
  @apply text-gray-400 dark:text-white/30;
}
.event-fc-color {
  @apply rounded-lg py-2.5 pr-3 pl-4;
}
.event-fc-color .fc-event-title {
  @apply p-0 text-sm font-normal text-gray-700;
}
.fc-daygrid-event-dot {
  @apply mr-3 ml-0 h-5 w-1 rounded-sm border-none;
}
.fc-event {
  @apply focus:shadow-none;
}
.fc-daygrid-event.fc-event-start {
  @apply !ml-3;
}
.event-fc-color.fc-bg-success {
  @apply border-success-50 bg-success-50;
}
.event-fc-color.fc-bg-danger {
  @apply border-error-50 bg-error-50;
}
.event-fc-color.fc-bg-primary {
  @apply border-brand-50 bg-brand-50;
}
.event-fc-color.fc-bg-warning {
  @apply border-orange-50 bg-orange-50;
}
.event-fc-color.fc-bg-success .fc-daygrid-event-dot {
  @apply bg-success-500;
}
.event-fc-color.fc-bg-danger .fc-daygrid-event-dot {
  @apply bg-error-500;
}
.event-fc-color.fc-bg-primary .fc-daygrid-event-dot {
  @apply bg-brand-500;
}
.event-fc-color.fc-bg-warning .fc-daygrid-event-dot {
  @apply bg-orange-500;
}
.fc-direction-ltr .fc-timegrid-slot-label-frame {
  @apply px-3 py-1.5 text-left text-sm font-medium text-gray-500 dark:text-gray-400;
}
.fc .fc-timegrid-axis-cushion {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.input-date-icon::-webkit-inner-spin-button,
.input-date-icon::-webkit-calendar-picker-indicator {
  opacity: 0;
  -webkit-appearance: none;
}

.swiper-button-prev svg,
.swiper-button-next svg {
  @apply !h-auto !w-auto;
}

.carouselTwo .swiper-button-next:after,
.carouselTwo .swiper-button-prev:after,
.carouselFour .swiper-button-next:after,
.carouselFour .swiper-button-prev:after {
  @apply hidden;
}
.carouselTwo .swiper-button-next.swiper-button-disabled,
.carouselTwo .swiper-button-prev.swiper-button-disabled,
.carouselFour .swiper-button-next.swiper-button-disabled,
.carouselFour .swiper-button-prev.swiper-button-disabled {
  @apply bg-white/60 opacity-100!;
}
.carouselTwo .swiper-button-next,
.carouselTwo .swiper-button-prev,
.carouselFour .swiper-button-next,
.carouselFour .swiper-button-prev {
  @apply shadow-slider-navigation h-10 w-10 rounded-full border-[0.5px] border-white/10 bg-white/90 !text-gray-700 backdrop-blur-[10px];
}

.carouselTwo .swiper-button-prev,
.carouselFour .swiper-button-prev {
  @apply !left-3 sm:!left-4;
}

.carouselTwo .swiper-button-next,
.carouselFour .swiper-button-next {
  @apply !right-3 sm:!right-4;
}

.carouselThree .swiper-pagination,
.carouselFour .swiper-pagination {
  @apply shadow-slider-navigation !bottom-3 !left-1/2 inline-flex !w-auto -translate-x-1/2 items-center gap-1.5 rounded-[40px] border-[0.5px] border-white/10 bg-white/60 px-2 py-1.5 backdrop-blur-[10px] sm:!bottom-5;
}

.carouselThree .swiper-pagination-bullet,
.carouselFour .swiper-pagination-bullet {
  @apply shadow-theme-xs !m-0 h-2.5 w-2.5 bg-white opacity-100 duration-200 ease-in-out;
}

.carouselThree .swiper-pagination-bullet-active,
.carouselFour .swiper-pagination-bullet-active {
  @apply w-6.5 rounded-xl;
}

.form-check-input:checked ~ span {
  @apply border-brand-500 dark:border-brand-500 border-[6px];
}

.taskCheckbox:checked ~ .box span {
  @apply opacity-100;
}
.taskCheckbox:checked ~ p {
  @apply text-gray-400 line-through;
}
.taskCheckbox:checked ~ .box {
  @apply border-brand-500 bg-brand-500 dark:border-brand-500;
}

.task {
  transition: all 0.2s ease; /* Smooth transition for visual effects */
}

.task.is-dragging {
  border-radius: 0.75rem;
  box-shadow:
    0px 1px 3px 0px rgba(16, 24, 40, 0.1),
    0px 1px 2px 0px rgba(16, 24, 40, 0.06);
  opacity: 0.8;
  cursor: grabbing; /* Changes the cursor to indicate dragging */
}

.custom-calendar .fc-h-event {
  background-color: transparent;
  border: none;
  color: black;
}

.social-button {
  @apply shadow-theme-xs flex h-11 w-11 items-center justify-center gap-2 rounded-full border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200;
}

.edit-button {
  @apply shadow-theme-xs flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-800 lg:inline-flex lg:w-auto dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200;
}

/* Modal z-index fix - Higher than header z-99999 */
.el-overlay {
  z-index: 100000 !important;
}

.el-dialog {
  z-index: 100001 !important;
}

.el-message-box {
  z-index: 100002 !important;
}

.el-notification {
  z-index: 100003 !important;
}

/* ========================================= */
/* Global Modal/Dialog Styling for Dark Mode */
/* ========================================= */

/* Base Dialog Container */
.el-dialog {
  @apply rounded-xl border-0 shadow-2xl;
  @apply bg-white dark:bg-gray-900;
  @apply border border-gray-200 dark:border-gray-700;
  margin: 0 auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dialog Header */
.el-dialog__header {
  @apply border-b border-gray-200 dark:border-gray-700;
  @apply bg-transparent;
  @apply rounded-t-xl;
  margin: 0;
  padding: 1.5rem;
}

/* Dialog Title */
.el-dialog__title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
  @apply leading-6;
  line-height: 1.5;
}

/* Close Button */
.el-dialog__headerbtn {
  @apply text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300;
  @apply transition-all duration-200;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700;
  @apply flex h-8 w-8 items-center justify-center rounded-full;
  @apply focus:ring-brand-500 focus:ring-2 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800;
  top: 1.5rem;
  right: 1.5rem;
  height: 2rem;
  width: 2rem;
}

.el-dialog__close {
  @apply text-base leading-none;
  font-size: 1rem;
}

/* Dialog Body */
.el-dialog__body {
  @apply bg-white dark:bg-gray-900;
  @apply text-gray-900 dark:text-white;
  @apply max-h-[calc(100vh-200px)] overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: theme('colors.gray.400') transparent;
}

.el-dialog__body::-webkit-scrollbar {
  @apply w-2;
}

.el-dialog__body::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.el-dialog__body::-webkit-scrollbar-thumb {
  @apply rounded-full bg-gray-400 dark:bg-gray-600;
}

.el-dialog__body::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Dialog Footer */
.el-dialog__footer {
  @apply border-t border-gray-200 dark:border-gray-700;
  @apply bg-transparent;
  @apply rounded-b-xl;
  margin: 0;
  padding: 1.5rem;
}

/* Overlay */
.el-overlay {
  @apply bg-black/60 dark:bg-black/75;
  @apply backdrop-blur-sm;
  transition: opacity 0.3s ease;
}

.el-overlay-dialog {
  @apply flex items-center justify-center p-4;
  @apply min-h-screen;
}

/* Form Elements in Dialogs */
.el-dialog .el-form-item__label {
  @apply text-gray-700 dark:text-gray-300;
  @apply font-medium;
}

.el-dialog .el-form-item__error {
  margin-top: 8px !important;
  margin-bottom: 0 !important;
  padding: 6px 0 !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  color: #ef4444 !important;
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: static !important;
  display: block !important;
  width: 100% !important;
  clear: both !important;
}

.dark .el-dialog .el-form-item__error {
  color: #f87171 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Responsive Input styling in dialogs */
.el-dialog .el-input__wrapper {
  @apply bg-white dark:bg-gray-800;
  @apply border-gray-300 dark:border-gray-600;
  @apply shadow-sm;
  @apply transition-all duration-200;

  /* Mobile: Larger touch targets */
  @apply min-h-[44px];

  /* Tablet+: Standard height */
  @apply sm:min-h-[40px];
}

.el-dialog .el-input__wrapper:hover {
  @apply border-gray-400 dark:border-gray-500;
}

.el-dialog .el-input__wrapper.is-focus {
  @apply border-brand-500 dark:border-brand-400;
  @apply ring-brand-500/10 dark:ring-brand-400/10 ring-2;
}

.el-dialog .el-input__wrapper.is-error {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

.el-dialog .el-input.is-error .el-input__wrapper {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

.el-dialog .el-input__inner {
  @apply text-gray-900 dark:text-white;
  @apply placeholder:text-gray-400 dark:placeholder:text-gray-500;

  /* Mobile: Larger font for better readability */
  @apply text-base;

  /* Tablet+: Standard font size */
  @apply sm:text-sm;

  /* Mobile: Prevent zoom on focus (iOS) */
  font-size: 16px;
}

@media (min-width: 640px) {
  .el-dialog .el-input__inner {
    font-size: 0.875rem; /* 14px */
  }
}

.el-dialog .el-input__inner:disabled {
  @apply text-gray-500 dark:text-gray-400;
  @apply bg-gray-100 dark:bg-gray-700;
  @apply cursor-not-allowed;
}

/* Textarea styling */
.el-dialog .el-textarea__inner {
  @apply bg-white dark:bg-gray-800;
  @apply border-gray-300 dark:border-gray-600;
  @apply text-gray-900 dark:text-white;
  @apply placeholder:text-gray-400 dark:placeholder:text-gray-500;
}

.el-dialog .el-textarea__inner:focus {
  @apply border-brand-500 dark:border-brand-400;
  @apply ring-brand-500/10 dark:ring-brand-400/10 ring-2;
}

.el-dialog .el-textarea.is-error .el-textarea__inner {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

/* Select styling */
.el-dialog .el-select .el-input__wrapper {
  @apply bg-white dark:bg-gray-800;
}

/* Responsive Button styling in dialogs */
.el-dialog .el-button--primary {
  @apply bg-brand-500 hover:bg-brand-600 active:bg-brand-700;
  @apply border-brand-500 hover:border-brand-600 active:border-brand-700;
  @apply text-white;
  @apply shadow-lg hover:shadow-xl;
  @apply transition-all duration-200;

  /* Mobile: Larger touch targets */
  @apply min-h-[44px] px-6;

  /* Tablet+: Standard size */
  @apply sm:min-h-[40px] sm:px-4;
}

.el-dialog .el-button--default {
  @apply bg-white dark:bg-gray-800;
  @apply border-gray-300 dark:border-gray-600;
  @apply text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;
  @apply hover:border-gray-400 dark:hover:border-gray-500;
  @apply transition-all duration-200;

  /* Mobile: Larger touch targets */
  @apply min-h-[44px] px-6;

  /* Tablet+: Standard size */
  @apply sm:min-h-[40px] sm:px-4;
}

.el-dialog .el-button--text {
  @apply text-gray-600 dark:text-gray-400;
  @apply hover:text-gray-800 dark:hover:text-gray-200;
  @apply hover:bg-gray-100 dark:hover:bg-gray-800;

  /* Mobile: Larger touch targets */
  @apply min-h-[44px] px-4;

  /* Tablet+: Standard size */
  @apply sm:min-h-[36px] sm:px-3;
}

/* Mobile: Stack buttons vertically in footer */
@media (max-width: 639px) {
  .el-dialog__footer {
    @apply space-y-3;
  }

  .el-dialog__footer .el-button {
    @apply w-full;
  }

  .el-dialog__footer .el-button + .el-button {
    @apply ml-0;
  }
}

/* Tablet+: Horizontal button layout */
@media (min-width: 640px) {
  .el-dialog__footer {
    @apply space-y-0 space-x-3;
  }

  .el-dialog__footer .el-button {
    @apply w-auto;
  }
}

/* Switch styling */
.el-dialog .el-switch.is-checked .el-switch__core {
  @apply bg-brand-500 dark:bg-brand-400;
  @apply border-brand-500 dark:border-brand-400;
}

.el-dialog .el-switch__core {
  @apply bg-gray-300 dark:bg-gray-600;
  @apply border-gray-300 dark:border-gray-600;
}

/* Checkbox styling */
.el-dialog .el-checkbox__input.is-checked .el-checkbox__inner {
  @apply bg-brand-500 dark:bg-brand-400;
  @apply border-brand-500 dark:border-brand-400;
}

.el-dialog .el-checkbox__inner {
  @apply bg-white dark:bg-gray-800;
  @apply border-gray-300 dark:border-gray-600;
}

.el-dialog .el-checkbox__label {
  @apply text-gray-700 dark:text-gray-300;
}

/* Radio styling */
.el-dialog .el-radio__input.is-checked .el-radio__inner {
  @apply bg-brand-500 dark:bg-brand-400;
  @apply border-brand-500 dark:border-brand-400;
}

.el-dialog .el-radio__inner {
  @apply bg-white dark:bg-gray-800;
  @apply border-gray-300 dark:border-gray-600;
}

.el-dialog .el-radio__label {
  @apply text-gray-700 dark:text-gray-300;
}

/* Upload styling */
.el-dialog .el-upload {
  @apply border-gray-300 dark:border-gray-600;
  @apply bg-white dark:bg-gray-800;
}

.el-dialog .el-upload:hover {
  @apply border-brand-500 dark:border-brand-400;
}

.el-dialog .el-upload-dragger {
  @apply bg-gray-50 dark:bg-gray-800;
  @apply border-gray-300 dark:border-gray-600;
  @apply text-gray-600 dark:text-gray-400;
}

.el-dialog .el-upload-dragger:hover {
  @apply border-brand-500 dark:border-brand-400;
  @apply text-brand-600 dark:text-brand-400;
}

/* Date picker styling */
.el-dialog .el-date-editor .el-input__wrapper {
  @apply bg-white dark:bg-gray-800;
}

/* Tree styling */
.el-dialog .el-tree {
  @apply bg-white dark:bg-gray-900;
  @apply text-gray-900 dark:text-white;
}

.el-dialog .el-tree-node__content {
  @apply hover:bg-gray-100 dark:hover:bg-gray-800;
}

.el-dialog .el-tree-node__content:hover {
  @apply text-gray-900 dark:text-white;
}

/* Table styling in dialogs */
.el-dialog .el-table {
  @apply bg-white dark:bg-gray-900;
}

.el-dialog .el-table th.el-table__cell {
  @apply bg-gray-50 dark:bg-gray-800;
  @apply text-gray-700 dark:text-gray-300;
  @apply border-gray-200 dark:border-gray-700;
}

.el-dialog .el-table td.el-table__cell {
  @apply border-gray-200 dark:border-gray-700;
  @apply text-gray-900 dark:text-white;
}

.el-dialog .el-table tr {
  @apply bg-white dark:bg-gray-900;
}

.el-dialog .el-table tr:hover td {
  @apply bg-gray-50 dark:bg-gray-800;
}

/* Tabs styling */
.el-dialog .el-tabs__header {
  @apply border-gray-200 dark:border-gray-700;
}

.el-dialog .el-tabs__item {
  @apply text-gray-600 dark:text-gray-400;
}

.el-dialog .el-tabs__item.is-active {
  @apply text-brand-500 dark:text-brand-400;
}

.el-dialog .el-tabs__active-bar {
  @apply bg-brand-500 dark:bg-brand-400;
}

/* ========================================== */
/* Enhanced Responsive Design for All Dialogs */
/* ========================================== */

/* Mobile-first responsive breakpoints */
@media (max-width: 639px) {
  /* Mobile: Full screen modal approach */
  .el-dialog {
    @apply fixed inset-0 h-full max-h-full w-full;
    @apply rounded-none border-0;
    @apply flex flex-col;
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .el-dialog__header {
    @apply flex-shrink-0 px-4 py-4;
    @apply rounded-none bg-transparent;
    /* Add safe area for notched devices */
    padding-top: max(1rem, env(safe-area-inset-top, 1rem));
  }

  .el-dialog__body {
    @apply min-h-0 flex-1 p-4;
    @apply overflow-y-auto;
    /* iOS momentum scrolling */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .el-dialog__footer {
    @apply flex-shrink-0 px-4 py-4;
    @apply rounded-none bg-transparent;
    /* Add safe area for home indicator */
    padding-bottom: max(1rem, env(safe-area-inset-bottom, 1rem));
  }

  .el-dialog__headerbtn {
    @apply h-11 w-11;
    @apply top-4 right-4;
    min-width: 44px !important;
    min-height: 44px !important;
    top: max(1rem, env(safe-area-inset-top, 1rem)) !important;
  }

  .el-dialog__close {
    @apply text-xl;
  }

  /* Mobile overlay adjustments */
  .el-overlay {
    @apply bg-black/40 dark:bg-black/60;
  }

  .el-overlay-dialog {
    @apply items-stretch justify-stretch p-0;
  }
}

/* Small tablets (640px-767px): Bottom sheet style */
@media (min-width: 640px) and (max-width: 767px) {
  .el-dialog {
    @apply fixed inset-x-0 top-auto bottom-0;
    @apply h-auto max-h-[85vh] min-h-[50vh];
    @apply rounded-t-2xl border-x border-t border-gray-200 dark:border-gray-700;
    @apply flex flex-col;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    @apply bg-transparent px-6 py-4;
    @apply rounded-t-2xl;
    position: relative;
  }

  /* Drag handle for bottom sheet */
  .el-dialog__header::before {
    content: '';
    @apply absolute top-2 left-1/2 -translate-x-1/2 transform;
    @apply h-1 w-12 rounded-full bg-gray-300 dark:bg-gray-600;
  }

  .el-dialog__body {
    @apply min-h-0 flex-1 overflow-y-auto;
    @apply max-h-[calc(85vh-120px)];
  }

  .el-dialog__footer {
    @apply bg-transparent px-6 py-4;
    @apply flex-shrink-0;
  }

  .el-dialog__headerbtn {
    @apply top-4 right-6 h-10 w-10;
    min-width: 40px !important;
    min-height: 40px !important;
  }

  .el-dialog__close {
    @apply text-lg;
  }

  .el-overlay {
    @apply bg-black/50 dark:bg-black/70;
  }

  .el-overlay-dialog {
    @apply items-end justify-stretch p-0;
  }
}

/* Medium tablets and up (768px+): Centered modal */
@media (min-width: 768px) {
  .el-dialog {
    width: var(--el-dialog-width, clamp(400px, 50vw, 600px)) !important;
    max-width: calc(100vw - 4rem) !important;
    @apply relative h-auto max-h-[90vh] w-auto;
    @apply rounded-xl border border-gray-200 dark:border-gray-700;
    @apply flex-none;
    margin: 0 auto !important;
  }

  .el-dialog__header {
    @apply bg-transparent px-8 py-6;
    @apply rounded-t-xl;
  }

  .el-dialog__body {
    @apply max-h-[calc(90vh-160px)] p-0;
    @apply flex-none overflow-y-auto;
  }

  .el-dialog__footer {
    @apply bg-transparent px-8 py-6;
    @apply rounded-b-xl;
  }

  .el-dialog__headerbtn {
    @apply top-6 right-8 h-8 w-8;
    min-width: 2rem !important;
    min-height: 2rem !important;
  }

  .el-dialog__close {
    @apply text-base;
  }

  .el-overlay {
    @apply bg-black/60 dark:bg-black/75;
  }

  .el-overlay-dialog {
    @apply items-center justify-center p-4;
  }
}

/* Large screens (1024px+): Desktop optimization */
@media (min-width: 1024px) {
  .el-dialog {
    width: var(--el-dialog-width, clamp(500px, 45vw, 700px)) !important;
    max-width: calc(100vw - 6rem) !important;
  }

  .el-dialog__header {
    @apply bg-transparent px-10 py-7;
  }

  .el-dialog__footer {
    @apply bg-transparent px-10 py-7;
  }

  .el-dialog__headerbtn {
    @apply top-7 right-10;
  }
}

/* Extra large screens (1280px+): Prevent excessive width */
@media (min-width: 1280px) {
  .el-dialog {
    width: var(--el-dialog-width, clamp(600px, 40vw, 800px)) !important;
    max-width: 1000px !important;
  }
}

/* Ultra-wide screens (1536px+): Maximum constraints */
@media (min-width: 1536px) {
  .el-dialog {
    width: var(--el-dialog-width, clamp(700px, 35vw, 900px)) !important;
    max-width: 1200px !important;
  }
}

/* ========================================== */
/* Global Form Error Styling                 */
/* ========================================== */

/* Global form item error styling - Simple and clean */
.el-form-item__error {
  margin-top: 8px !important;
  margin-bottom: 0 !important;
  padding: 6px 0 !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  color: #ef4444 !important;
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: static !important;
  display: block !important;
  width: 100% !important;
  clear: both !important;
}

/* Dark mode error styling */
.dark .el-form-item__error {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Global input error styling */
.el-input.is-error .el-input__wrapper,
.el-input__wrapper.is-error {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

/* Global textarea error styling */
.el-textarea.is-error .el-textarea__inner {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

/* Global select error styling */
.el-select.is-error .el-input__wrapper {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

/* Global form validation messages */
.el-form-item.is-error .el-input__wrapper {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

.el-form-item.is-error .el-textarea__inner {
  @apply border-red-500 dark:border-red-400;
  @apply ring-2 ring-red-500/10 dark:ring-red-400/10;
}

/* Error icon styling */
.el-form-item.is-error .el-input__suffix-inner {
  @apply text-red-500 dark:text-red-400;
}

/* Custom error class for additional styling */
.form-error {
  @apply text-red-600 dark:text-red-400;
  @apply text-sm font-medium;
  @apply mt-1 flex items-center gap-1;
}

.form-error::before {
  content: '⚠';
  @apply text-red-600 dark:text-red-400;
}

/* ========================================== */
/* Optimized Modal System                     */
/* ========================================== */

/* CSS Custom Properties for Modal Configuration */
:root {
  /* Modal spacing */
  --modal-padding: 1.5rem;
  --modal-border-radius: 0.75rem;
  --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Responsive breakpoints */
  --modal-mobile-max: 639px;
  --modal-tablet-min: 640px;
  --modal-tablet-max: 767px;
  --modal-desktop-min: 768px;

  /* Animation timing */
  --modal-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Universal Modal Base */
.el-dialog,
.custom-dialog {
  @apply bg-white dark:bg-gray-900;
  @apply border border-gray-200 dark:border-gray-700;
  box-shadow: var(--modal-shadow);
  border-radius: var(--modal-border-radius);
  transition: var(--modal-transition);
  margin: 0;

  /* Mobile: Full screen */
  @apply fixed inset-0 h-full w-full;
  @apply rounded-none border-0;

  /* Tablet: Bottom sheet */
  @apply sm:fixed sm:inset-x-0 sm:top-auto sm:bottom-0;
  @apply sm:h-auto sm:max-h-[85vh] sm:w-full;
  @apply sm:rounded-t-xl sm:border-x sm:border-t;

  /* Desktop: Centered modal */
  @apply md:relative md:inset-auto md:h-auto md:w-auto;
  @apply md:max-h-[90vh] md:max-w-[90vw];
  @apply md:rounded-xl md:border;
}

/* Modal Header */
.el-dialog__header {
  @apply bg-transparent;
  @apply border-b border-gray-200 dark:border-gray-700;
  @apply px-6 py-4;
  @apply flex items-center justify-between;
  margin: 0;

  /* Mobile: Add safe area */
  padding-top: max(1rem, env(safe-area-inset-top, 1rem));

  /* Tablet+: Standard padding */
  @apply sm:pt-4;
}

/* Modal Title */
.el-dialog__title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
  @apply sm:text-xl;
  @apply pr-10; /* Space for close button */
}

/* Close Button */
.el-dialog__headerbtn {
  @apply flex h-10 w-10 items-center justify-center;
  @apply text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700;
  @apply rounded-full transition-colors;
  @apply focus:ring-brand-500 focus:ring-2 focus:ring-offset-2 focus:outline-none;
  @apply top-4 right-4;

  /* Mobile: Larger touch target */
  @apply sm:h-8 sm:w-8;
}

/* Modal Body */
.el-dialog__body {
  @apply p-0;
  @apply overflow-y-auto;
  @apply flex-1;

  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175) transparent;
}

.el-dialog__body::-webkit-scrollbar {
  @apply w-1;
}

.el-dialog__body::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.el-dialog__body::-webkit-scrollbar-thumb {
  @apply rounded-full bg-gray-400 dark:bg-gray-600;
}

/* Modal Footer */
.el-dialog__footer {
  @apply bg-transparent;
  @apply border-t border-gray-200 dark:border-gray-700;
  @apply px-6 py-4;
  margin: 0;

  /* Mobile: Add safe area */
  padding-bottom: max(1rem, env(safe-area-inset-bottom, 1rem));

  /* Tablet+: Standard padding */
  @apply sm:pb-4;
}

/* Content-Aware Sizing */
.el-dialog[data-size='compact'] {
  @apply md:w-96 md:max-w-md;
}

.el-dialog[data-size='large'] {
  @apply md:w-full md:max-w-4xl;
}

.el-dialog[data-size='auto'] {
  @apply md:w-auto md:max-w-2xl md:min-w-96;
}

/* Height Variants */
.el-dialog[data-height='compact'] {
  @apply md:max-h-96;
}

.el-dialog[data-height='full'] {
  @apply md:h-[90vh];
}

/* Responsive Animations */
@media (max-width: 767px) {
  .dialog-fade-enter-from,
  .dialog-fade-leave-to {
    opacity: 0;
    transform: translateY(100%);
  }
}

@media (min-width: 768px) {
  .dialog-fade-enter-from,
  .dialog-fade-leave-to {
    opacity: 0;
    transform: scale(0.95) translateY(-1.25rem);
  }
}

.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: var(--modal-transition);
}

.dialog-fade-enter-to,
.dialog-fade-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .dialog-fade-enter-active,
  .dialog-fade-leave-active {
    transition: opacity 0.2s ease;
  }

  .dialog-fade-enter-from,
  .dialog-fade-leave-to {
    opacity: 0;
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .el-dialog {
    @apply border-2 border-gray-900 dark:border-white;
  }
}

/* ========================================== */
/* End of Optimized Modal System             */
/* ========================================== */

/* ========================================== */
/* Element Plus Dark Mode Support             */
/* ========================================== */

/* CSS Variables for Element Plus Dark Mode */
:root {
  /* Light mode variables */
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f2f3f5;
  --el-bg-color-overlay: #ffffff;
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #a8abb2;
  --el-text-color-disabled: #c0c4cc;
  --el-border-color: #dcdfe6;
  --el-border-color-light: #e4e7ed;
  --el-border-color-lighter: #ebeef5;
  --el-border-color-extra-light: #f2f6fc;
  --el-border-color-dark: #d4d7de;
  --el-border-color-hover: #c0c4cc;
  --el-fill-color: #f0f2f5;
  --el-fill-color-light: #f5f7fa;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-dark: #ebedf0;
  --el-fill-color-darker: #e6e8eb;
  --el-color-primary: #409eff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;
  --el-color-success: #67c23a;
  --el-color-success-light-3: #95d475;
  --el-color-success-light-5: #b3e19d;
  --el-color-success-light-7: #d1edc4;
  --el-color-success-light-8: #e1f3d8;
  --el-color-success-light-9: #f0f9eb;
  --el-color-success-dark-2: #529b2e;
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: #eebe77;
  --el-color-warning-light-5: #f3d19e;
  --el-color-warning-light-7: #f8e3c5;
  --el-color-warning-light-8: #faecd8;
  --el-color-warning-light-9: #fdf6ec;
  --el-color-warning-dark-2: #b88230;
  --el-color-danger: #f56c6c;
  --el-color-danger-light-3: #f78989;
  --el-color-danger-light-5: #f9a7a7;
  --el-color-danger-light-7: #fbc4c4;
  --el-color-danger-light-8: #fcd3d3;
  --el-color-danger-light-9: #fef0f0;
  --el-color-danger-dark-2: #c45656;
  --el-color-error: #f56c6c;
  --el-color-error-light-3: #f78989;
  --el-color-error-light-5: #f9a7a7;
  --el-color-error-light-7: #fbc4c4;
  --el-color-error-light-8: #fcd3d3;
  --el-color-error-light-9: #fef0f0;
  --el-color-error-dark-2: #c45656;
  --el-color-info: #909399;
  --el-color-info-light-3: #b1b3b8;
  --el-color-info-light-5: #c8c9cc;
  --el-color-info-light-7: #dedfe0;
  --el-color-info-light-8: #e9e9eb;
  --el-color-info-light-9: #f4f4f5;
  --el-color-info-dark-2: #73767a;
  --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
  --el-box-shadow-dark:
    0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);
  --el-disabled-bg-color: #f5f7fa;
  --el-disabled-text-color: #a8abb2;
  --el-disabled-border-color: #e4e7ed;
  --el-overlay-color: rgba(255, 255, 255, 0.9);
  --el-overlay-color-light: rgba(255, 255, 255, 0.7);
  --el-overlay-color-lighter: rgba(255, 255, 255, 0.3);
  --el-mask-color: rgba(255, 255, 255, 0.9);
  --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
  --el-border-width: 1px;
  --el-border-style: solid;
  --el-border-color-hover: var(--el-text-color-placeholder);
  --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);
  --el-svg-monochrome-grey: #dcdde0;
}

/* Dark mode variables */
.dark {
  --el-bg-color: #1e2636;
  --el-bg-color-page: #1e2636;
  --el-bg-color-overlay: #1e2636;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-border-color-dark: #58585b;
  --el-border-color-hover: #636466;
  --el-fill-color: #2a3441;
  --el-fill-color-light: #2a3441;
  --el-fill-color-lighter: #1e2636;
  --el-fill-color-extra-light: #1a2231;
  --el-fill-color-dark: #374151;
  --el-fill-color-darker: #424243;
  --el-color-primary: #409eff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;
  --el-color-success: #67c23a;
  --el-color-success-light-3: #95d475;
  --el-color-success-light-5: #b3e19d;
  --el-color-success-light-7: #d1edc4;
  --el-color-success-light-8: #e1f3d8;
  --el-color-success-light-9: #f0f9eb;
  --el-color-success-dark-2: #529b2e;
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: #eebe77;
  --el-color-warning-light-5: #f3d19e;
  --el-color-warning-light-7: #f8e3c5;
  --el-color-warning-light-8: #faecd8;
  --el-color-warning-light-9: #fdf6ec;
  --el-color-warning-dark-2: #b88230;
  --el-color-danger: #f56c6c;
  --el-color-danger-light-3: #f78989;
  --el-color-danger-light-5: #f9a7a7;
  --el-color-danger-light-7: #fbc4c4;
  --el-color-danger-light-8: #fcd3d3;
  --el-color-danger-light-9: #fef0f0;
  --el-color-danger-dark-2: #c45656;
  --el-color-error: #f56c6c;
  --el-color-error-light-3: #f78989;
  --el-color-error-light-5: #f9a7a7;
  --el-color-error-light-7: #fbc4c4;
  --el-color-error-light-8: #fcd3d3;
  --el-color-error-light-9: #fef0f0;
  --el-color-error-dark-2: #c45656;
  --el-color-info: #909399;
  --el-color-info-light-3: #b1b3b8;
  --el-color-info-light-5: #c8c9cc;
  --el-color-info-light-7: #dedfe0;
  --el-color-info-light-8: #e9e9eb;
  --el-color-info-light-9: #f4f4f5;
  --el-color-info-dark-2: #73767a;
  --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.36), 0px 8px 20px rgba(0, 0, 0, 0.72);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.72);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.72);
  --el-box-shadow-dark:
    0px 16px 48px 16px rgba(0, 0, 0, 0.72), 0px 12px 32px rgba(0, 0, 0, 0.72), 0px 8px 16px -8px rgba(0, 0, 0, 0.72);
  --el-disabled-bg-color: #2a3441;
  --el-disabled-text-color: #6c6e72;
  --el-disabled-border-color: #414243;
  --el-overlay-color: rgba(0, 0, 0, 0.8);
  --el-overlay-color-light: rgba(0, 0, 0, 0.7);
  --el-overlay-color-lighter: rgba(0, 0, 0, 0.5);
  --el-mask-color: rgba(0, 0, 0, 0.8);
  --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);
  --el-border-width: 1px;
  --el-border-style: solid;
  --el-border-color-hover: var(--el-text-color-placeholder);
  --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);
  --el-svg-monochrome-grey: #dcdde0;
}

/* ========================================== */
/* Element Plus Component Overrides           */
/* ========================================== */

/* Select Dropdown Styling */
.dark .el-select-dropdown {
  background-color: #1e2636 !important;
  border: 1px solid #414243 !important;
  box-shadow:
    0px 12px 32px 4px rgba(0, 0, 0, 0.36),
    0px 8px 20px rgba(0, 0, 0, 0.72) !important;
  border-radius: 8px !important;
}

.dark .el-select-dropdown .el-select-dropdown__item {
  color: #e5eaf3 !important;
  background-color: transparent !important;
  border-radius: 4px !important;
  margin: 2px 4px !important;
  transition: all 0.2s ease !important;
}

.dark .el-select-dropdown .el-select-dropdown__item:hover {
  background-color: #2a3441 !important;
  color: #e5eaf3 !important;
}

.dark .el-select-dropdown .el-select-dropdown__item.selected {
  background-color: #409eff !important;
  color: white !important;
  font-weight: 500 !important;
}

.dark .el-select-dropdown .el-select-dropdown__item.selected:hover {
  background-color: #337ecc !important;
  color: white !important;
}

.dark .el-select-dropdown .el-select-dropdown__item.is-disabled {
  color: #6c6e72 !important;
  background-color: transparent !important;
  cursor: not-allowed !important;
}

/* Select Input Styling */
.dark .el-select .el-input__wrapper {
  background-color: #1e2636 !important;
  border-color: #4c4d4f !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.dark .el-select .el-input__wrapper:hover {
  border-color: #636466 !important;
}

.dark .el-select .el-input__wrapper.is-focus {
  border-color: #409eff !important;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.12) !important;
}

.dark .el-select .el-input__inner {
  color: #e5eaf3 !important;
  background-color: transparent !important;
}

.dark .el-select .el-input__inner::placeholder {
  color: #8d9095 !important;
}

.dark .el-select .el-input__suffix {
  color: #a3a6ad !important;
}

.dark .el-select .el-input__suffix:hover {
  color: #e5eaf3 !important;
}

/* Select Arrow Icon */
.dark .el-select .el-input__suffix .el-select__caret {
  color: #a3a6ad !important;
  transition: all 0.2s ease !important;
}

.dark .el-select .el-input__suffix .el-select__caret.is-reverse {
  color: #409eff !important;
}

/* Select Dropdown Empty State */
.dark .el-select-dropdown .el-select-dropdown__empty {
  color: #a3a6ad !important;
  padding: 12px 16px !important;
}

/* Select Dropdown Loading State */
.dark .el-select-dropdown .el-select-dropdown__loading {
  color: #a3a6ad !important;
  padding: 12px 16px !important;
}

/* Select Dropdown Scrollbar */
.dark .el-select-dropdown .el-scrollbar__wrap {
  background-color: #1e2636 !important;
}

.dark .el-select-dropdown .el-scrollbar__thumb {
  background-color: #4c4d4f !important;
  border-radius: 4px !important;
}

.dark .el-select-dropdown .el-scrollbar__thumb:hover {
  background-color: #636466 !important;
}

/* ========================================== */
/* End of Element Plus Dark Mode Support     */
/* ========================================== */

/* ========================================== */
/* Enhanced Element Plus Select Component Styling */
/* ========================================== */

/* Base Input Styling (Light Mode) */
.el-input__wrapper {
  @apply rounded-lg border border-gray-300 bg-white;
  @apply transition-all duration-200 ease-in-out;
  @apply px-3 py-2.5;
  @apply min-h-[40px];
  @apply flex items-center;
  @apply shadow-sm;
}

.el-input__wrapper:hover {
  @apply border-gray-400;
}

.el-input__wrapper.is-focus {
  @apply border-brand-500;
  @apply ring-brand-500/10 ring-2;
  @apply shadow-sm;
}

.el-input__wrapper.is-error {
  @apply border-red-500;
  @apply ring-2 ring-red-500/10;
}

.el-input__inner {
  @apply text-gray-900;
  @apply bg-transparent;
  @apply border-none outline-none;
  @apply h-full w-full;
  @apply text-sm;
  @apply placeholder:text-gray-400;
}

.el-input__inner:disabled {
  @apply text-gray-500;
  @apply bg-gray-50;
  @apply cursor-not-allowed;
}

/* Select Component Styling - Matching Input */
.el-select .el-select__wrapper {
  @apply rounded-lg border border-gray-300 bg-white;
  @apply transition-all duration-200 ease-in-out;
  @apply px-3 py-2.5;
  @apply min-h-[40px];
  @apply flex items-center;
  @apply shadow-sm;
  @apply cursor-pointer;
}

.el-select .el-select__wrapper:hover {
  @apply border-gray-400;
}

.el-select .el-select__wrapper.is-focus {
  @apply border-brand-500;
  @apply ring-brand-500/10 ring-2;
  @apply shadow-sm;
}

.el-select .el-select__wrapper.is-error {
  @apply border-red-500;
  @apply ring-2 ring-red-500/10;
}

.el-select .el-select__input {
  @apply text-gray-900;
  @apply bg-transparent;
  @apply border-none outline-none;
  @apply h-full w-full;
  @apply text-sm;
  @apply placeholder:text-gray-400;
  @apply cursor-pointer;
}

.el-select .el-select__input:disabled {
  @apply text-gray-500;
  @apply bg-gray-50;
  @apply cursor-not-allowed;
}

/* Select Arrow Icon */
.el-select .el-select__suffix {
  @apply text-gray-400;
  @apply flex items-center;
  @apply ml-2;
  @apply transition-all duration-200;
}

.el-select .el-select__suffix:hover {
  @apply text-gray-600;
}

.el-select .el-select__suffix .el-select__caret {
  @apply text-gray-400;
  @apply transition-all duration-200;
  @apply text-xs;
}

.el-select .el-select__suffix .el-select__caret.is-reverse {
  @apply text-brand-500;
  @apply rotate-180;
}

/* Select Dropdown Styling */
.el-select-dropdown {
  @apply bg-white;
  @apply border border-gray-200;
  @apply rounded-lg;
  @apply shadow-lg;
  @apply p-1;
  @apply max-h-[300px];
  @apply overflow-y-auto;
  @apply z-[9999];
}

.el-select-dropdown .el-select-dropdown__item {
  @apply text-gray-900;
  @apply bg-transparent;
  @apply rounded-md;
  @apply px-3 py-2;
  @apply transition-all duration-200;
  @apply text-sm;
  @apply cursor-pointer;
  @apply hover:bg-gray-50;
  @apply hover:text-gray-900;
  @apply m-0.5;
}

.el-select-dropdown .el-select-dropdown__item.selected {
  @apply bg-brand-500;
  @apply text-white;
  @apply font-medium;
}

.el-select-dropdown .el-select-dropdown__item.selected:hover {
  @apply bg-brand-600;
  @apply text-white;
}

.el-select-dropdown .el-select-dropdown__item.is-disabled {
  @apply text-gray-400;
  @apply bg-transparent;
  @apply cursor-not-allowed;
  @apply hover:bg-transparent;
}

/* Select Dropdown Empty and Loading States */
.el-select-dropdown .el-select-dropdown__empty,
.el-select-dropdown .el-select-dropdown__loading {
  @apply text-gray-500;
  @apply px-3 py-2;
  @apply text-center;
  @apply text-sm;
}

/* Select Dropdown Scrollbar */
.el-select-dropdown .el-scrollbar__wrap {
  @apply bg-white;
}

.el-select-dropdown .el-scrollbar__thumb {
  @apply bg-gray-300;
  @apply rounded-full;
}

.el-select-dropdown .el-scrollbar__thumb:hover {
  @apply bg-gray-400;
}

/* ========================================== */
/* Dark Mode Overrides                        */
/* ========================================== */

/* Dark Mode Input Styling */
.dark .el-input__wrapper {
  @apply border-gray-600 bg-gray-800;
  @apply text-gray-100;
}

.dark .el-input__wrapper:hover {
  @apply border-gray-500;
}

.dark .el-input__wrapper.is-focus {
  @apply border-brand-400;
  @apply ring-brand-400/20;
}

.dark .el-input__wrapper.is-error {
  @apply border-red-400;
  @apply ring-red-400/20;
}

.dark .el-input__inner {
  @apply text-gray-100;
  @apply placeholder:text-gray-500;
}

.dark .el-input__inner:disabled {
  @apply text-gray-500;
  @apply bg-gray-700;
}

/* Dark Mode Select Styling */
.dark .el-select .el-select__wrapper {
  @apply border-gray-600 bg-gray-800;
  @apply text-gray-100;
}

.dark .el-select .el-select__wrapper:hover {
  @apply border-gray-500;
}

.dark .el-select .el-select__wrapper.is-focus {
  @apply border-brand-400;
  @apply ring-brand-400/20;
}

.dark .el-select .el-select__wrapper.is-error {
  @apply border-red-400;
  @apply ring-red-400/20;
}

.dark .el-select .el-select__input {
  @apply text-gray-100;
  @apply placeholder:text-gray-500;
}

.dark .el-select .el-select__input:disabled {
  @apply text-gray-500;
  @apply bg-gray-700;
}

/* Dark Mode Select Arrow */
.dark .el-select .el-select__suffix {
  @apply text-gray-400;
}

.dark .el-select .el-select__suffix:hover {
  @apply text-gray-300;
}

.dark .el-select .el-select__suffix .el-select__caret {
  @apply text-gray-400;
}

.dark .el-select .el-select__suffix .el-select__caret.is-reverse {
  @apply text-brand-400;
}

/* Dark Mode Select Dropdown */
.dark .el-select-dropdown {
  @apply bg-gray-800;
  @apply border-gray-600;
  @apply shadow-xl;
}

.dark .el-select-dropdown .el-select-dropdown__item {
  @apply text-gray-100;
  @apply hover:bg-gray-700;
  @apply hover:text-gray-100;
}

.dark .el-select-dropdown .el-select-dropdown__item.selected {
  @apply bg-brand-500;
  @apply text-white;
}

.dark .el-select-dropdown .el-select-dropdown__item.selected:hover {
  @apply bg-brand-600;
  @apply text-white;
}

.dark .el-select-dropdown .el-select-dropdown__item.is-disabled {
  @apply text-gray-500;
  @apply hover:bg-transparent;
}

/* Dark Mode Select Dropdown States */
.dark .el-select-dropdown .el-select-dropdown__empty,
.dark .el-select-dropdown .el-select-dropdown__loading {
  @apply text-gray-400;
}

/* Dark Mode Select Dropdown Scrollbar */
.dark .el-select-dropdown .el-scrollbar__wrap {
  @apply bg-gray-800;
}

.dark .el-select-dropdown .el-scrollbar__thumb {
  @apply bg-gray-600;
}

.dark .el-select-dropdown .el-scrollbar__thumb:hover {
  @apply bg-gray-500;
}

/* ========================================== */
/* Responsive Design                          */
/* ========================================== */

/* Mobile Optimizations */
@media (max-width: 639px) {
  .el-input__wrapper,
  .el-select .el-select__wrapper {
    @apply min-h-[44px];
    @apply text-base;
  }

  .el-input__inner,
  .el-select .el-select__input {
    @apply text-base;
  }

  .el-select-dropdown {
    @apply max-h-[250px];
  }
}

/* Tablet and Desktop */
@media (min-width: 640px) {
  .el-input__wrapper,
  .el-select .el-select__wrapper {
    @apply h-[40px];
  }

  .el-input__inner,
  .el-select .el-select__input {
    @apply text-sm;
  }
}

/* ========================================== */
/* Focus States and Accessibility             */
/* ========================================== */

/* Focus ring for keyboard navigation */
.el-input__wrapper:focus-within,
.el-select .el-select__wrapper:focus-within {
  @apply outline-none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .el-input__wrapper,
  .el-select .el-select__wrapper {
    @apply border-2;
  }

  .el-input__wrapper.is-focus,
  .el-select .el-select__wrapper.is-focus {
    @apply border-brand-600;
    @apply ring-0;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .el-input__wrapper,
  .el-select .el-select__wrapper,
  .el-select-dropdown .el-select-dropdown__item,
  .el-select .el-select__suffix .el-select__caret {
    @apply transition-none;
  }
}

/* ========================================== */
/* Error States                               */
/* ========================================== */

/* Error styling for select components */
.el-select.is-error .el-select__wrapper {
  @apply border-red-500;
  @apply ring-2 ring-red-500/10;
}

.dark .el-select.is-error .el-select__wrapper {
  @apply border-red-400;
  @apply ring-red-400/20;
}

/* Error message styling */
.el-form-item.is-error .el-select .el-select__wrapper {
  @apply border-red-500;
  @apply ring-2 ring-red-500/10;
}

.dark .el-form-item.is-error .el-select .el-select__wrapper {
  @apply border-red-400;
  @apply ring-red-400/20;
}

/* ========================================== */
/* Loading States                             */
/* ========================================== */

/* Loading state for select */
.el-select.is-loading .el-select__suffix {
  @apply text-gray-400;
}

.dark .el-select.is-loading .el-select__suffix {
  @apply text-gray-500;
}

/* ========================================== */
/* Multiple Select Support                    */
/* ========================================== */

/* Multiple select tags */
.el-select .el-select__tags {
  @apply flex flex-wrap gap-1;
  @apply mt-1;
}

.el-select .el-select__tags .el-tag {
  @apply bg-brand-100 text-brand-700;
  @apply border-brand-200 border;
  @apply rounded-md;
  @apply px-2 py-1;
  @apply text-xs;
  @apply flex items-center gap-1;
}

.dark .el-select .el-select__tags .el-tag {
  @apply bg-brand-500/20 text-brand-300;
  @apply border-brand-500/30;
}

/* Tag close button */
.el-select .el-select__tags .el-tag .el-tag__close {
  @apply text-brand-600;
  @apply hover:text-brand-800;
  @apply hover:bg-brand-200;
  @apply rounded-full;
  @apply p-0.5;
}

.dark .el-select .el-select__tags .el-tag .el-tag__close {
  @apply text-brand-300;
  @apply hover:text-brand-100;
  @apply hover:bg-brand-500/30;
}

/* ========================================== */
/* End of Enhanced Element Plus Select Styling */
/* ========================================== */