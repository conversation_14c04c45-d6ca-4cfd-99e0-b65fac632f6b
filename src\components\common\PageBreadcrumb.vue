<template>
  <div class="mb-6 flex flex-wrap items-center justify-between gap-3">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white/90">
      {{ props.pageTitle }}
    </h2>
    <nav>
      <ol class="flex items-center gap-1.5">
        <li v-for="(item, index) in breadcrumbItems" :key="index">
          <router-link
            v-if="item.to && index < breadcrumbItems.length - 1"
            class="inline-flex items-center gap-1.5 text-sm text-gray-500 dark:text-gray-400"
            :to="item.to"
          >
            {{ item.label }}
            <inline-svg :src="breadcrumbIcon" />
          </router-link>
          <span v-else class="text-sm text-gray-800 dark:text-white/90">
            {{ item.label }}
          </span>
        </li>
      </ol>
    </nav>
  </div>
</template>

<script setup>
import breadcrumbIcon from '@/assets/images/icons/breadcrumb.svg'
import { defineProps, computed } from 'vue'

const props = defineProps({
  pageTitle: String,
  breadcrumbs: {
    type: Array,
    default: () => [],
  },
})

const breadcrumbItems = computed(() => {
  const customBreadcrumbs = props.breadcrumbs || []
  const currentPage = { label: props.pageTitle }

  return [...customBreadcrumbs, currentPage]
})
</script>
