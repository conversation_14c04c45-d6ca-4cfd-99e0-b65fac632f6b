<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa Bài viết' : 'Tạo Bài viết Mới'"
    width="1000px"
    :before-close="handleClose"
    :z-index="100000"
    append-to-body
    class="post-form-modal"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="140px" label-position="top">
      <el-row :gutter="20">
        <!-- Left Column -->
        <el-col :span="16">
          <!-- Basic Information -->
          <div class="form-section">
            <h3 class="section-title">Thông tin cơ bản</h3>

            <el-form-item label="Tiêu đề bài viết" prop="title">
              <el-input v-model="formData.title" placeholder="Nhập tiêu đề bài viết" maxlength="255" show-word-limit />
            </el-form-item>

            <el-form-item label="Tóm tắt" prop="excerpt">
              <el-input
                v-model="formData.excerpt"
                type="textarea"
                :rows="3"
                placeholder="Nhập tóm tắt ngắn gọn về bài viết"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="Nội dung" prop="body">
              <div class="editor-container">
                <el-input
                  v-model="formData.body"
                  type="textarea"
                  :rows="15"
                  placeholder="Nhập nội dung bài viết (hỗ trợ HTML)"
                />
                <div class="editor-help">Hỗ trợ HTML. Trong tương lai sẽ tích hợp rich text editor.</div>
              </div>
            </el-form-item>
          </div>

          <!-- SEO Information -->
          <div class="form-section">
            <h3 class="section-title">Thông tin SEO</h3>

            <el-form-item label="Meta Title" prop="meta_title">
              <el-input
                v-model="formData.meta_title"
                placeholder="Tiêu đề SEO (để trống sẽ dùng tiêu đề chính)"
                maxlength="255"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="Meta Description" prop="meta_description">
              <el-input
                v-model="formData.meta_description"
                type="textarea"
                :rows="3"
                placeholder="Mô tả SEO (155-160 ký tự)"
                maxlength="160"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="Meta Keywords" prop="meta_keywords">
              <el-input v-model="formData.meta_keywords" placeholder="Từ khóa SEO, cách nhau bằng dấu phẩy" />
            </el-form-item>
          </div>
        </el-col>

        <!-- Right Column -->
        <el-col :span="8">
          <!-- Publishing Options -->
          <div class="form-section">
            <h3 class="section-title">Tùy chọn xuất bản</h3>

            <el-form-item label="Danh mục" prop="category_id">
              <el-select v-model="formData.category_id" placeholder="Chọn danh mục" style="width: 100%" clearable>
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="Tác giả" prop="user_id">
              <el-select v-model="formData.user_id" placeholder="Chọn tác giả" style="width: 100%">
                <el-option v-for="user in users" :key="user.id" :label="user.name" :value="user.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="Trạng thái" prop="status">
              <el-select v-model="formData.status" style="width: 100%">
                <el-option label="Bản nháp" value="draft" />
                <el-option label="Chờ duyệt" value="pending_review" />
                <el-option label="Đã xuất bản" value="published" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-checkbox v-model="formData.is_hot"> Tin hot </el-checkbox>
            </el-form-item>

            <el-form-item>
              <el-checkbox v-model="formData.show_on_homepage"> Hiển thị trên trang chủ </el-checkbox>
            </el-form-item>
          </div>

          <!-- Cover Image -->
          <div class="form-section">
            <h3 class="section-title">Ảnh bìa</h3>

            <el-form-item label="Ảnh bìa" prop="cover_image">
              <div class="image-upload">
                <div v-if="imagePreview" class="image-preview">
                  <img :src="imagePreview" alt="Preview" />
                  <div class="image-actions">
                    <el-button type="danger" size="small" @click="removeImage"> Xóa </el-button>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">Chọn ảnh bìa</div>
                </div>
                <input ref="fileInput" type="file" accept="image/*" @change="handleImageChange" style="display: none" />
                <el-button
                  type="primary"
                  size="small"
                  @click="$refs.fileInput.click()"
                  style="margin-top: 0.5rem; width: 100%"
                >
                  {{ imagePreview ? 'Thay đổi ảnh' : 'Chọn ảnh' }}
                </el-button>
              </div>
              <div class="form-help">Kích thước khuyến nghị: 800x450px. Định dạng: JPG, PNG</div>
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button @click="handleSaveDraft" :loading="loading"> Lưu nháp </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? 'Cập nhật' : 'Xuất bản' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { usePosts } from '@/composables/modules/cms/usePosts.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  post: {
    type: Object,
    default: null,
  },
  categories: {
    type: Array,
    default: () => [],
  },
  users: {
    type: Array,
    default: () => [],
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Composables
const { loading, createPost, updatePost } = usePosts()

// Form ref
const formRef = ref()
const fileInput = ref()

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isEdit = computed(() => !!props.post?.id)

// Form data
const defaultFormData = {
  title: '',
  excerpt: '',
  body: '',
  category_id: null,
  user_id: null,
  status: 'draft',
  is_hot: false,
  show_on_homepage: false,
  cover_image: null,
  meta_title: '',
  meta_description: '',
  meta_keywords: '',
}

const formData = ref({ ...defaultFormData })
const imagePreview = ref('')
const selectedFile = ref(null)

// Form rules
const formRules = {
  title: [
    { required: true, message: 'Vui lòng nhập tiêu đề bài viết', trigger: 'blur' },
    { min: 5, max: 255, message: 'Tiêu đề phải từ 5-255 ký tự', trigger: 'blur' },
  ],
  body: [
    { required: true, message: 'Vui lòng nhập nội dung bài viết', trigger: 'blur' },
    { min: 50, message: 'Nội dung phải có ít nhất 50 ký tự', trigger: 'blur' },
  ],
  user_id: [{ required: true, message: 'Vui lòng chọn tác giả', trigger: 'change' }],
  status: [{ required: true, message: 'Vui lòng chọn trạng thái', trigger: 'change' }],
  meta_title: [{ max: 255, message: 'Meta title không được vượt quá 255 ký tự', trigger: 'blur' }],
  meta_description: [{ max: 160, message: 'Meta description không được vượt quá 160 ký tự', trigger: 'blur' }],
}

// Methods
const resetForm = () => {
  formData.value = { ...defaultFormData }
  imagePreview.value = ''
  selectedFile.value = null
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const populateForm = (post) => {
  if (post) {
    formData.value = {
      title: post.title || '',
      excerpt: post.excerpt || '',
      body: post.body || '',
      category_id: post.category_id || null,
      user_id: post.user_id || null,
      status: post.status || 'draft',
      is_hot: post.is_hot || false,
      show_on_homepage: post.show_on_homepage || false,
      cover_image: post.cover_image || null,
      meta_title: post.meta_title || '',
      meta_description: post.meta_description || '',
      meta_keywords: post.meta_keywords || '',
    }
    imagePreview.value = post.cover_image || ''
  } else {
    resetForm()
    // Set default user to current user (first user in list for now)
    if (props.users.length > 0) {
      formData.value.user_id = props.users[0].id
    }
  }
}

const handleImageChange = (event) => {
  const file = event.target.files[0]
  if (file) {
    selectedFile.value = file

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.value = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

const removeImage = () => {
  imagePreview.value = ''
  selectedFile.value = null
  formData.value.cover_image = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    const submitData = { ...formData.value }

    // Add file if selected
    if (selectedFile.value) {
      submitData.cover_image = selectedFile.value
    }

    // Set status to published if not draft
    if (submitData.status === 'draft') {
      submitData.status = 'published'
    }

    if (isEdit.value) {
      await updatePost(props.post.id, submitData)
    } else {
      await createPost(submitData)
    }

    emit('success')
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const handleSaveDraft = async () => {
  try {
    await formRef.value.validate()

    const submitData = { ...formData.value }
    submitData.status = 'draft'

    // Add file if selected
    if (selectedFile.value) {
      submitData.cover_image = selectedFile.value
    }

    if (isEdit.value) {
      await updatePost(props.post.id, submitData)
    } else {
      await createPost(submitData)
    }

    emit('success')
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// Watch for post changes
watch(
  () => props.post,
  (newPost) => {
    populateForm(newPost)
  },
  { immediate: true },
)

// Watch for dialog visibility
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      populateForm(props.post)
    }
  },
)
</script>

<style scoped>
.post-form-modal :deep(.el-dialog__body) {
  padding: 1.5rem;
  max-height: 80vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.editor-container {
  position: relative;
}

.editor-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.image-upload {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
  transition: border-color 0.2s;
}

.image-upload:hover {
  border-color: #3b82f6;
}

.image-preview {
  position: relative;
  margin-bottom: 0.5rem;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 0.25rem;
}

.image-actions {
  margin-top: 0.5rem;
}

.upload-placeholder {
  padding: 2rem 0;
}

.upload-icon {
  font-size: 2rem;
  color: #9ca3af;
  margin-bottom: 0.5rem;
}

.upload-text {
  color: #6b7280;
  font-size: 0.875rem;
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}
</style>
