<template>
  <Modal
    v-model="isVisible"
    :title="isEdit ? 'Chỉnh sửa vai trò' : 'Tạo vai trò mới'"
    width="600px"
    @close="handleClose"
  >
    <template #body>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Form Fields -->
          <div class="space-y-6">
            <!-- Name Field -->
            <FormField label="Tên vai trò" :icon="User" required :error="errors.name">
              <el-input
                v-model="form.name"
                placeholder="Nhập tên vai trò (ví dụ: editor)"
                :disabled="saving"
                size="large"
                clearable
              />
            </FormField>

            <!-- Guard Name Field -->
            <FormField label="Guard Name" :icon="Lock" required :error="errors.guard_name">
              <el-select
                v-model="form.guard_name"
                placeholder="Chọn guard name"
                :disabled="saving"
                size="large"
                class="w-full"
              >
                <el-option label="API" value="api" />
                <el-option label="Web" value="web" />
              </el-select>
            </FormField>

            <!-- Description Field -->
            <FormField label="Mô tả" :icon="Document" :error="errors.description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                placeholder="Nhập mô tả vai trò..."
                :disabled="saving"
                clearable
              />
            </FormField>

            <!-- General Error -->
            <div
              v-if="errors.general"
              class="rounded-lg bg-red-50 p-3 text-sm text-red-600 dark:bg-red-900/20 dark:text-red-400"
            >
              <strong>Lỗi:</strong> {{ errors.general }}
            </div>

            <!-- Info Note -->
            <div class="rounded-lg bg-blue-50 p-3 text-sm text-gray-500 dark:bg-blue-900/20 dark:text-gray-400">
              <strong>Lưu ý:</strong> Phân quyền cho vai trò sẽ được thiết lập ở bảng "Ma trận phân quyền" bên cạnh.
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-4 border-t border-gray-200 pt-6 dark:border-gray-700">
            <el-button type="default" @click="handleClose" :disabled="saving" class="flex-1" size="large">
              <el-icon class="mr-1"><Close /></el-icon>
              Hủy
            </el-button>

            <el-button
              type="primary"
              :loading="saving"
              :disabled="!canSubmit || (isEdit ? !props.canEdit : !props.canCreate)"
              class="flex-1"
              size="large"
              @click="handleSubmit"
            >
              <el-icon v-if="!saving" class="mr-1"><Check /></el-icon>
              {{ saving ? 'Đang lưu...' : isEdit ? 'Cập nhật' : 'Tạo mới' }}
            </el-button>
          </div>
        </form>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElButton, ElInput, ElSelect, ElOption, ElIcon, ElMessage } from 'element-plus'
import { Close, User, Lock, Document, Check } from '@element-plus/icons-vue'

// Components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'

// Composables
import { useRoles } from '@/composables/modules/users/useRoles.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  role: {
    type: Object,
    default: null,
  },
  canCreate: {
    type: Boolean,
    default: true,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Composables
const { createRole, updateRole, saving, errors, clearErrors } = useRoles()

// Computed
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isEdit = computed(() => !!props.role)

const canSubmit = computed(() => {
  return form.name && form.guard_name
})

// Form data
const form = reactive({
  name: '',
  guard_name: 'api',
  description: '',
})

// Methods
const resetForm = () => {
  Object.assign(form, {
    name: '',
    guard_name: 'api',
    description: '',
  })
  clearErrors()
}

const populateForm = (role) => {
  if (!role) return

  Object.assign(form, {
    name: role.name || '',
    guard_name: role.guard_name || 'api',
    description: role.description || '',
  })
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const handleSubmit = async () => {
  try {
    if (isEdit.value) {
      await updateRole(props.role.id, form)
    } else {
      await createRole(form)
    }
    emit('success')
    handleClose()
  } catch (error) {
    // Error is already handled in useRoles composable
    console.error('Submit error:', error)
  }
}

// Watchers
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) return

    if (props.role) {
      populateForm(props.role)
    } else {
      resetForm()
    }
  },
)
</script>
