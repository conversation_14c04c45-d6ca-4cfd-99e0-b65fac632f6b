import apiAxios from '@/utils/configs/axios.config.js'

const siteSettingsApi = {
  // Site Setting Groups
  getSiteSettingGroups(params = {}) {
    return apiAxios({
      method: 'get',
      url: 'cms/site-setting-groups',
      params,
    })
  },

  getSiteSettingGroupById(id) {
    return apiAxios({
      method: 'get',
      url: `cms/site-setting-groups/${id}`,
    })
  },

  createSiteSettingGroup(data) {
    return apiAxios({
      method: 'post',
      url: 'cms/site-setting-groups',
      data,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },

  updateSiteSettingGroup(id, data) {
    return apiAxios({
      method: 'put',
      url: `cms/site-setting-groups/${id}`,
      data,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },

  deleteSiteSettingGroup(id) {
    return apiAxios({
      method: 'delete',
      url: `cms/site-setting-groups/${id}`,
    })
  },

  // Site Settings
  getSiteSettings(groupId, params = {}) {
    return apiAxios({
      method: 'get',
      url: `cms/site-setting-groups/${groupId}/site-settings`,
      params,
    })
  },

  getSiteSettingById(groupId, id) {
    return apiAxios({
      method: 'get',
      url: `cms/site-setting-groups/${groupId}/site-settings/${id}`,
    })
  },

  createSiteSetting(groupId, data) {
    // Nếu có file upload (image, document, video) thì gửi form-data
    if (data && ['image', 'document', 'video'].includes(data.type) && data.value instanceof File) {
      const formData = new FormData()

      // Append các field thông thường
      formData.append('name', data.name)
      formData.append('key', data.key)
      formData.append('type', data.type)
      formData.append('status', data.status)
      formData.append('group_id', data.group_id)

      // Gửi file với field name 'value' như server expect
      formData.append('value', data.value, data.value.name)

      return apiAxios({
        method: 'post',
        url: `cms/site-setting-groups/${groupId}/site-settings`,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else if (data instanceof FormData) {
      // Handle FormData directly
      return apiAxios({
        method: 'post',
        url: `cms/site-setting-groups/${groupId}/site-settings`,
        data,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // Log dữ liệu gửi lên server (json)
      return apiAxios({
        method: 'post',
        url: `cms/site-setting-groups/${groupId}/site-settings`,
        data,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }
  },

  updateSiteSetting(groupId, id, data) {
    // Nếu có file upload (image, document, video) thì gửi form-data với Method Spoofing
    if (data && ['image', 'document', 'video'].includes(data.type) && data.value instanceof File) {
      const formData = new FormData()

      // Thêm Method Spoofing cho PUT request
      formData.append('_method', 'PUT')

      // Append các field thông thường
      formData.append('name', data.name)
      formData.append('key', data.key)
      formData.append('type', data.type)
      formData.append('status', data.status)
      formData.append('group_id', data.group_id)

      // Gửi file với field name 'value' như server expect
      formData.append('value', data.value, data.value.name)

      return apiAxios({
        method: 'post', // Sử dụng POST thay vì PUT
        url: `cms/site-setting-groups/${groupId}/site-settings/${id}`,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else if (data instanceof FormData) {
      // Handle FormData directly with method spoofing
      data.append('_method', 'PUT')
      return apiAxios({
        method: 'post',
        url: `cms/site-setting-groups/${groupId}/site-settings/${id}`,
        data,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // Log dữ liệu gửi lên server (json)
      return apiAxios({
        method: 'put',
        url: `cms/site-setting-groups/${groupId}/site-settings/${id}`,
        data,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }
  },

  deleteSiteSetting(groupId, id) {
    return apiAxios({
      method: 'delete',
      url: `cms/site-setting-groups/${groupId}/site-settings/${id}`,
    })
  },

  // PATCH Site Setting Attributes (for status toggle, etc.)
  patchSiteSettingAttributes(groupId, id, data) {
    return apiAxios({
      method: 'patch',
      url: `cms/site-setting-groups/${groupId}/site-settings/${id}/attributes`,
      data,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },
}

export default siteSettingsApi
