/*
  Test Template Example
  Mẫu test chuẩn theo kiến trúc dự án
*/

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFeatureName, FORM_FIELDS } from './ComposableTemplate.js'

// Mock dependencies
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

describe('useFeatureName', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Khởi tạo (initialization)', () => {
    it('nên khởi tạo với giá trị mặc định đúng', () => {
      const { formData, errors, loading } = useFeatureName()

      expect(formData[FORM_FIELDS.FIELD_NAME]).toBe('')
      expect(errors[FORM_FIELDS.FIELD_NAME]).toBe('')
      expect(loading.value).toBe(false)
    })

    it('nên cung cấp constants form fields đúng', () => {
      expect(FORM_FIELDS.FIELD_NAME).toBe('field_name')
    })
  })

  describe('Validation form', () => {
    it('nên validate các trường bắt buộc', async () => {
      const { formData, validateForm, errors } = useFeatureName()

      // Trường rỗng không hợp lệ
      formData[FORM_FIELDS.FIELD_NAME] = ''
      const result = await validateForm()

      expect(result).toBe(false)
      expect(errors[FORM_FIELDS.FIELD_NAME]).toBeTruthy()
    })

    it('nên xử lý validation errors', async () => {
      const { formData, validateForm } = useFeatureName()

      // Dữ liệu hợp lệ
      formData[FORM_FIELDS.FIELD_NAME] = 'Valid value'
      const result = await validateForm()

      expect(result).toBe(true)
    })
  })

  describe('Computed properties', () => {
    it('nên tính toán canSubmit đúng', () => {
      const { formData, canSubmit, loading } = useFeatureName()

      // Trường rỗng không thể submit
      formData[FORM_FIELDS.FIELD_NAME] = ''
      expect(canSubmit.value).toBe(false)

      // Trường hợp lệ có thể submit
      formData[FORM_FIELDS.FIELD_NAME] = 'Valid value'
      expect(canSubmit.value).toBe(true)

      // Trạng thái loading không thể submit
      loading.value = true
      expect(canSubmit.value).toBe(false)
    })
  })

  describe('Form submission', () => {
    it('nên submit form thành công', async () => {
      const { formData, submitForm } = useFeatureName()

      // Mock API success
      const mockApiService = {
        submitData: vi.fn().mockResolvedValue({ data: 'success' }),
      }

      formData[FORM_FIELDS.FIELD_NAME] = 'Valid value'
      const result = await submitForm()

      expect(result.success).toBe(true)
    })

    it('nên xử lý validation errors từ server', async () => {
      const { formData, submitForm, errors } = useFeatureName()

      // Mock API validation error
      const mockError = {
        status: 422,
        data: {
          errors: {
            [FORM_FIELDS.FIELD_NAME]: ['Trường này không hợp lệ'],
          },
        },
      }

      const mockApiService = {
        submitData: vi.fn().mockRejectedValue(mockError),
      }

      formData[FORM_FIELDS.FIELD_NAME] = 'Valid value'
      const result = await submitForm()

      expect(result.success).toBe(false)
      expect(errors[FORM_FIELDS.FIELD_NAME]).toBe('Trường này không hợp lệ')
    })

    it('nên xử lý các lỗi khác', async () => {
      const { formData, submitForm } = useFeatureName()

      // Mock API error
      const mockError = {
        status: 500,
        message: 'Server error',
      }

      const mockApiService = {
        submitData: vi.fn().mockRejectedValue(mockError),
      }

      formData[FORM_FIELDS.FIELD_NAME] = 'Valid value'
      const result = await submitForm()

      expect(result.success).toBe(false)
      expect(result.message).toBe('Server error')
    })
  })

  describe('Form reset', () => {
    it('nên reset form về trạng thái ban đầu', () => {
      const { formData, errors, resetForm } = useFeatureName()

      // Thay đổi dữ liệu
      formData[FORM_FIELDS.FIELD_NAME] = 'Modified value'
      errors[FORM_FIELDS.FIELD_NAME] = 'Some error'

      resetForm()

      expect(formData[FORM_FIELDS.FIELD_NAME]).toBe('')
      expect(errors[FORM_FIELDS.FIELD_NAME]).toBe('')
    })
  })
})
