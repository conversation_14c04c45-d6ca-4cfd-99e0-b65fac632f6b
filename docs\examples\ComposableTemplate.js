/*
  Composable Template Example
  Mẫu composable chuẩn theo kiến trúc dự án
*/

import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as yup from 'yup'
import { validate } from '@/utils/helpers/validate.helper.js'

// Form field constants - unified với backend
export const FORM_FIELDS = {
  FIELD_NAME: 'field_name',
  ANOTHER_FIELD: 'another_field',
}

// Validation schema
const validationSchema = yup.object().shape({
  [FORM_FIELDS.FIELD_NAME]: yup.string().required('Trường này là bắt buộc'),
})

// Label mapping cho error messages
const labelMap = {
  [FORM_FIELDS.FIELD_NAME]: 'Tên trường',
}

/**
 * Composable for feature functionality
 * Quản lý logic business cho tính năng
 * @returns {Object} State và methods của composable
 */
export const useFeatureName = () => {
  // State
  const loading = ref(false)
  const formData = reactive({
    [FORM_FIELDS.FIELD_NAME]: '',
  })
  const errors = reactive({
    [FORM_FIELDS.FIELD_NAME]: '',
  })

  // Computed properties
  const canSubmit = computed(() => {
    return formData[FORM_FIELDS.FIELD_NAME].trim() !== '' && !loading.value
  })

  // Methods
  const clearErrors = () => {
    Object.keys(errors).forEach((key) => {
      errors[key] = ''
    })
  }

  const resetForm = () => {
    Object.keys(formData).forEach((key) => {
      formData[key] = ''
    })
    clearErrors()
  }

  const validateForm = async () => {
    clearErrors()
    const { errors: validationErrors } = await validate(validationSchema, formData, labelMap)

    if (Object.keys(validationErrors).length > 0) {
      Object.assign(errors, validationErrors)
      return false
    }
    return true
  }

  const submitForm = async () => {
    if (!(await validateForm())) {
      return { success: false, message: 'Dữ liệu không hợp lệ' }
    }

    loading.value = true

    try {
      // API call
      // Giả lập response thành công (ví dụ demo, không gọi API thật)
      const response = { data: 'success' }

      ElMessage.success('Thao tác thành công!')
      return { success: true, data: response }
    } catch (error) {
      console.error('Submit error:', error)

      // Handle server validation errors
      if (error.status === 422 && error.data?.errors) {
        Object.keys(error.data.errors).forEach((field) => {
          if (Object.prototype.hasOwnProperty.call(errors, field)) {
            errors[field] = error.data.errors[field][0]
          }
        })
        return { success: false, message: 'Dữ liệu không hợp lệ' }
      }

      // Handle other errors
      const errorMessage = error.message || 'Có lỗi xảy ra'
      ElMessage.error(errorMessage)
      return { success: false, message: errorMessage }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    formData,
    errors,

    // Computed
    canSubmit,

    // Methods
    clearErrors,
    resetForm,
    validateForm,
    submitForm,

    // Constants
    FORM_FIELDS,
  }
}
