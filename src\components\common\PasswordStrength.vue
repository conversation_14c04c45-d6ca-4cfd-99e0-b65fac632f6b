<template>
  <div v-if="password" class="mt-3 space-y-3">
    <!-- Strength Indicator -->
    <div class="flex items-center gap-2">
      <span class="text-xs text-gray-500 dark:text-gray-400"><PERSON><PERSON> mạnh:</span>
      <div class="h-2 flex-1 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
        <div :class="strengthColor" :style="{ width: strengthWidth }" class="h-full transition-all duration-300"></div>
      </div>
      <span :class="strengthTextColor" class="text-xs font-medium">
        {{ strengthText }}
      </span>
    </div>

    <!-- Requirements -->
    <div
      class="rounded-lg border border-gray-200 bg-gray-50 p-3 text-xs text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400"
    >
      <p class="mb-2 font-medium">M<PERSON><PERSON> kh<PERSON>u mạnh cần có:</p>
      <ul class="list-inside list-disc space-y-1">
        <li
          v-for="requirement in requirements"
          :key="requirement.text"
          :class="{ 'text-green-600 dark:text-green-400': requirement.met }"
        >
          {{ requirement.text }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { checkPasswordStrength } from '@/utils/helpers/validate.helper.js'

const props = defineProps({
  password: {
    type: String,
    default: '',
  },
})

// Password strength calculation
const strength = computed(() => checkPasswordStrength(props.password))

// Password strength colors
const strengthColor = computed(() => {
  const colors = {
    0: 'bg-gray-300',
    1: 'bg-red-500',
    2: 'bg-yellow-500',
    3: 'bg-green-500',
  }
  return colors[strength.value] || 'bg-gray-300'
})

// Password strength width
const strengthWidth = computed(() => `${(strength.value / 3) * 100}%`)

// Password strength text
const strengthText = computed(() => {
  const texts = {
    0: '',
    1: 'Yếu',
    2: 'Trung bình',
    3: 'Mạnh',
  }
  return texts[strength.value] || ''
})

// Password strength text color
const strengthTextColor = computed(() => {
  const colors = {
    0: 'text-gray-400',
    1: 'text-red-500',
    2: 'text-yellow-500',
    3: 'text-green-500',
  }
  return colors[strength.value] || 'text-gray-400'
})

// Password requirements
const requirements = computed(() => [
  {
    text: 'Ít nhất 8 ký tự',
    met: props.password.length >= 8,
  },
  {
    text: 'Chữ hoa và chữ thường',
    met: /[A-Z]/.test(props.password) && /[a-z]/.test(props.password),
  },
  {
    text: 'Số và ký tự đặc biệt',
    met: /\d/.test(props.password) && /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(props.password),
  },
])
</script>
