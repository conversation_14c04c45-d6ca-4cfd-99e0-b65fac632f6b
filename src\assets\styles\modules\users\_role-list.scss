// Role List Component Styles
.role-list-wrapper {
  .selected-row {
    background-color: #eff6ff !important;
    border-color: #3b82f6 !important;

    .dark & {
      background-color: rgba(59, 130, 246, 0.1) !important;
      border-color: #3b82f6 !important;
    }
  }

  // Role List Sidebar
  .role-list-sidebar {
    .role-item {
      transition: all 0.2s ease-in-out;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;

        .dark & {
          background-color: rgba(59, 130, 246, 0.1);
        }
      }
    }
  }

  // Permission Matrix
  .permission-matrix {
    .permission-table {
      th {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f9fafb;

        .dark & {
          background-color: #374151;
        }
      }

      .permission-checkbox {
        .el-checkbox__input {
          .el-checkbox__inner {
            border-radius: 4px;
          }
        }
      }

      .group-row {
        background-color: #f9fafb;
        font-weight: 600;

        .dark & {
          background-color: #374151;
        }
      }

      .child-group-row {
        background-color: #f3f4f6;
        font-weight: 500;

        .dark & {
          background-color: #4b5563;
        }
      }
    }
  }

  // Role Info Card
  .role-info {
    .role-info-card {
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      border: 1px solid #bfdbfe;

      .dark & {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
        border-color: #3b82f6;
      }
    }
  }

  // Loading States
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .dark & {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }

  // Empty States
  .empty-state {
    text-align: center;
    padding: 2rem;
    color: #6b7280;

    .dark & {
      color: #9ca3af;
    }

    .empty-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }

    .empty-description {
      font-size: 0.9rem;
      opacity: 0.7;
    }
  }

  // Responsive Design
  @media (max-width: 1024px) {
    .grid {
      grid-template-columns: 1fr;
    }

    .role-list-sidebar {
      order: 2;
    }

    .permission-matrix {
      order: 1;
    }
  }

  @media (max-width: 768px) {
    .permission-table {
      font-size: 0.875rem;

      th,
      td {
        padding: 0.5rem 0.25rem;
      }
    }

    .role-item {
      padding: 0.75rem;

      .role-actions {
        flex-direction: column;
        gap: 0.25rem;
      }
    }
  }

  // Animation Classes
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .slide-enter-active,
  .slide-leave-active {
    transition: transform 0.3s ease;
  }

  .slide-enter-from {
    transform: translateX(-100%);
  }

  .slide-leave-to {
    transform: translateX(100%);
  }

  // Custom Scrollbar
  .custom-scrollbar {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;

      .dark & {
        background: #374151;
      }
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;

      &:hover {
        background: #94a3b8;
      }

      .dark & {
        background: #6b7280;

        &:hover {
          background: #9ca3af;
        }
      }
    }
  }
}
