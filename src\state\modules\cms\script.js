import { ref } from 'vue'

// <PERSON><PERSON> liệu mock, nguồn chân lý duy nhất
const scripts = ref([
  {
    id: 1,
    name: 'Google Analytics',
    key: 'ga',
    code: '<!-- GA Code -->',
    position: 'head',
    sort_order: 1,
    status: 'active',
  },
  {
    id: 2,
    name: 'Facebook Pixel',
    key: 'fb_pixel',
    code: '<!-- FB Pixel Code -->',
    position: 'body',
    sort_order: 2,
    status: 'active',
  },
  {
    id: 3,
    name: 'Tawk.to Chat',
    key: 'tawkto',
    code: '<!-- Tawk.to Code -->',
    position: 'footer',
    sort_order: 10,
    status: 'inactive',
  },
  {
    id: 4,
    name: 'Google Tag Manager',
    key: 'gtm',
    code: '<!-- GTM Code -->',
    position: 'head',
    sort_order: 0,
    status: 'active',
  },
  {
    id: 5,
    name: 'Hotjar',
    key: 'hotjar',
    code: '<!-- Hotjar Code -->',
    position: 'body',
    sort_order: 5,
    status: 'inactive',
  },
])

// Hàm để các component sử dụng
export function useScriptStore() {
  const getAllScripts = () => {
    // Trả về một bản sao để tránh thay đổi trực tiếp từ component
    return [...scripts.value]
  }

  const getScriptById = (id) => {
    return scripts.value.find((s) => s.id === id)
  }

  const addScript = (scriptData) => {
    const newScript = {
      ...scriptData,
      id: Date.now(), // Tạo ID duy nhất (giả lập)
    }
    scripts.value.unshift(newScript)
    return newScript
  }

  const updateScript = (scriptData) => {
    const index = scripts.value.findIndex((s) => s.id === scriptData.id)
    if (index !== -1) {
      scripts.value[index] = { ...scriptData }
      return scripts.value[index]
    }
    return null
  }

  const deleteScript = (id) => {
    const index = scripts.value.findIndex((s) => s.id === id)
    if (index !== -1) {
      scripts.value.splice(index, 1)
      return true
    }
    return false
  }

  return {
    scripts, // Có thể export trực tiếp nếu muốn theo dõi (watch) từ component
    getAllScripts,
    getScriptById,
    addScript,
    updateScript,
    deleteScript,
  }
}
