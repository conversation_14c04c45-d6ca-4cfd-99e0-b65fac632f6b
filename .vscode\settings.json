{
  // Ghi chú: <PERSON><PERSON> các cài đặt này hoạt động, hãy đảm bảo bạn đã cài đặt extension
  // "ESLint" của dbaeumer và "Prettier - Code formatter" của esbenp.

  // Đặt Prettier làm trình định dạng mặc định cho các ngôn ngữ phổ biến trong dự án.
  // Điều này đảm bảo rằng khi bạn format thủ công (Shift + Option + F), nó sẽ dùng Prettier.
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // Bật tính năng "format on save" (định dạng khi lưu file).
  "editor.formatOnSave": true,

  // Tự động chạy ESLint để sửa lỗi khi lưu file.
  // Đây là cài đặt quan trọng nhất. Nó sẽ áp dụng các quy tắc từ .eslintrc
  // và format theo Prettier (do dự án đã có eslint-plugin-prettier).
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  }
}
