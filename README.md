# Admin UI

Admin panel cho hệ thống quản lý.

## Tính năng

- **Authentication System**: Đăng nhập/đăng xuất với JWT
- **Route Protection**: Bảo vệ các route yêu cầu authentication
- **User Management**: Quản lý thông tin user
- **Responsive Design**: Giao diện responsive với Tailwind CSS
- **Dark Mode**: Hỗ trợ dark mode

## Cài đặt (Cách truyền thống)

Đây là cách cài đặt nếu bạn không sử dụng Docker.

1. **Sử dụng đúng phiên bản Node.js**:
   Dự án yêu cầu phiên bản Node.js là **22.14.0** (được định nghĩa trong file `.nvmrc`).
   Chúng tôi khuyến khích sử dụng [NVM (Node Version Manager)](https://github.com/nvm-sh/nvm) để quản lý các phiên bản Node.

   ```bash
   # Tự động cài đặt và sử dụng phiên bản Node được yêu cầu
   nvm install
   nvm use
   ```

   _Lưu ý: Nếu bạn không dùng đúng phiên bản, quá trình `npm install` sẽ báo lỗi do đã được cấu hình bắt buộc._

2. **Cài đặt dependencies**:

   ```bash
   npm install
   ```

3. **Tạo file `.env` từ `.env.example`**:

   ```bash
   cp .env.example .env
   ```

4. **Cấu hình biến môi trường trong `.env`**:

   ```env
   VITE_APP_NAME=Admin Panel
   VITE_API_URL=http://localhost:8000/api/v1
   ```

5. **Chạy development server**:
   ```bash
   npm run dev
   ```

## Cấu hình Editor (VSCode)

Để có trải nghiệm phát triển tốt nhất và đảm bảo code luôn tuân thủ các quy tắc của dự án, chúng tôi khuyến khích bạn sử dụng Visual Studio Code với các extension sau:

1.  **[ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)**: Tích hợp ESLint vào editor để hiển thị lỗi và tự động sửa.
2.  **[Prettier - Code formatter](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)**: Extension chính thức của Prettier để định dạng code.

**Tin tốt là bạn không cần phải cấu hình gì thêm!**

Dự án đã bao gồm một file `.vscode/settings.json` sẽ tự động thiết lập các cài đặt sau cho bạn:

- **Tự động format khi lưu file** (`formatOnSave`).
- **Tự động sửa lỗi ESLint** khi lưu file (`codeActionsOnSave`).

Chỉ cần cài đặt hai extension trên, mở dự án và mọi thứ sẽ hoạt động một cách kỳ diệu.

## Chạy bằng Docker (Khuyến khích)

Đây là phương pháp được khuyến khích để đảm bảo môi trường phát triển và production đồng nhất.

### Môi trường Development

1.  **Tạo file `.env`**:
    Sao chép từ file `.env.example` và cấu hình các biến môi trường cần thiết.

    ```bash
    cp .env.example .env
    ```

    **Lưu ý**: Trong môi trường Docker, nếu API của bạn cũng chạy trong một container khác trên cùng một Docker network, bạn nên sử dụng tên service của container API (ví dụ: `http://my-api-container:8001/api/v1`) thay vì `localhost`.

2.  **Khởi động service**:
    Lệnh này sẽ khởi động container ở chế độ development với hot-reload.

    ```bash
    docker-compose up -d
    ```

    Ứng dụng sẽ có thể truy cập tại `http://localhost:5173`.

3.  **Xem logs**:

    ```bash
    docker-compose logs -f
    ```

4.  **Dừng service**:
    ```bash
    docker-compose down
    ```

### Môi trường Production

1.  **Build và chạy image**:
    Lệnh này sẽ build image production và khởi động container.

    ```bash
    docker-compose -f docker-compose.prod.yml up -d --build
    ```

    Ứng dụng sẽ có thể truy cập tại `http://localhost:8080`.

2.  **Dừng service**:
    ```bash
    docker-compose -f docker-compose.prod.yml down
    ```

## Cấu trúc Authentication

### Components

- `Login.vue`: Form đăng nhập với validation
- `UserMenu.vue`: Menu user với chức năng logout

### Composables

- `useAuthUser.js`: Xử lý các API authentication
- `useApi.js`: Wrapper cho API calls

### State Management

- `index.js`: Pinia store cho authentication state

### Router

- Route protection với `beforeEach` guard
- Redirect tự động dựa trên authentication status

## API Endpoints

### Authentication

- `POST /login`: Đăng nhập
- `DELETE /logout`: Đăng xuất
- `GET /me`: Lấy thông tin user hiện tại

### Request Format

```javascript
// Login
{
  "email": "<EMAIL>",
  "password": "password",
  "remember_me": true
}
```

### Response Format

```javascript
// Login Success
{
  "user": {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>"
  },
  "accessToken": "jwt_token_here"
}
```

## Development

### Scripts

- `npm run dev`: Chạy development server
- `npm run build`: Build production
- `npm run preview`: Preview production build
- `npm run lint`: Chạy ESLint

### Code Style

- Sử dụng ESLint và Prettier
- Vue 3 Composition API
- Tailwind CSS cho styling

## Docker Deployment

Để triển khai ứng dụng với Docker trong môi trường staging, sử dụng file `docker-compose.staging.yml`:

```bash
docker-compose -f docker-compose.staging.yml up -d
```

Lệnh này sẽ:

- Build image từ Dockerfile hiện tại
- Khởi chạy container với cấu hình trong file
- Sử dụng biến môi trường từ file `.env.staging`

Để dừng và xóa container, chạy:

```bash
docker-compose -f docker-compose.staging.yml down
```

Lưu ý:

- Đảm bảo đã cài đặt Docker và Docker Compose
- Tạo file `.env.staging` với các biến môi trường cần thiết trước khi khởi chạy
