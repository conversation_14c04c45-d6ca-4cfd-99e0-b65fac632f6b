<template>
  <div class="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="w-full max-w-md text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-gray-300 dark:text-gray-600">404</h1>
        <h2 class="mb-4 text-2xl font-semibold text-gray-900 dark:text-white">Trang không tồn tại</h2>
        <p class="mb-8 text-gray-600 dark:text-gray-400">
          Trang bạn đang tìm kiếm chưa được phát triển hoặc không tồn tại.
        </p>
      </div>

      <div class="space-y-4">
        <router-link
          to="/"
          class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
        >
          <HomeIcon class="mr-2 h-5 w-5" />
          Về trang chủ
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { HomeIcon } from '@/components/icons/index.js'
</script>
