import apiAxios from '@/utils/configs/axios.config.js'

const usersApi = {
  // Get users list with pagination and filters
  getUsers(params) {
    return apiAxios({
      method: 'get',
      url: '/users',
      params,
    })
  },

  // Create new user
  createUser(data) {
    return apiAxios({
      method: 'post',
      url: '/users',
      data,
    })
  },

  // Update user
  updateUser(id, data) {
    return apiAxios({
      method: 'post',
      url: `/users/${id}`,
      data,
    })
  },

  // Delete user
  deleteUser(id) {
    return apiAxios({
      method: 'delete',
      url: `/users/${id}`,
    })
  },

  // Change user password
  changeUserPassword(id, data) {
    return apiAxios({
      method: 'put',
      url: `/users/${id}/change-password`,
      data,
    })
  },

  // Get all roles
  getAllRoles(params) {
    return apiAxios({
      method: 'get',
      url: '/data/all-roles',
      params,
    })
  },
}

export default usersApi
