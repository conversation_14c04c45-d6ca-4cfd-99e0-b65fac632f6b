/* Input fields */
.dark .el-input__wrapper {
  background-color: #334155 !important;
  border-color: #475569 !important;
  box-shadow: none !important;
}

.dark .el-input__wrapper:hover {
  border-color: #64748b !important;
}

.dark .el-input__wrapper.is-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.dark .el-input__inner {
  background-color: transparent !important;
  color: #f1f5f9 !important;
}

.dark .el-input__inner::placeholder {
  color: #94a3b8 !important;
}

/* Textarea */
.dark .el-textarea__inner {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark .el-textarea__inner:hover {
  border-color: #64748b !important;
}

.dark .el-textarea__inner:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.dark .el-textarea__inner::placeholder {
  color: #94a3b8 !important;
}

/* Select dropdown */
.dark .el-select .el-input__wrapper {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .el-select-dropdown {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .el-select-dropdown__item {
  color: #f1f5f9 !important;
}

.dark .el-select-dropdown__item:hover {
  background-color: #475569 !important;
}

.dark .el-select-dropdown__item.selected {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Date picker */
.dark .el-date-editor .el-input__wrapper {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .el-picker-panel {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .el-picker-panel__content {
  color: #f1f5f9 !important;
}

.dark .el-date-table th {
  color: #94a3b8 !important;
}

.dark .el-date-table td {
  color: #f1f5f9 !important;
}

.dark .el-date-table td.available:hover {
  background-color: #475569 !important;
}

.dark .el-date-table td.current {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Upload component */
.dark .el-upload {
  background-color: #334155 !important;
}

.dark .el-upload-dragger {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .el-upload-dragger:hover {
  border-color: #3b82f6 !important;
}

.dark .el-upload__text {
  color: #94a3b8 !important;
}

/* Form labels */
.dark .el-form-item__label {
  color: #e2e8f0 !important;
}

/* Switches */
.dark .el-switch {
  --el-switch-on-color: #3b82f6 !important;
  --el-switch-off-color: #475569 !important;
}

/* Buttons */
.dark .el-button {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark .el-button:hover {
  background-color: #475569 !important;
  border-color: #64748b !important;
}

.dark .el-button--primary {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

.dark .el-button--primary:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

/* Danger button - giữ màu đỏ trong dark mode */
.dark .el-button--danger {
  background-color: #dc2626 !important;
  border-color: #dc2626 !important;
  color: #ffffff !important;
}

.dark .el-button--danger:hover {
  background-color: #b91c1c !important;
  border-color: #b91c1c !important;
  color: #ffffff !important;
}

.dark .el-button--danger:focus {
  background-color: #dc2626 !important;
  border-color: #dc2626 !important;
  color: #ffffff !important;
  outline: none !important;
}

/* Remove focus outline and ring for all buttons */
.el-button:focus,
.el-button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

/* Remove focus ring for all elements in dark mode */
.dark *:focus,
.dark *:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

/* Remove focus outline and ring for all buttons in dark mode */
.dark .el-button:focus,
.dark .el-button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

/* Info button */
.dark .el-button--info {
  background-color: #6b7280 !important;
  border-color: #6b7280 !important;
  color: #ffffff !important;
}

.dark .el-button--info:hover {
  background-color: #4b5563 !important;
  border-color: #4b5563 !important;
  color: #ffffff !important;
}

/* Word limit counter */
.dark .el-input__count {
  color: #94a3b8 !important;
}

/* Popper/Tooltip */
.dark .el-popper {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}
/* Loading */
.dark .el-loading-mask {
  background-color: rgba(30, 41, 59, 0.8) !important;
}

.dark .el-loading-spinner .path {
  stroke: #3b82f6 !important;
}
/* Message */
.dark .el-message {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark .el-message--success {
  background-color: #065f46 !important;
  border-color: #059669 !important;
  color: #d1fae5 !important;
}

.dark .el-message--error {
  background-color: #7f1d1d !important;
  border-color: #dc2626 !important;
  color: #fecaca !important;
}

.dark .el-message--warning {
  background-color: #78350f !important;
  border-color: #d97706 !important;
  color: #fed7aa !important;
}

/* Table */
.dark .el-table {
  background-color: #1e293b !important;
  border-color: #334155 !important;
}

.dark .el-table th.el-table__cell {
  background-color: #0f172a !important;
  border-color: #334155 !important;
  color: #e2e8f0 !important;
}

.dark .el-table td.el-table__cell {
  background-color: #1e293b !important;
  border-color: #334155 !important;
  color: #f1f5f9 !important;
}

.dark .el-table tr:hover > td.el-table__cell {
  background-color: #334155 !important;
}

/* Table Empty State */
.dark .el-table__empty-block {
  background-color: #1e293b !important;
  min-height: 200px !important;
}

.dark .el-table__empty-text {
  color: #94a3b8 !important;
  font-size: 14px !important;
}