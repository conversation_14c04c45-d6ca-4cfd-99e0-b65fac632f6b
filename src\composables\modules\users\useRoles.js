/*
  Roles Composable
  Quản lý logic business cho module roles
*/

import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as yup from 'yup'
import { rolesApi } from '@/utils/apis'
import { VALIDATION } from '@/utils/configs/constant.config.js'

// Validation schemas
const roleSchema = yup.object({
  name: yup
    .string()
    .required('Tên vai trò là bắt buộc')
    .min(VALIDATION.NAME_MIN_LENGTH, 'Tên phải có ít nhất 2 ký tự')
    .max(255, 'Tên không được quá 255 ký tự'),
  guard_name: yup.string().oneOf(['api', 'web'], 'Guard name phải là api hoặc web').default('api'),
  description: yup.string().max(500, '<PERSON><PERSON> tả không được quá 500 ký tự'),
})

export function useRoles() {
  // State
  const roles = ref([])
  const role = ref(null)
  const rolePermissions = ref([])
  const loading = ref(false)
  const saving = ref(false)
  const deleting = ref(false)
  const filters = ref({
    search: '',
  })
  const errors = ref({})

  // Computed
  const hasRoles = computed(() => roles.value.length > 0)
  const isFiltered = computed(() => {
    return filters.value.search
  })

  // Helper methods
  const handleValidationError = (error) => {
    // Clear previous errors
    errors.value = {}

    if (error.name === 'ValidationError') {
      // Handle Yup validation errors
      errors.value = error.errors.reduce((acc, err) => {
        acc[err.path] = err.message
        return acc
      }, {})
      return
    }

    // Handle API validation errors
    const apiErrors = error.response?.data?.errors
    if (apiErrors && typeof apiErrors === 'object') {
      errors.value = Object.entries(apiErrors).reduce((acc, [key, value]) => {
        acc[key] = Array.isArray(value) ? value[0] : value
        return acc
      }, {})
      return
    }

    // Handle general error message
    const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra'
    errors.value = { general: errorMessage }
  }

  // Methods
  const fetchRoles = async () => {
    try {
      loading.value = true
      const response = await rolesApi.getRoles(filters.value)

      if (response.data.success) {
        roles.value = response.data.data.roles.data || []
      } else {
        throw new Error(response.data.message || 'Có lỗi xảy ra khi tải danh sách vai trò')
      }
    } catch (error) {
      console.error('Error fetching roles:', error)
      handleValidationError(error)
      ElMessage.error('Không thể tải danh sách vai trò')
    } finally {
      loading.value = false
    }
  }

  const fetchRole = async (id) => {
    try {
      loading.value = true
      const response = await rolesApi.getRole(id)

      if (response.data.success) {
        // Lưu role data
        role.value = response.data.data.role

        // Lưu permission_groups data
        rolePermissions.value = response.data.data.permission_groups || []
      } else {
        throw new Error(response.data.message || 'Có lỗi xảy ra khi tải thông tin vai trò')
      }
    } catch (error) {
      console.error('Error fetching role:', error)
      handleValidationError(error)
      ElMessage.error('Không thể tải thông tin vai trò')
    } finally {
      loading.value = false
    }
  }

  const createRole = async (roleData) => {
    try {
      saving.value = true
      await roleSchema.validate(roleData)

      const response = await rolesApi.createRole(roleData)

      if (response.data.success) {
        const newRole = response.data.data.role
        roles.value.unshift(newRole)
        ElMessage.success('Tạo vai trò thành công')
        return newRole
      }

      // Handle API error response
      const error = new Error(response.data.message || 'Có lỗi xảy ra khi tạo vai trò')
      error.response = { data: response.data }
      throw error
    } catch (error) {
      console.error('Error creating role:', error)
      handleValidationError(error)

      // Only show generic error if no specific validation errors
      if (!hasErrors()) {
        ElMessage.error('Không thể tạo vai trò')
      }
      throw error
    } finally {
      saving.value = false
    }
  }

  const updateRole = async (id, roleData) => {
    try {
      saving.value = true
      await roleSchema.validate(roleData)

      const response = await rolesApi.updateRole(id, roleData)

      if (response.data.success) {
        const updatedRole = response.data.data.role

        // Update in roles list
        const index = roles.value.findIndex((r) => r.id === id)
        if (index !== -1) {
          roles.value[index] = updatedRole
        }

        // Update current role if it's the same
        if (role.value?.id === id) {
          role.value = updatedRole
        }

        ElMessage.success('Cập nhật vai trò thành công')
        return updatedRole
      }

      // Handle API error response
      const error = new Error(response.data.message || 'Có lỗi xảy ra khi cập nhật vai trò')
      error.response = { data: response.data }
      throw error
    } catch (error) {
      console.error('Error updating role:', error)
      handleValidationError(error)

      // Only show generic error if no specific validation errors
      if (!hasErrors()) {
        ElMessage.error('Không thể cập nhật vai trò')
      }
      throw error
    } finally {
      saving.value = false
    }
  }

  const deleteRole = async (id) => {
    try {
      await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa vai trò này?', 'Xác nhận xóa', {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      })

      deleting.value = true
      const response = await rolesApi.deleteRole(id)

      if (response.data.success) {
        const index = roles.value.findIndex((r) => r.id === id)
        if (index !== -1) {
          roles.value.splice(index, 1)
        }

        ElMessage.success('Xóa vai trò thành công')
      } else {
        throw new Error(response.data.message || 'Có lỗi xảy ra khi xóa vai trò')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Error deleting role:', error)
        ElMessage.error('Không thể xóa vai trò')
      }
      throw error
    } finally {
      deleting.value = false
    }
  }

  const assignPermissions = async (id, permissionIds) => {
    try {
      const response = await rolesApi.assignPermissions(id, { permission_ids: permissionIds })

      if (response.data.success) {
        ElMessage.success('Gán quyền cho vai trò thành công')
        return response.data.data.role
      } else {
        throw new Error(response.data.message || 'Có lỗi xảy ra khi gán quyền')
      }
    } catch (error) {
      console.error('Error assigning permissions:', error)
      ElMessage.error(error.message || 'Không thể gán quyền cho vai trò')
      throw error
    }
  }

  const revokePermissions = async (id, permissionIds) => {
    try {
      const response = await rolesApi.revokePermissions(id, { permission_ids: permissionIds })

      if (response.data.success) {
        ElMessage.success('Gỡ quyền khỏi vai trò thành công')
        return response.data.data.role
      } else {
        throw new Error(response.data.message || 'Có lỗi xảy ra khi gỡ quyền')
      }
    } catch (error) {
      console.error('Error revoking permissions:', error)
      ElMessage.error(error.message || 'Không thể gỡ quyền khỏi vai trò')
      throw error
    }
  }

  const searchRoles = async () => {
    try {
      loading.value = true
      const response = await rolesApi.getRoles(filters.value)

      if (response.data.success) {
        roles.value = response.data.data.roles.data || []
      } else {
        throw new Error(response.data.message || 'Có lỗi xảy ra khi tìm kiếm vai trò')
      }
    } catch (error) {
      console.error('Error searching roles:', error)
      handleValidationError(error)
      ElMessage.error('Không thể tìm kiếm vai trò')
    } finally {
      loading.value = false
    }
  }

  const resetFilters = () => {
    filters.value = {
      search: '',
    }
    clearErrors()
  }

  const clearErrors = () => {
    errors.value = {}
  }

  const hasErrors = () => {
    return Object.keys(errors.value).length > 0
  }

  return {
    // State
    roles,
    role,
    rolePermissions,
    loading,
    saving,
    deleting,
    filters,
    errors,

    // Computed
    hasRoles,
    isFiltered,

    // Methods
    fetchRoles,
    fetchRole,
    createRole,
    updateRole,
    deleteRole,
    assignPermissions,
    revokePermissions,
    searchRoles,
    resetFilters,
    clearErrors,
    hasErrors,
  }
}
