import { ref, reactive } from 'vue'
import { staticPageApi } from '@/utils/apis'
import { ElMessage } from 'element-plus'
import { extractErrorResponse } from '@/utils/helpers/response.helper.js'

/**
 * Composable quản lý trạng thái và thao tác với trang tĩnh
 * @returns {Object} - Các state và methods để quản lý trang tĩnh
 */
export function useStaticPage() {
  // State
  const loading = ref(false)
  const staticPages = ref([])
  const staticPage = ref(null)
  const pagination = reactive({
    current_page: 1,
    per_page: 10,
    total: 0,
    last_page: 1,
    from: 1,
    to: 0,
    has_more_pages: false,
  })
  const filters = reactive({ search: '' })
  const sort = reactive({ prop: 'updated_at', order: 'desc' })

  /**
   * Lấy danh sách trang tĩnh có phân trang
   * @param {Object} params - <PERSON>ham số phân trang, sắp xếp (không bao gồm search)
   * @returns {Object} - <PERSON><PERSON> liệu trả về từ API
   */
  const fetchStaticPages = async (params = {}) => {
    loading.value = true
    try {
      // Xây dựng tham số mặc định nếu không có
      const apiParams = {
        page: params.page || pagination.current_page,
        limit: params.per_page || pagination.per_page,
      }

      // Không truyền search vào API (sẽ filter local)

      const response = await staticPageApi.getStaticPages(apiParams)

      if (response.data && response.data.success) {
        const responseData = response.data.data

        // Cập nhật danh sách trang
        if (responseData.data) {
          staticPages.value = responseData.data
        }

        // Cập nhật thông tin phân trang
        if (responseData.pagination) {
          Object.assign(pagination, responseData.pagination)
        } else {
          pagination.total = staticPages.value.length
          pagination.to = pagination.from + staticPages.value.length - 1
        }

        return responseData
      }

      return { data: [], pagination: { total: 0 } }
    } catch (error) {
      console.error('Error fetching static pages:', error)
      ElMessage.error('Không thể tải danh sách trang tĩnh')
      staticPages.value = []
      return { data: [], pagination: { total: 0 } }
    } finally {
      loading.value = false
    }
  }

  /**
   * Lọc danh sách trang tĩnh theo title trên state
   * @param {string} keyword - Từ khóa tìm kiếm
   * @returns {Array} - Danh sách trang đã lọc
   */
  const filterStaticPagesByTitle = (keyword = '') => {
    if (!keyword) return staticPages.value

    const searchTerm = keyword.toLowerCase().trim()
    return staticPages.value.filter((page) => (page.title || '').toLowerCase().includes(searchTerm))
  }

  /**
   * Lấy chi tiết một trang tĩnh
   * @param {number|string} id - ID của trang tĩnh
   * @returns {Object} - Dữ liệu trang tĩnh
   */
  const fetchStaticPage = async (id) => {
    loading.value = true
    try {
      const response = await staticPageApi.getStaticPage(id)

      if (response.data && response.data.success) {
        staticPage.value = response.data.data
        return staticPage.value
      }

      return null
    } catch (error) {
      console.error(`Error fetching static page with id ${id}:`, error)
      ElMessage.error('Không thể tải thông tin trang tĩnh')
      staticPage.value = null
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Tạo trang tĩnh mới
   * @param {Object} pageData - Dữ liệu trang tĩnh
   * @returns {Object} - Kết quả tạo trang
   */
  const createStaticPage = async (pageData) => {
    loading.value = true
    try {
      const response = await staticPageApi.createStaticPage(pageData)

      if (response.data && response.data.success) {
        ElMessage.success(response.data.message || 'Tạo trang tĩnh thành công')
        return response.data.data
      }

      ElMessage.error('Không thể tạo trang tĩnh')
      return null
    } catch (error) {
      console.error('Error creating static page:', error)
      const { message } = extractErrorResponse(error)
      ElMessage.error(message || 'Không thể tạo trang tĩnh')
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Cập nhật trang tĩnh
   * @param {number|string} id - ID của trang tĩnh
   * @param {Object} pageData - Dữ liệu cập nhật
   * @returns {Object} - Kết quả cập nhật
   */
  const updateStaticPage = async (id, pageData) => {
    loading.value = true
    try {
      const response = await staticPageApi.updateStaticPage(id, pageData)

      if (response.data && response.data.success) {
        ElMessage.success(response.data.message || 'Cập nhật trang tĩnh thành công')
        return response.data.data
      }

      ElMessage.error('Không thể cập nhật trang tĩnh')
      return null
    } catch (error) {
      console.error(`Error updating static page with id ${id}:`, error)
      const { message } = extractErrorResponse(error)
      ElMessage.error(message || 'Không thể cập nhật trang tĩnh')
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Xóa trang tĩnh
   * @param {number|string} id - ID của trang tĩnh
   * @returns {boolean} - Kết quả xóa
   */
  const deleteStaticPage = async (id) => {
    loading.value = true
    try {
      const response = await staticPageApi.deleteStaticPage(id)

      if (response.data && response.data.success) {
        ElMessage.success(response.data.message || 'Xóa trang tĩnh thành công')
        return true
      }

      ElMessage.error('Không thể xóa trang tĩnh')
      return false
    } catch (error) {
      console.error(`Error deleting static page with id ${id}:`, error)
      const { message } = extractErrorResponse(error)
      ElMessage.error(message || 'Không thể xóa trang tĩnh')
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    staticPages,
    staticPage,
    pagination,
    filters,
    sort,

    // Methods
    fetchStaticPages,
    fetchStaticPage,
    createStaticPage,
    updateStaticPage,
    deleteStaticPage,
    filterStaticPagesByTitle,
  }
}
