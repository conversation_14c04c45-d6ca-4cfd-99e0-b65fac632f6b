<template>
  <div class="role-card">
    <div v-if="role" class="rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-700 dark:bg-blue-900/20">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
            {{ role.name }}
          </h3>
          <p class="text-sm text-blue-700 dark:text-blue-300">
            {{ role.guard_name || 'api' }}
          </p>
        </div>
        <div class="text-right">
          <div class="text-sm text-blue-600 dark:text-blue-400">{{ displayPermissionCount }} quyền đ<PERSON> cấp</div>
          <div class="text-xs text-blue-500 dark:text-blue-500">
            {{ formatDate(role.created_at) }}
          </div>
        </div>
      </div>
    </div>
    <div v-else class="p-3 text-center text-gray-500 dark:text-gray-400">Ma <PERSON>rậ<PERSON> quyền</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  role: {
    type: Object,
    default: null,
  },
  permissionCount: {
    type: Number,
    default: 0,
  },
})

// Computed property for real-time permission count
const displayPermissionCount = computed(() => {
  // Use provided permissionCount if available (real-time from parent)
  if (props.permissionCount !== undefined && props.permissionCount !== 0) {
    return props.permissionCount
  }

  // Fallback to role's permissions_count
  return props.role?.permissions_count || 0
})

// Methods
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('vi-VN')
}
</script>
