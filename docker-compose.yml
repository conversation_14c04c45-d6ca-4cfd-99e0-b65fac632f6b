# Cấu hình Docker Compose cho môi trường Development

services:
  # Định nghĩa service cho ứng dụng Vue.js
  app:
    # Sử dụng image Node.js phiên bản 22.14.0-alpine
    image: node:22.14.0-alpine

    # Đặt tên cho container để dễ quản lý
    container_name: jx1-admin-ui-dev

    # Chạy container với quyền của người dùng hiện tại thay vì root
    # Giúp tránh các vấn đề về quyền sở hữu file (ví dụ: node_modules)
    user: '${UID}:${GID}'

    # Đặt thư mục làm việc trong container
    working_dir: /app

    # Ánh xạ mã nguồn từ máy host vào container
    # Điều này cho phép hot-reloading khi code thay đổi
    volumes:
      - .:/app
      # Sử dụng một volume riêng cho node_modules để không bị ghi đè bởi thư mục trên host
      - /app/node_modules

    # Mở port 5173 của container và ánh xạ ra port 5173 trên máy host
    # Vite mặc định chạy trên port này
    ports:
      - '5173:5173'

    # Điểm vào cho container
    entrypoint: ['./docker-entrypoint.sh']

    # Tải các biến môi trường từ file .env trong thư mục gốc của dự án
    env_file:
      - .env

    # Khởi động lại container trừ khi bị dừng thủ công
    restart: unless-stopped

    # Tham gia vào mạng jx1-network
    networks:
      - jx1-network

# Định nghĩa mạng jx1-network
networks:
  jx1-network:
    # Sử dụng driver bridge
    driver: bridge
