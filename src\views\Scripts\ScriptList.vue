<template>
  <div class="p-6">
    <PageBreadcrumb :pageTitle="currentPageTitle" />

    <!-- Filters & Actions -->
    <div class="mb-6 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
      <div class="flex flex-col items-stretch gap-4 sm:flex-row sm:items-center sm:justify-between">
        <!-- Filters -->
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-2">
          <el-input
            v-model="filters.search"
            placeholder="Tìm kiếm theo tên hoặc key..."
            clearable
            @input="handleSearch"
            class="w-full sm:w-64"
          />
          <el-select v-model="filters.position" placeholder="Vị trí" clearable class="w-full sm:w-40">
            <el-option label="Head" value="head" />
            <el-option label="Body" value="body" />
            <el-option label="Footer" value="footer" />
          </el-select>
          <el-select v-model="filters.status" placeholder="Trạng thái" clearable class="w-full sm:w-40">
            <el-option label="Active" value="active" />
            <el-option label="Inactive" value="inactive" />
          </el-select>
          <el-button @click="resetFilters">
            <RefreshIcon class="mr-2 h-4 w-4" />
            Đặt lại
          </el-button>
        </div>

        <!-- Actions -->
        <div class="flex-shrink-0">
          <el-button type="primary" @click="handleCreateScript">
            <PlusIcon class="mr-2 h-4 w-4" />
            Tạo Script
          </el-button>
        </div>
      </div>
    </div>

    <!-- Scripts Table -->
    <div class="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
      <el-table
        v-loading="loading"
        :data="paginatedScripts"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f8fafc', color: '#374151' }"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="name" label="Tên Script" min-width="200" sortable="custom" />
        <el-table-column prop="key" label="Key" width="150" sortable="custom" />

        <el-table-column prop="position" label="Vị trí" width="120" sortable="custom">
          <template #default="{ row }">
            <el-tag
              size="small"
              :type="row.position === 'head' ? 'primary' : row.position === 'body' ? 'success' : 'info'"
            >
              {{ row.position.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="Sắp xếp" width="100" align="center" sortable="custom" />

        <el-table-column prop="status" label="Trạng thái" width="120" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ row.status === 'active' ? 'Active' : 'Inactive' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="Hành động" width="180" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-end gap-2">
              <el-button type="default" size="small" @click="handleEditScript(row)" :icon="SettingsIcon">Sửa</el-button>
              <el-button type="danger" size="small" @click="handleDeleteScript(row)" :icon="TrashIcon">Xóa</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="border-t border-gray-200 p-4 dark:border-gray-700">
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
            <span class="text-sm text-gray-700 dark:text-gray-300">
              Hiển thị {{ pagination.from }} - {{ pagination.to }} của {{ pagination.total }} kết quả
            </span>
            <div class="flex items-center gap-2">
              <el-select v-model="pagination.per_page" style="width: 80px" size="small">
                <el-option label="10" :value="10" />
                <el-option label="20" :value="20" />
                <el-option label="30" :value="30" />
                <el-option label="50" :value="50" />
              </el-select>
              <span class="text-sm text-gray-700 dark:text-gray-300">/ trang</span>
            </div>
          </div>

          <div class="flex justify-center sm:justify-end">
            <el-pagination
              v-model:current-page="pagination.current_page"
              :total="pagination.total"
              :page-size="pagination.per_page"
              layout="prev, pager, next"
              small
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Script Form Modal -->
  <ScriptFormModal v-model:visible="isModalVisible" :script="selectedScript" @success="handleSuccess" />
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import PageBreadcrumb from '~/components/common/PageBreadcrumb.vue'
import ScriptFormModal from '~/components/pages/script/ScriptFormModal.vue'
import { PlusIcon, RefreshIcon, SettingsIcon, TrashIcon } from '@/components/icons/index.js'
import { useScriptStore } from '~/state'

const router = useRouter()
const { getAllScripts, deleteScript: deleteScriptFromStore } = useScriptStore()

// State
const currentPageTitle = ref('Quản lý Script')
const loading = ref(false)
const allScripts = ref([])
const isModalVisible = ref(false)
const selectedScript = ref(null)
const filters = reactive({
  search: '',
  status: '',
  position: '',
})
const sort = reactive({
  prop: 'sort_order',
  order: 'ascending',
})
const pagination = reactive({
  current_page: 1,
  per_page: 10,
  total: 0,
  from: 0,
  to: 0,
})

// Computed Properties
const filteredScripts = computed(() => {
  let data = [...allScripts.value]
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase()
    data = data.filter((s) => s.name.toLowerCase().includes(searchTerm) || s.key.toLowerCase().includes(searchTerm))
  }
  if (filters.status) {
    data = data.filter((s) => s.status === filters.status)
  }
  if (filters.position) {
    data = data.filter((s) => s.position === filters.position)
  }

  // Sorting
  if (sort.prop && sort.order) {
    data.sort((a, b) => {
      const aVal = a[sort.prop]
      const bVal = b[sort.prop]
      const modifier = sort.order === 'ascending' ? 1 : -1

      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return aVal.localeCompare(bVal) * modifier
      }
      if (aVal < bVal) return -1 * modifier
      if (aVal > bVal) return 1 * modifier
      return 0
    })
  }

  return data
})

const paginatedScripts = computed(() => {
  const data = filteredScripts.value
  const start = (pagination.current_page - 1) * pagination.per_page
  const end = start + pagination.per_page
  return data.slice(start, end)
})

watch(
  filteredScripts,
  (data) => {
    pagination.total = data.length
    const start = (pagination.current_page - 1) * pagination.per_page
    pagination.from = data.length > 0 ? start + 1 : 0
    const end = start + pagination.per_page
    pagination.to = Math.min(end, data.length)

    // Tự động về trang 1 nếu trang hiện tại không còn hợp lệ sau khi lọc
    const totalPages = Math.ceil(data.length / pagination.per_page)
    if (pagination.current_page > totalPages && totalPages > 0) {
      pagination.current_page = 1
    }
  },
  { deep: true },
)

// Methods
const fetchData = () => {
  loading.value = true
  try {
    // Lấy dữ liệu từ store
    const scripts = getAllScripts()
    allScripts.value = [...scripts] // Không cần sort ở đây nữa
  } catch (error) {
    ElMessage.error('Lỗi khi tải dữ liệu script.')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current_page = 1
}

const resetFilters = () => {
  filters.search = ''
  filters.status = ''
  filters.position = ''
  pagination.current_page = 1
}

const handleSortChange = ({ prop, order }) => {
  sort.prop = prop
  sort.order = order
}

const handleCreateScript = () => {
  selectedScript.value = null
  isModalVisible.value = true
}

const handleEditScript = (script) => {
  selectedScript.value = script
  isModalVisible.value = true
}

const handleDeleteScript = (script) => {
  ElMessageBox.confirm(`Bạn có chắc chắn muốn xóa script "${script.name}"?`, 'Xác nhận xóa', {
    confirmButtonText: 'Xóa',
    cancelButtonText: 'Hủy',
    type: 'warning',
  })
    .then(() => {
      const success = deleteScriptFromStore(script.id)
      if (success) {
        ElMessage.success('Xóa script thành công!')
        fetchData() // Tải lại dữ liệu sau khi xóa
      } else {
        ElMessage.error('Không tìm thấy script để xóa.')
      }
    })
    .catch(() => {
      ElMessage.info('Đã hủy thao tác xóa.')
    })
}

const handleSuccess = () => {
  isModalVisible.value = false
  fetchData()
}

// Lifecycle
onMounted(() => {
  fetchData()
})
</script>
