<template>
  <div class="menu-list-wrapper">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý CMS', to: '/cms' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Two Column Layout: 4:6 ratio -->
      <div class="grid grid-cols-1 gap-6 lg:h-[650px] lg:grid-cols-10 lg:items-stretch">
        <!-- Column 1: Menu List (4/10 width) -->
        <div class="h-full space-y-4 lg:col-span-4">
          <MenuSidebar
            :menus="menus"
            :selected-menu="selectedMenu"
            :loading="loading"
            :loading-more="loadingMoreMenus"
            :has-more-pages="pagination.has_more_pages"
            @create-menu="handleCreateMenu"
            @edit-menu="handleEditMenu"
            @delete-menu="handleDeleteMenu"
            @select-menu="handleSelectMenu"
            @refresh="handleRefresh"
            @load-more="handleLoadMoreMenus"
            @search="handleSearch"
          />
        </div>

        <!-- Column 2: Menu Items List (6/10 width) -->
        <div class="menu-items-panel lg:col-span-6">
          <!-- Panel Header -->
          <div class="panel-header">
            <div class="header-content">
              <h3 class="panel-title">
                {{ selectedMenu ? `${selectedMenu.name} - ${totalItems} menu items` : 'Chọn Menu để quản lý Items' }}
              </h3>
              <ButtonCommon v-if="selectedMenu" :icon="PlusIcon" type="primary" size="small" @click="handleAddMenuItem">
                Thêm Item
              </ButtonCommon>
            </div>
          </div>

          <!-- Panel Content -->
          <div class="panel-content">
            <div v-if="!selectedMenu" class="empty-state">
              <div class="empty-icon">📋</div>
              <p class="empty-text">Chọn một menu để xem items</p>
            </div>

            <div v-else-if="menuItems.length === 0 && !loadingMenuItems" class="empty-state">
              <div class="empty-icon">📝</div>
              <p class="empty-text">Chưa có menu items</p>
              <ButtonCommon type="primary" size="small" :icon="PlusIcon" @click="handleAddMenuItem">
                Thêm Item
              </ButtonCommon>
            </div>

            <div v-else-if="loadingMenuItems" class="empty-state">
              <el-icon class="mb-4 animate-spin text-2xl text-gray-400">
                <Loading />
              </el-icon>
              <p class="empty-text">Đang tải menu items...</p>
            </div>

            <DraggableMenuItemTree
              v-else-if="selectedMenu"
              :items="menuItems"
              :selected-item-id="selectedItem?.id"
              :expanded-items="expandedItems"
              @edit="handleEditMenuItem"
              @delete="deleteMenuItem"
              @toggle-expand="toggleExpand"
            />

            <!-- Total count indicator -->
            <div v-if="menuItems.length > 0" class="items-count">Tổng số: {{ totalItems }} items</div>
          </div>
        </div>
      </div>

      <!-- Modals -->
      <MenuFormModal v-model:visible="showCreateMenuModal" :menu="null" @success="handleMenuCreated" />
      <MenuFormModal v-model:visible="showEditMenuModal" :menu="editingMenu" @success="handleMenuUpdated" />
      <MenuItemModal
        v-if="selectedMenu"
        v-model:visible="showMenuItemModal"
        :item="editingMenuItem"
        :menu-id="selectedMenu.id"
        :menu-items="menuItems"
        :current-menu="currentMenu"
        :linkable-types="linkableTypes"
        :create-menu-item="createMenuItem"
        :update-menu-item="updateMenuItem"
        :loading="loadingMenuItems"
        @success="handleMenuItemSuccess"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { Edit, Delete, Loading, Search } from '@element-plus/icons-vue'
import { useMenus } from '@/composables/modules/cms/useMenus.js'
import { useMenuItems } from '@/composables/modules/cms/useMenuItems.js'
import { PlusIcon, RefreshIcon, SettingsIcon } from '@/components/icons/index.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import MenuFormModal from '@/components/modules/cms/menus/MenuFormModal.vue'
import DraggableMenuItemTree from '@/components/modules/cms/menus/DraggableMenuItemTree.vue'
import MenuItemForm from '@/components/modules/cms/menus/MenuItemForm.vue'
import MenuSidebar from '@/components/modules/cms/menus/MenuSidebar.vue'
import MenuItemModal from '@/components/modules/cms/menus/MenuItemModal.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { hasPermission, getUserPermissions } from '@/utils/helpers/permission.helper.js'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'

// Page title
const currentPageTitle = ref('Quản lý Menu')

// Composables
const { loading, loadingMore: loadingMoreMenus, menus, pagination, fetchMenus, loadMoreMenus, searchMenus, deleteMenu: deleteMenuApi } = useMenus()
const {
  loading: loadingMenuItems,
  loadingMore,
  menuItems,
  currentMenu,
  totalItems,
  hasMorePages,
  fetchMenuWithItems,
  fetchMenuItems,
  loadMoreItems,
  createMenuItem,
  updateMenuItem,
  deleteMenuItem: deleteMenuItemApi,
  resetState,
} = useMenuItems()

// Modal states
const showCreateMenuModal = ref(false)
const showEditMenuModal = ref(false)
const showMenuItemModal = ref(false)

// Selected states
const selectedMenu = ref(null)
const editingMenu = ref(null)
const editingMenuItem = ref(null)
const selectedItem = ref(null)
const scrollContainer = ref(null)
const expandedItems = ref(new Set())

// Linkable types (you may need to fetch this from API)
const linkableTypes = ref([])

// Methods for Menu Management
const handleCreateMenu = () => {
  editingMenu.value = null
  showCreateMenuModal.value = true
}

const handleEditMenu = (menu) => {
  editingMenu.value = menu
  showEditMenuModal.value = true
}

const handleDeleteMenu = async (menuId) => {
  await deleteMenuApi(menuId)
  await fetchMenus()

  // Clear selected menu if it was deleted
  if (selectedMenu.value && selectedMenu.value.id === menuId) {
    selectedMenu.value = null
    selectedItem.value = null
    editingMenuItem.value = null
    resetState()
  }
}

const handleSelectMenu = async (menu) => {
  selectedMenu.value = menu
  selectedItem.value = null
  editingMenuItem.value = null
  await loadMenuData()
}

const handleRefresh = async () => {
  await fetchMenus()
  if (selectedMenu.value) {
    await loadMenuData()
  }
}

const handleLoadMoreMenus = async () => {
  await loadMoreMenus()
}

const handleSearch = async (searchQuery) => {
  await searchMenus(searchQuery)
}
const loadMenuData = async () => {
  if (selectedMenu.value?.id) {
    try {
      resetState()
      // Load all menu items (fetchMenuWithItems already loads all items)
      await fetchMenuWithItems(selectedMenu.value.id)

      // Auto expand ALL items with children (recursive)
      const expandAllWithChildren = (items, expandedSet) => {
        items.forEach((item) => {
          if (item.children && item.children.length > 0) {
            expandedSet.add(item.id)
            // Recursively expand children
            expandAllWithChildren(item.children, expandedSet)
          }
        })
      }

      const newExpandedItems = new Set()
      expandAllWithChildren(menuItems.value, newExpandedItems)

      expandedItems.value = newExpandedItems
    } catch (error) {
      console.error('Error loading menu data:', error)
    }
  }
}

const handleAddMenuItem = () => {
  editingMenuItem.value = null
  showMenuItemModal.value = true
}

const handleEditMenuItem = (item) => {
  editingMenuItem.value = item
  showMenuItemModal.value = true
}

const editMenuItem = (item) => {
  handleEditMenuItem(item)
}

const deleteMenuItem = async (itemId) => {
  await deleteMenuItemApi(itemId)
  selectedItem.value = null
  await loadMenuData() // Reload menu data after deletion
}

const handleMenuItemSuccess = async () => {
  editingMenuItem.value = null
  showMenuItemModal.value = false
  await loadMenuData() // Reload menu data
}

const handleItemSuccess = async () => {
  selectedItem.value = null
  await loadMenuData() // Reload menu data
}

const handleItemCancel = () => {
  selectedItem.value = null
}

const toggleExpand = (itemId) => {
  if (expandedItems.value.has(itemId)) {
    expandedItems.value.delete(itemId)
  } else {
    expandedItems.value.add(itemId)
  }
}

// Modal handlers
const handleMenuCreated = () => {
  showCreateMenuModal.value = false
  fetchMenus()
}

const handleMenuUpdated = () => {
  showEditMenuModal.value = false
  editingMenu.value = null
  fetchMenus()

  // Refresh menu data if the updated menu is currently selected
  if (selectedMenu.value && editingMenu.value && selectedMenu.value.id === editingMenu.value.id) {
    loadMenuData()
  }
}

// Watch for selected menu changes
watch(selectedMenu, async (newMenu) => {
  if (newMenu) {
    await loadMenuData()
  } else {
    resetState()
    selectedItem.value = null
    editingMenuItem.value = null
  }
})

// Initialize
onMounted(async () => {
  await fetchMenus()

  // Auto-select first menu if available
  if (menus.value && menus.value.length > 0) {
    selectedMenu.value = menus.value[0]
  }
})

// Permission checks
const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)
const userPermissions = getUserPermissions(authUser.value)

const canCreateMenu = hasPermission(['menu_management.create'], userPermissions)
const canEditMenu = hasPermission(['menu_management.edit'], userPermissions)
const canDeleteMenu = hasPermission(['menu_management.delete'], userPermissions)
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/cms/menu-list' as *;
</style>
