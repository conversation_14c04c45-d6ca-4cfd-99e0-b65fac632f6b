import apiAxios from '@/utils/configs/axios.config.js'

const galleriesApi = {
  // Get gallery list with pagination and filters
  getGalleries(params = {}) {
    return apiAxios({
      method: 'get',
      url: 'cms/gallery-groups',
      params,
    })
  },
  getGalleryById(id) {
    return apiAxios({
      method: 'get',
      url: `cms/gallery-groups/${id}`,
    })
  },

  /**
   * L<PERSON>y danh s<PERSON>ch các ảnh trong 1 nhóm thư viên ảnh cụ thể
   * @param {*} id - id Gallary Group
   * @param {} params - limit: số lượng bản ghi trong 1 page, page: trang hiện tại
   * @returns
   */
  getItemsByGalleryGroupId(id, params = {}) {
    return apiAxios({
      method: 'get',
      url: `cms/gallery-groups/${id}/items`,
      params,
    })
  },

  // Create new gallery
  createGallery(data) {
    return apiAxios({
      method: 'post',
      url: 'cms/gallery-groups',
      data,
    })
  },
  // Update gallery
  updateGallery(id, data) {
    return apiAxios({
      method: 'put',
      url: `cms/gallery-groups/${id}`,
      data,
    })
  },
  // Delete gallery
  deleteGallery(id) {
    return apiAxios({
      method: 'delete',
      url: `cms/gallery-groups/${id}`,
    })
  },

  // === Gallery Items ===

  /**
   * Thêm một ảnh mới vào nhóm gallery.
   * @param {string} groupId - ID của nhóm gallery.
   * @param {object} itemData - Dữ liệu của ảnh mới.
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  createGalleryItem(groupId, itemData) {
    return apiAxios({
      method: 'post',
      url: `cms/gallery-groups/${groupId}/items`,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: itemData,
    })
  },

  /**
   * Cập nhật thông tin một ảnh.
   * @param {string} groupId - ID của nhóm gallery.
   * @param {string} itemId - ID của ảnh cần cập nhật.
   * @param {object} itemData - Dữ liệu cập nhật.
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  updateGalleryItem(groupId, itemId, itemData) {
    return apiAxios({
      method: 'post',
      url: `cms/gallery-groups/${groupId}/items/${itemId}`,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: itemData,
    })
  },

  /**
   * Xóa một ảnh khỏi nhóm gallery.
   * @param {string} groupId - ID của nhóm gallery.
   * @param {string} itemId - ID của ảnh cần xóa.
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  deleteGalleryItem(groupId, itemId) {
    return apiAxios({
      method: 'delete',
      url: `cms/gallery-groups/${groupId}/items/${itemId}`,
    })
  },
}

export default galleriesApi
