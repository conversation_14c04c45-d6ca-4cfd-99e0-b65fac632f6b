.form-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: transparent;
}

.form-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

.form-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-card:last-child {
  margin-bottom: 0;
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  padding: 1.5rem;
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.footer-btn {
  height: 40px !important;
  padding: 0 24px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  min-width: 80px !important;
}

.cancel-btn {
  color: #6b7280 !important;
  border-color: #d1d5db !important;
}

/* Upload styles */
.upload-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.upload-dragger {
  width: 100%;
  aspect-ratio: 16/9;
  min-height: 180px;
  max-width: 420px;
  margin: 0 auto;
  border: 2px dashed #b3b3b3;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 80%, #f3e8ff 100%);
  position: relative;
  transition:
    border-color 0.2s,
    box-shadow 0.2s;
  box-shadow: 0 4px 24px 0 rgba(80, 80, 120, 0.07);
}

.upload-dragger:hover {
  border-color: #8b5cf6;
  box-shadow: 0 8px 32px 0 rgba(139, 92, 246, 0.1);
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(80, 80, 120, 0.08);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  transition:
    box-shadow 0.2s,
    filter 0.2s;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.image-preview img:hover {
  filter: brightness(0.97) saturate(1.1);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.13);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(80, 80, 120, 0.13);
  opacity: 0;
  border-radius: 16px;
  transition: opacity 0.2s;
  pointer-events: none;
}

.image-preview:hover .image-overlay {
  opacity: 1;
  pointer-events: auto;
}

.image-overlay .btn-common {
  background: linear-gradient(90deg, #8b5cf6 60%, #a78bfa 100%);
  color: #fff;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.13);
  padding: 8px 18px;
  font-size: 1rem;
  transition:
    background 0.2s,
    box-shadow 0.2s;
}

.image-overlay .btn-common:hover {
  background: linear-gradient(90deg, #7c3aed 60%, #a78bfa 100%);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.18);
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #b3b3b3;
  background: linear-gradient(135deg, #f3e8ff 60%, #f8fafc 100%);
  border-radius: 16px;
  transition: background 0.2s;
}

.upload-icon {
  font-size: 2.5rem;
  color: #a78bfa;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 1rem;
  color: #8b5cf6;
  font-weight: 500;
  text-align: center;
}

.upload-hint {
  font-size: 0.85rem;
  color: #b3b3b3;
  margin-top: 2px;
}

.remove-image-btn.btn-common {
  margin-top: 10px;
  color: #fff;
  background: linear-gradient(90deg, #f87171 60%, #fbbf24 100%);
  border-radius: 8px;
  font-weight: 600;
  border: none;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.13);
  padding: 6px 16px;
  font-size: 0.95rem;
  transition:
    background 0.2s,
    box-shadow 0.2s;
}

.remove-image-btn.btn-common:hover {
  background: linear-gradient(90deg, #ef4444 60%, #f59e42 100%);
  box-shadow: 0 4px 16px rgba(251, 191, 36, 0.18);
}

.required-asterisk {
  color: #ef4444;
  font-weight: 600;
  margin-left: 4px;
}

/* Hide Element Plus default required asterisk */
:deep(.el-form-item.is-required .el-form-item__label::before) {
  display: none !important;
}

:deep(.el-form-item.is-required::before) {
  display: none !important;
}

:deep(.el-form-item__label::before) {
  display: none !important;
}

/* Error state styling */
:deep(.el-input.is-error .el-input__wrapper) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 1px #f56c6c !important;
}

:deep(.el-textarea.is-error .el-textarea__inner) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 1px #f56c6c !important;
}

:deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  margin: 0;
}

:deep(.el-input-number.is-error .el-input__wrapper) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 1px #f56c6c !important;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

// Dark mode support
.dark {
  .form-card {
    background: #18181c;
    border-color: #23232b;
    box-shadow: 0 1px 3px rgba(0,0,0,0.4);
  }
  
  .form-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.6);
  }
  
  .card-header {
    background: linear-gradient(135deg, #23232b 0%, #18181c 100%);
    border-bottom: 1px solid #23232b;
  }
  
  .card-title {
    color: #e0e7ef;
  }
  
  .form-help {
    color: #a1a1aa;
  }
  
  .footer-btn {
    color: #e0e7ef !important;
    background: #23232b !important;
    border-color: #23232b !important;
  }
  
  .cancel-btn {
    color: #a1a1aa !important;
    border-color: #23232b !important;
  }
  
  .submit-btn {
    background: linear-gradient(135deg, #2563eb 0%, #1e293b 100%) !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3) !important;
  }
  
  .submit-btn:hover {
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4) !important;
  }
  
  .upload-dragger {
    border-color: #23232b;
    background: #23232b;
  }
  
  .upload-dragger:hover {
    border-color: #2563eb;
  }
  
  .upload-icon {
    color: #a1a1aa;
  }
  
  .upload-text {
    color: #a1a1aa;
  }
  
  .upload-hint {
    color: #71717a !important;
  }
  
  :deep(.el-form-item__label) {
    color: #e0e7ef;
  }
} 