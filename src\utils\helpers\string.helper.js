/**
 * String Helper Functions
 */

/**
 * Generate URL-friendly slug from Vietnamese text
 * @param {string} text - Input text in Vietnamese
 * @param {string} separator - Separator character (default: '_')
 * @returns {string} - URL-friendly slug
 */
export const generateSlug = (text, separator = '_') => {
  if (!text) return ''

  // Vietnamese character mapping
  const vietnameseMap = {
    à: 'a',
    á: 'a',
    ạ: 'a',
    ả: 'a',
    ã: 'a',
    â: 'a',
    ầ: 'a',
    ấ: 'a',
    ậ: 'a',
    ẩ: 'a',
    ẫ: 'a',
    ă: 'a',
    ằ: 'a',
    ắ: 'a',
    ặ: 'a',
    ẳ: 'a',
    ẵ: 'a',
    è: 'e',
    é: 'e',
    ẹ: 'e',
    ẻ: 'e',
    ẽ: 'e',
    ê: 'e',
    ề: 'e',
    ế: 'e',
    ệ: 'e',
    ể: 'e',
    ễ: 'e',
    ì: 'i',
    í: 'i',
    ị: 'i',
    ỉ: 'i',
    ĩ: 'i',
    ò: 'o',
    ó: 'o',
    ọ: 'o',
    ỏ: 'o',
    õ: 'o',
    ô: 'o',
    ồ: 'o',
    ố: 'o',
    ộ: 'o',
    ổ: 'o',
    ỗ: 'o',
    ơ: 'o',
    ờ: 'o',
    ớ: 'o',
    ợ: 'o',
    ở: 'o',
    ỡ: 'o',
    ù: 'u',
    ú: 'u',
    ụ: 'u',
    ủ: 'u',
    ũ: 'u',
    ư: 'u',
    ừ: 'u',
    ứ: 'u',
    ự: 'u',
    ử: 'u',
    ữ: 'u',
    ỳ: 'y',
    ý: 'y',
    ỵ: 'y',
    ỷ: 'y',
    ỹ: 'y',
    đ: 'd',
    À: 'a',
    Á: 'a',
    Ạ: 'a',
    Ả: 'a',
    Ã: 'a',
    Â: 'a',
    Ầ: 'a',
    Ấ: 'a',
    Ậ: 'a',
    Ẩ: 'a',
    Ẫ: 'a',
    Ă: 'a',
    Ằ: 'a',
    Ắ: 'a',
    Ặ: 'a',
    Ẳ: 'a',
    Ẵ: 'a',
    È: 'e',
    É: 'e',
    Ẹ: 'e',
    Ẻ: 'e',
    Ẽ: 'e',
    Ê: 'e',
    Ề: 'e',
    Ế: 'e',
    Ệ: 'e',
    Ể: 'e',
    Ễ: 'e',
    Ì: 'i',
    Í: 'i',
    Ị: 'i',
    Ỉ: 'i',
    Ĩ: 'i',
    Ò: 'o',
    Ó: 'o',
    Ọ: 'o',
    Ỏ: 'o',
    Õ: 'o',
    Ô: 'o',
    Ồ: 'o',
    Ố: 'o',
    Ộ: 'o',
    Ổ: 'o',
    Ỗ: 'o',
    Ơ: 'o',
    Ờ: 'o',
    Ớ: 'o',
    Ợ: 'o',
    Ở: 'o',
    Ỡ: 'o',
    Ù: 'u',
    Ú: 'u',
    Ụ: 'u',
    Ủ: 'u',
    Ũ: 'u',
    Ư: 'u',
    Ừ: 'u',
    Ứ: 'u',
    Ự: 'u',
    Ử: 'u',
    Ữ: 'u',
    Ỳ: 'y',
    Ý: 'y',
    Ỵ: 'y',
    Ỷ: 'y',
    Ỹ: 'y',
    Đ: 'd',
  }

  return (
    text
      .trim()
      .toLowerCase()
      // Replace Vietnamese characters
      .replace(/./g, (char) => vietnameseMap[char] || char)
      // Remove special characters except alphanumeric and spaces
      .replace(/[^a-z0-9\s]/g, '')
      // Replace multiple spaces with single space
      .replace(/\s+/g, ' ')
      .trim()
      // Replace spaces with separator
      .replace(/\s/g, separator)
      // Remove multiple separators
      .replace(new RegExp(`\\${separator}+`, 'g'), separator)
      // Remove leading/trailing separators
      .replace(new RegExp(`^\\${separator}+|\\${separator}+$`, 'g'), '')
  )
}

/**
 * Generate slug with underscore separator (for categories)
 * @param {string} text - Input text
 * @returns {string} - Slug with underscores
 */

/**
 * Generate slug with dash separator (for URLs)
 * @param {string} text - Input text
 * @returns {string} - Slug with dashes
 */
export const generateSlugWithDash = (text) => {
  return generateSlug(text, '-')
}

/**
 * Capitalize first letter of each word
 * @param {string} text - Input text
 * @returns {string} - Capitalized text
 */
export const capitalizeWords = (text) => {
  if (!text) return ''
  return text.replace(/\b\w/g, (char) => char.toUpperCase())
}

/**
 * Truncate text to specified length
 * @param {string} text - Input text
 * @param {number} length - Maximum length
 * @param {string} suffix - Suffix to add (default: '...')
 * @returns {string} - Truncated text
 */
export const truncateText = (text, length = 100, suffix = '...') => {
  if (!text || text.length <= length) return text || ''
  return text.substring(0, length).trim() + suffix
}
