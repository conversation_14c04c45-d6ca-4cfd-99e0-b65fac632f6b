/**
 * Common Button Actions Composable
 * Provides standardized button configurations for common CRUD operations
 */

import { computed } from 'vue'
import {
  PlusIcon,
  RefreshIcon,
  EditIcon,
  TrashIcon,
  SettingsIcon,
  SaveIcon,
  EyeIcon,
} from '@/components/icons/index.js'

export function useButtonActions() {
  // Standard button configurations
  const buttonConfigs = {
    // Create actions
    create: {
      text: 'Tạo mới',
      icon: PlusIcon,
      type: 'primary',
      size: 'medium',
    },

    createMenu: {
      text: 'Tạo Menu',
      icon: PlusIcon,
      type: 'primary',
      size: 'medium',
    },

    createMenuItem: {
      text: 'Thêm Item',
      icon: PlusIcon,
      type: 'primary',
      size: 'small',
    },

    // Refresh/Sync actions
    refresh: {
      text: 'Làm mới',
      icon: RefreshIcon,
      type: 'default',
      size: 'medium',
    },

    sync: {
      text: 'Đồng bộ',
      icon: RefreshIcon,
      type: 'info',
      variant: 'outline',
      size: 'medium',
    },

    syncMenu: {
      text: 'Đồng bộ Menu',
      icon: RefreshIcon,
      type: 'default',
      size: 'medium',
    },

    syncMenuItems: {
      text: 'Đồng bộ Items',
      icon: RefreshIcon,
      type: 'info',
      variant: 'outline',
      size: 'small',
    },

    // Edit actions
    edit: {
      text: 'Sửa',
      icon: EditIcon,
      type: 'info',
      size: 'small',
    },

    editMenu: {
      text: 'Sửa Menu',
      icon: EditIcon,
      type: 'info',
      size: 'small',
    },

    editMenuItem: {
      text: 'Sửa Item',
      icon: EditIcon,
      type: 'info',
      size: 'small',
    },

    // Delete actions
    delete: {
      text: 'Xóa',
      icon: TrashIcon,
      type: 'danger',
      size: 'small',
    },

    deleteMenu: {
      text: 'Xóa Menu',
      icon: TrashIcon,
      type: 'danger',
      size: 'small',
    },

    deleteMenuItem: {
      text: 'Xóa Item',
      icon: TrashIcon,
      type: 'danger',
      size: 'small',
    },

    // Save actions
    save: {
      text: 'Lưu',
      icon: SaveIcon,
      type: 'primary',
      size: 'medium',
    },

    saveChanges: {
      text: 'Lưu thay đổi',
      icon: SaveIcon,
      type: 'primary',
      size: 'medium',
    },

    // View actions
    view: {
      text: 'Xem',
      icon: EyeIcon,
      type: 'default',
      size: 'small',
    },

    // Settings actions
    settings: {
      text: 'Cài đặt',
      icon: SettingsIcon,
      type: 'default',
      variant: 'ghost',
      size: 'medium',
    },

    // Icon-only variants
    iconOnly: {
      refresh: {
        icon: RefreshIcon,
        type: 'default',
        size: 'small',
        rounded: true,
      },

      settings: {
        icon: SettingsIcon,
        type: 'default',
        variant: 'ghost',
        size: 'small',
        rounded: true,
      },

      edit: {
        icon: EditIcon,
        type: 'info',
        size: 'small',
        rounded: true,
      },

      delete: {
        icon: TrashIcon,
        type: 'danger',
        size: 'small',
        rounded: true,
      },
    },
  }

  // Helper function to get button config
  const getButtonConfig = (actionType, variant = null) => {
    if (variant && buttonConfigs[variant] && buttonConfigs[variant][actionType]) {
      return buttonConfigs[variant][actionType]
    }

    return buttonConfigs[actionType] || buttonConfigs.create
  }

  // Helper function to create button props with loading state
  const createButtonProps = (actionType, options = {}) => {
    const baseConfig = getButtonConfig(actionType, options.variant)

    return {
      ...baseConfig,
      ...options,
      loading: options.loading || false,
      disabled: options.disabled || false,
    }
  }

  // Common button groups for different contexts
  const buttonGroups = {
    // Menu management buttons
    menuManagement: (loading = false) => ({
      create: createButtonProps('createMenu', { loading }),
      refresh: createButtonProps('refresh', { loading }),
      sync: createButtonProps('syncMenu', { loading }),
    }),

    // Menu item management buttons
    menuItemManagement: (loading = false) => ({
      create: createButtonProps('createMenuItem', { loading }),
      refresh: createButtonProps('refresh', { loading }),
      sync: createButtonProps('syncMenuItems', { loading }),
    }),

    // Table row actions
    tableRowActions: (loading = false) => ({
      edit: createButtonProps('edit', { loading }),
      delete: createButtonProps('delete', { loading }),
      view: createButtonProps('view', { loading }),
    }),

    // Icon-only actions
    iconActions: (loading = false) => ({
      refresh: createButtonProps('refresh', { variant: 'iconOnly', loading }),
      settings: createButtonProps('settings', { variant: 'iconOnly', loading }),
      edit: createButtonProps('edit', { variant: 'iconOnly', loading }),
      delete: createButtonProps('delete', { variant: 'iconOnly', loading }),
    }),
  }

  return {
    buttonConfigs,
    getButtonConfig,
    createButtonProps,
    buttonGroups,
  }
}

// Pre-defined action handlers composable
export function useCommonActions() {
  // Common confirmation dialogs
  const confirmDelete = async (itemName = 'mục này') => {
    const { ElMessageBox } = await import('element-plus')

    try {
      await ElMessageBox.confirm(`Bạn có chắc chắn muốn xóa ${itemName}?`, 'Xác nhận xóa', {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      })
      return true
    } catch {
      return false
    }
  }

  // Common success/error messages
  const showSuccessMessage = (message) => {
    import('element-plus').then(({ ElMessage }) => {
      ElMessage.success(message)
    })
  }

  const showErrorMessage = (message) => {
    import('element-plus').then(({ ElMessage }) => {
      ElMessage.error(message)
    })
  }

  // Standard CRUD action handlers
  const createActionHandlers = (options = {}) => {
    const { onRefresh, onCreate, onEdit, onDelete, onSync, itemName = 'mục' } = options

    return {
      async handleRefresh() {
        if (onRefresh) {
          await onRefresh()
          showSuccessMessage('Làm mới thành công')
        }
      },

      async handleCreate() {
        if (onCreate) {
          await onCreate()
        }
      },

      async handleEdit(item) {
        if (onEdit) {
          await onEdit(item)
        }
      },

      async handleDelete(item) {
        const confirmed = await confirmDelete(itemName)
        if (confirmed && onDelete) {
          try {
            await onDelete(item)
            showSuccessMessage(`Xóa ${itemName} thành công`)
          } catch (error) {
            showErrorMessage(`Không thể xóa ${itemName}`)
          }
        }
      },

      async handleSync() {
        if (onSync) {
          await onSync()
          showSuccessMessage('Đồng bộ thành công')
        }
      },
    }
  }

  return {
    confirmDelete,
    showSuccessMessage,
    showErrorMessage,
    createActionHandlers,
  }
}
