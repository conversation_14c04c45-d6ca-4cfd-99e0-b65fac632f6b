import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as yup from 'yup'
import { validate, DEFAULT_MESSAGES } from '@/utils/helpers/validate.helper.js'
import authApi from '@/utils/apis/auth.api.js'

/**
 * Change Password API Contract
 *
 * Unified naming convention between frontend and backend:
 * - old_password: Current password
 * - new_password: New password
 * - new_password_confirmation: Confirm new password
 *
 * No mapping needed - direct field name matching
 */

// Constants - Match backend field names exactly
const FORM_FIELDS = {
  OLD_PASSWORD: 'old_password',
  NEW_PASSWORD: 'new_password',
  NEW_PASSWORD_CONFIRMATION: 'new_password_confirmation',
}

const LABEL_MAP = {
  [FORM_FIELDS.OLD_PASSWORD]: 'Mật khẩu hiện tại',
  [FORM_FIELDS.NEW_PASSWORD]: 'Mật khẩu mới',
  [FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]: '<PERSON><PERSON><PERSON> nhận mật khẩu',
}

// Validation schema using backend field names
const VALIDATION_SCHEMA = yup.object().shape({
  [FORM_FIELDS.OLD_PASSWORD]: yup.string().required(DEFAULT_MESSAGES.passwordCurrent),
  [FORM_FIELDS.NEW_PASSWORD]: yup
    .string()
    .required(DEFAULT_MESSAGES.passwordNew)
    .min(8, 'Mật khẩu mới phải có ít nhất 8 ký tự')
    .passwordStrength(2, DEFAULT_MESSAGES.passwordStrength)
    .notSameAs(yup.ref(FORM_FIELDS.OLD_PASSWORD), DEFAULT_MESSAGES.passwordSame),
  [FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]: yup
    .string()
    .required('Xác nhận mật khẩu là bắt buộc')
    .confirmPassword(yup.ref(FORM_FIELDS.NEW_PASSWORD), DEFAULT_MESSAGES.passwordMatch),
})

export function useChangePassword() {
  // State - Using backend field names directly
  const loading = ref(false)

  const formData = reactive({
    [FORM_FIELDS.OLD_PASSWORD]: '',
    [FORM_FIELDS.NEW_PASSWORD]: '',
    [FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]: '',
  })

  const errors = reactive({
    [FORM_FIELDS.OLD_PASSWORD]: '',
    [FORM_FIELDS.NEW_PASSWORD]: '',
    [FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]: '',
  })

  // Computed
  const hasErrors = computed(() => {
    return Object.values(errors).some((error) => !!error)
  })

  const isFormValid = computed(() => {
    return Object.values(formData).every((value) => !!value.trim()) && !hasErrors.value
  })

  const canSubmit = computed(() => {
    return isFormValid.value && !loading.value
  })

  // Methods
  const clearErrors = () => {
    Object.keys(errors).forEach((key) => {
      errors[key] = ''
    })
  }

  const resetForm = () => {
    Object.keys(formData).forEach((key) => {
      formData[key] = ''
    })
    clearErrors()
  }

  const validateForm = async () => {
    clearErrors()

    const { errors: validationErrors } = await validate(VALIDATION_SCHEMA, formData, LABEL_MAP)

    if (Object.keys(validationErrors).length > 0) {
      Object.assign(errors, validationErrors)
      return false
    }

    return true
  }

  const handleApiError = (error) => {
    console.error('Change password error:', error)

    switch (error.status) {
      case 400:
      case 401:
        errors[FORM_FIELDS.OLD_PASSWORD] = error.data?.message || 'Mật khẩu hiện tại không đúng'
        break

      case 422:
        if (error.data?.errors) {
          Object.assign(errors, error.data.errors)
        }
        break

      default:
        ElMessage.error(error.message || 'Có lỗi xảy ra khi thay đổi mật khẩu!')
    }
  }

  // No mapping needed - direct field name matching
  const buildRequestData = () => ({
    [FORM_FIELDS.OLD_PASSWORD]: formData[FORM_FIELDS.OLD_PASSWORD],
    [FORM_FIELDS.NEW_PASSWORD]: formData[FORM_FIELDS.NEW_PASSWORD],
    [FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]: formData[FORM_FIELDS.NEW_PASSWORD_CONFIRMATION],
  })

  const validateRequestData = (requestData) => {
    const { old_password, new_password, new_password_confirmation } = requestData

    if (!old_password || !new_password || !new_password_confirmation) {
      ElMessage.error('Vui lòng điền đầy đủ thông tin!')
      return false
    }

    return true
  }

  const submitForm = async () => {
    if (!(await validateForm())) {
      return { success: false }
    }

    loading.value = true

    try {
      const requestData = buildRequestData()

      if (!validateRequestData(requestData)) {
        return { success: false }
      }

      await authApi.changePassword(requestData)

      ElMessage.success('Thay đổi mật khẩu thành công!')
      resetForm()

      return { success: true }
    } catch (error) {
      handleApiError(error)
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    formData,
    errors,

    // Computed
    hasErrors,
    isFormValid,
    canSubmit,

    // Methods
    clearErrors,
    resetForm,
    validateForm,
    submitForm,

    // Constants
    FORM_FIELDS,
  }
}
