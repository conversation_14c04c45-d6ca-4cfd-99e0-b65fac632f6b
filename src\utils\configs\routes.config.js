import Overview from '@/views/Overview.vue'
import Login from '@/views/Auth/Login.vue'
import UserProfile from '@/views/Account/UserProfile.vue'

import { UserList, RoleList } from '@/views/modules/users'
import {
  MenuList,
  CategoryList,
  PostList,
  PostCreate,
  PostEdit,
  PostDetail,
  PageList,
  GalleryList,
} from '@/views/modules/cms'
import SiteSettingGroups from '@/views/modules/cms/SiteSettingGroups.vue'
import ScriptList from '@/views/Scripts/ScriptList.vue'
import NotFound from '@/views/NotFound.vue'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'
import { UserLogs } from '@/views/modules/logs'

export default [
  // Auth
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: {
      title: 'Đăng nhập',
      authRequired: false,
      beforeResolve(routeTo, routeFrom, next) {
        const authStore = useAuthStore()
        const { isAuthenticated } = storeToRefs(authStore)
        if (isAuthenticated.value) {
          next({ name: 'overview' })
        } else {
          next()
        }
      },
    },
  },
  /* End Auth page */

  /* Begin Profile page */
  {
    path: '/profile',
    name: 'profile',
    component: UserProfile,
    meta: { title: 'Thông Tin Cá Nhân', authRequired: true },
  },
  // Dashboard
  {
    path: '/',
    name: 'overview',
    component: Overview,
    meta: { title: 'Tổng Quan', authRequired: true },
  },
  // Users
  {
    path: '/users',
    name: 'users',
    component: UserList,
    meta: { title: 'Quản lý người dùng', authRequired: true },
  },
  {
    path: '/roles',
    name: 'roles',
    component: RoleList,
    meta: { title: 'Quản lý vai trò & quyền hạn', authRequired: true },
  },
  // Static Pages
  {
    path: '/cms/static-pages',
    name: 'cms-static-pages',
    component: PageList,
    meta: { title: 'Quản lý trang tĩnh', authRequired: true },
  },
  // CMS
  {
    path: '/cms/menus',
    name: 'cms-menus',
    component: MenuList,
    meta: { title: 'Quản lý Menu', authRequired: true },
  },
  {
    path: '/cms/posts',
    name: 'cms-posts',
    component: PostList,
    meta: { title: 'Quản lý Bài viết', authRequired: true },
  },
  {
    path: '/cms/posts/create',
    name: 'cms-posts-create',
    component: PostCreate,
    meta: { title: 'Tạo Bài viết', authRequired: true },
  },
  {
    path: '/cms/posts/:id',
    name: 'cms-posts-detail',
    component: PostDetail,
    meta: { title: 'Chi tiết Bài viết', authRequired: true },
  },
  {
    path: '/cms/posts/edit/:id',
    name: 'cms-posts-edit',
    component: PostEdit,
    meta: { title: 'Chỉnh sửa Bài viết', authRequired: true },
  },
  {
    path: '/cms/categories',
    name: 'cms-categories',
    component: CategoryList,
    meta: { title: 'Quản lý Danh mục', authRequired: true },
  },

  {
    path: '/cms/site-setting-groups',
    name: 'cms-site-setting-groups',
    component: SiteSettingGroups,
    meta: { title: 'Nhóm cài đặt hệ thống', authRequired: true },
  },
  // Scripts
  {
    path: '/scripts',
    name: 'scripts',
    component: ScriptList,
    meta: { title: 'Quản lý Script', authRequired: true },
  },
  // Gallery
  {
    path: '/cms/gallery',
    name: 'cms-gallery',
    component: GalleryList,
    meta: { title: 'Quản lý Gallery', authRequired: true },
  },

  // Logs
  {
    path: '/log/user-logs',
    name: 'user-logs',
    component: UserLogs,
    meta: {
      title: 'Lịch sử hoạt động',
      authRequired: true,
    },
  },
  // 404
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: NotFound,
    meta: { title: 'Trang không tồn tại', authRequired: false },
  },
  // 403 Forbidden
  {
    path: '/forbidden',
    name: 'forbidden',
    component: () => import('@/views/Forbidden.vue'),
    meta: { title: 'Không có quyền truy cập', authRequired: false },
  },
]
