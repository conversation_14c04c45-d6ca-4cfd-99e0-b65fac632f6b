<template>
  <div class="role-list-wrapper">
    <PageBreadcrumb
      :page-title="currentPageTitle"
      :breadcrumbs="[
        { label: 'Trang chủ', to: '/' },
        { label: '<PERSON>u<PERSON><PERSON> lý người dùng', to: '/users' },
      ]"
    />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Two Column Layout: 1/3 - 2/3 ratio -->
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Left Column: Roles List (1/3 width) -->
        <div class="space-y-4 lg:col-span-1">
          <RoleSidebar
            :roles="roles"
            :selected-role="selectedRole"
            :loading="loading"
            :filters="filters"
            :real-time-permission-counts="realTimePermissionCounts"
            :loading-permissions="loadingPermissions"
            @create-role="handleCreateRole"
            @edit-role="handleEditRole"
            @delete-role="handleDeleteRole"
            @select-role="handleSelectRole"
            @search="handleSearch"
            @reset-filters="handleResetFilters"
            :can-create="canCreateRole"
            :can-edit="canEditRole"
            :can-delete="canDeleteRole"
          />
        </div>

        <!-- Right Column: Permissions Matrix (2/3 width) -->
        <div class="lg:col-span-2">
          <template v-if="canViewPermissionTable">
            <PermissionTable
              ref="permissionTableRef"
              :selected-role="selectedRole"
              :permissions="rolePermissions"
              :permissions-loading="loadingPermissions"
              @permission-changed="handlePermissionChange"
              :can-assign="canAssignPermission"
            >
              <template #role-info>
                <RoleCard :role="selectedRole" :permission-count="getSelectedRolePermissionCount()" />
              </template>
            </PermissionTable>
          </template>
          <template v-else>
            <div class="flex h-96 items-center justify-center">
              <div
                class="rounded-lg bg-yellow-50 p-6 text-center text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300"
              >
                <strong>Bạn không có quyền xem bảng phân quyền.</strong>
                <div class="mt-2 text-sm">Vui lòng liên hệ quản trị viên để được cấp quyền xem bảng phân quyền.</div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- Role Form Modal -->
      <RoleModal
        v-model:visible="showRoleForm"
        :role="selectedRole"
        @success="handleRoleFormSuccess"
        :can-create="canCreateRole"
        :can-edit="canEditRole"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoles } from '@/composables/modules/users/useRoles.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import { RoleSidebar, PermissionTable, RoleCard, RoleModal } from '@/components/pages/roles'
import { hasPermission, getUserPermissions } from '@/utils/helpers/permission.helper.js'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'

// Page title
const currentPageTitle = ref('Quản lý vai trò & quyền hạn')

// Composables
const { loading, roles, role, rolePermissions, filters, fetchRoles, fetchRole, deleteRole, searchRoles, resetFilters } =
  useRoles()

// Local state
const showRoleForm = ref(false)
const showRoleDetail = ref(false)
const selectedRole = ref(null)
const loadingPermissions = ref(false)
const permissionTableRef = ref(null)
const realTimePermissionCounts = ref({})

// Methods
const handleCreateRole = () => {
  selectedRole.value = null
  showRoleForm.value = true
}

const handleEditRole = (role) => {
  selectedRole.value = role
  showRoleForm.value = true
}

const handleViewRole = (role) => {
  selectedRole.value = role
  showRoleDetail.value = true
}

const handleDeleteRole = async (role) => {
  await deleteRole(role.id)
  await fetchRoles()

  // Auto-select first role after delete if current selected role was deleted
  if (
    roles.value &&
    roles.value.length > 0 &&
    (!selectedRole.value || !roles.value.find((r) => r.id === selectedRole.value.id))
  ) {
    selectedRole.value = roles.value[0]
    await loadRolePermissions(selectedRole.value.id)
  }
}

const handleSelectRole = async (role) => {
  selectedRole.value = role
  await loadRolePermissions(role.id)
}

// Load permissions data for selected role
const loadRolePermissions = async (roleId) => {
  try {
    loadingPermissions.value = true

    await fetchRole(roleId)

    if (role.value) {
      // Update selected role with full data from API
      selectedRole.value = { ...role.value }
    } else {
      throw new Error('Có lỗi xảy ra khi tải quyền của vai trò')
    }
  } catch (error) {
    ElMessage.error('Không thể tải quyền của vai trò')
  } finally {
    loadingPermissions.value = false
  }
}

const handleSearch = async (searchTerm) => {
  try {
    // Update filters and search
    filters.value.search = searchTerm
    await searchRoles()

    // Auto-select first role after search if current role is not in results
    if (roles.value && roles.value.length > 0) {
      const currentRoleInResults = selectedRole.value && roles.value.find((r) => r.id === selectedRole.value.id)

      if (!currentRoleInResults) {
        selectedRole.value = roles.value[0]
        await loadRolePermissions(selectedRole.value.id)
      }
    } else {
      // Clear selected role if no results
      selectedRole.value = null
    }
  } catch (error) {
    ElMessage.error('Không thể tìm kiếm vai trò')
  }
}

const handleResetFilters = async () => {
  try {
    resetFilters()
    await fetchRoles()

    // Auto-select first role after reset
    if (roles.value && roles.value.length > 0) {
      selectedRole.value = roles.value[0]
      await loadRolePermissions(selectedRole.value.id)
    } else {
      selectedRole.value = null
    }
  } catch (error) {
    ElMessage.error('Không thể reset bộ lọc')
  }
}

const handlePermissionChange = async () => {
  // Update real-time permission count for current role without API call
  if (selectedRole.value && permissionTableRef.value) {
    const newCount = permissionTableRef.value.activePermissionCount
    if (newCount !== undefined) {
      realTimePermissionCounts.value[selectedRole.value.id] = newCount

      // Update the selected role's permission count locally
      if (selectedRole.value) {
        selectedRole.value.permissions_count = newCount
      }

      // Update the role in the roles list locally
      const roleIndex = roles.value.findIndex((r) => r.id === selectedRole.value.id)
      if (roleIndex !== -1) {
        roles.value[roleIndex].permissions_count = newCount
      }
    }
  }
}

const getSelectedRolePermissionCount = () => {
  // Use real-time count from PermissionTable if available
  if (permissionTableRef.value && permissionTableRef.value.activePermissionCount !== undefined) {
    return permissionTableRef.value.activePermissionCount
  }

  // Fallback to rolePermissions count
  if (!rolePermissions.value || rolePermissions.value.length === 0) return 0

  // Count active permissions from the current permissions structure
  const countActivePermissions = (groups) => {
    let count = 0
    for (const group of groups) {
      // Count direct active permissions
      if (group.permissions) {
        count += group.permissions.filter((p) => p.active).length
      }
      // Count children active permissions
      if (group.children) {
        count += countActivePermissions(group.children)
      }
    }
    return count
  }

  return countActivePermissions(rolePermissions.value)
}

const handleRoleFormSuccess = async () => {
  showRoleForm.value = false
  selectedRole.value = null
  await fetchRoles()

  // Auto-select first role after refresh
  if (roles.value && roles.value.length > 0) {
    selectedRole.value = roles.value[0]
    await loadRolePermissions(selectedRole.value.id)
  }
}

const handleEditFromDetail = (role) => {
  showRoleDetail.value = false
  selectedRole.value = role
  showRoleForm.value = true
}

// Lifecycle
onMounted(async () => {
  await fetchRoles()

  // Initialize real-time permission count after component is mounted
  nextTick(() => {
    if (permissionTableRef.value && selectedRole.value) {
      const count = permissionTableRef.value.activePermissionCount
      if (count !== undefined) {
        realTimePermissionCounts.value[selectedRole.value.id] = count
      }
    }
  })
})

// Watch for roles changes to auto-select first role
watch(
  roles,
  async (newRoles) => {
    if (newRoles && newRoles.length > 0 && !selectedRole.value) {
      selectedRole.value = newRoles[0]
      await loadRolePermissions(selectedRole.value.id)
    }
  },
  { immediate: true },
)

// Watch for permission table changes to update real-time counts
watch(
  () => permissionTableRef.value?.activePermissionCount,
  (newCount) => {
    if (selectedRole.value && newCount !== undefined) {
      realTimePermissionCounts.value[selectedRole.value.id] = newCount
    }
  },
  { immediate: false },
)

// Watch for selected role changes to update real-time count
watch(
  selectedRole,
  async (newRole) => {
    if (newRole) {
      // Wait for permission table to be ready
      await nextTick()
      if (permissionTableRef.value) {
        const count = permissionTableRef.value.activePermissionCount
        if (count !== undefined) {
          realTimePermissionCounts.value[newRole.id] = count
        }
      }
    }
  },
  { immediate: false },
)

const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)
const userPermissions = getUserPermissions(authUser.value)

const canCreateRole = hasPermission(['role_management.create'], userPermissions)
const canEditRole = hasPermission(['role_management.edit'], userPermissions)
const canDeleteRole = hasPermission(['role_management.delete'], userPermissions)
const canAssignPermission = hasPermission(['permission_management.edit'], userPermissions)
const canViewPermissionTable = hasPermission(['permission_management.view'], userPermissions)
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/users/_role-list.scss' as *;
</style>
