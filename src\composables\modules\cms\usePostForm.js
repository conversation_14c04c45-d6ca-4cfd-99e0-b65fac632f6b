/**
 * Post Form Composable
 * Handles form logic for creating and editing posts
 */

import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { uploadApi } from '@/utils/apis/index.js'

export function usePostForm() {
  // Form state
  const formRef = ref()
  const formData = reactive({
    title: '',
    slug: '',
    excerpt: '',
    body: '',
    cover_image: '', // Có thể là File object hoặc string URL
    cover_image_preview: null, // URL preview cho File object
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    category_id: null,
    status: 'draft',
    is_hot: false,
    show_on_homepage: false,
    published_at: null,
  })

  // Upload state
  const uploadProgress = ref(0)
  const isManualSlug = ref(false)

  // Form validation rules
  const formRules = {
    category_id: [{ required: true, message: 'Vui lòng chọn danh mục', trigger: 'change' }],
    title: [
      { required: true, message: '<PERSON>ui lòng nhập tiêu đề bài viết', trigger: ['blur', 'change'] },
      { min: 3, max: 255, message: 'Ti<PERSON>u đề phải từ 3 đến 255 ký tự', trigger: ['blur', 'change'] },
    ],
    slug: [
      { required: true, message: 'Vui lòng nhập slug', trigger: ['blur', 'change'] },
      { min: 3, max: 255, message: 'Slug phải từ 3 đến 255 ký tự', trigger: ['blur', 'change'] },
      {
        pattern: /^[a-z0-9-]+$/,
        message: 'Slug chỉ được chứa chữ cái thường, số và dấu gạch ngang',
        trigger: ['blur', 'change'],
      },
    ],
    body: [
      { required: true, message: 'Vui lòng nhập nội dung bài viết', trigger: ['blur', 'change'] },
      {
        validator: (rule, value, callback) => {
          // Strip HTML tags and check content length
          const textContent = value?.replace(/<[^>]*>/g, '').trim() || ''
          if (!textContent) {
            callback(new Error('Vui lòng nhập nội dung bài viết'))
          } else if (textContent.length < 10) {
            callback(new Error('Nội dung phải có ít nhất 10 ký tự'))
          } else {
            callback()
          }
        },
        trigger: ['blur', 'change'],
      },
    ],
    excerpt: [{ max: 500, message: 'Tóm tắt không được vượt quá 500 ký tự', trigger: ['blur', 'change'] }],
    meta_title: [{ max: 60, message: 'Meta title không được vượt quá 60 ký tự', trigger: ['blur', 'change'] }],
    meta_description: [{ max: 160, message: 'Meta description không được vượt quá 160 ký tự', trigger: ['blur', 'change'] }],
    meta_keywords: [{ max: 255, message: 'Meta keywords không được vượt quá 255 ký tự', trigger: ['blur', 'change'] }],
  }

  // Status options
  const statusOptions = [
    { value: 'draft', label: 'Bản nháp' },
    { value: 'pending_review', label: 'Chờ duyệt' },
    { value: 'published', label: 'Đã xuất bản' },
  ]

  // Editor options
  const editorOptions = {
    theme: 'snow',
    modules: {
      toolbar: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['clean'],
        ['link', 'image', 'video'],
      ],
    },
  }

  // Methods
  const generateSlug = () => {
    if (!isManualSlug.value && formData.title) {
      formData.slug = formData.title
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[đĐ]/g, 'd')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-')
    }
  }

  const handleSlugInput = () => {
    isManualSlug.value = true
  }

  const handleBeforeUpload = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt5M = file.size / 1024 / 1024 < 5

    if (!isImage) {
      ElMessage.error('Chỉ được upload file ảnh!')
      return false
    }

    if (!isLt5M) {
      ElMessage.error('Kích thước ảnh phải nhỏ hơn 5MB!')
      return false
    }

    return true
  }

  const handleUpload = async ({ file }) => {
    try {
      uploadProgress.value = 10

      const response = await uploadApi.uploadImage(file, 'posts')

      uploadProgress.value = 100

      if (response.data.success) {
        formData.cover_image = response.data.data.path
        ElMessage.success('Tải ảnh lên thành công!')
      } else {
        throw new Error(response.data.message || 'Tải ảnh lên thất bại')
      }
    } catch (error) {
      console.error('Error uploading image:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Tải ảnh lên thất bại')
    } finally {
      setTimeout(() => {
        uploadProgress.value = 0
      }, 1000)
    }
  }

  const removeImage = () => {
    formData.cover_image = ''
  }

  const getImageUrl = (imagePath) => {
    if (!imagePath) return ''
    if (imagePath.startsWith('http')) return imagePath
    return `${import.meta.env.VITE_API_BASE_URL}${imagePath}`
  }

  const handleImageError = () => {
    ElMessage.error('Không thể tải ảnh')
  }

  const resetForm = () => {
    // Cleanup preview URL để tránh memory leak
    if (formData.cover_image_preview) {
      URL.revokeObjectURL(formData.cover_image_preview)
    }

    Object.assign(formData, {
      title: '',
      slug: '',
      excerpt: '',
      body: '',
      cover_image: '',
      cover_image_preview: null,
      meta_title: '',
      meta_description: '',
      meta_keywords: '',
      category_id: null,
      status: 'draft',
      is_hot: false,
      show_on_homepage: false,
      published_at: null,
    })
    isManualSlug.value = false
    uploadProgress.value = 0
  }

  const loadPostData = (post) => {
    if (post) {
      Object.assign(formData, {
        title: post.title || '',
        slug: post.slug || '',
        excerpt: post.excerpt || '',
        body: post.body || '',
        cover_image: post.cover_image || '', // URL từ server
        cover_image_preview: null, // Reset preview cho ảnh mới
        meta_title: post.meta_title || '',
        meta_description: post.meta_description || '',
        meta_keywords: post.meta_keywords || '',
        category_id: post.category?.id || post.category_id || null,
        status: post.status || 'draft',
        is_hot: Boolean(post.is_hot), // Convert number to boolean
        show_on_homepage: Boolean(post.show_on_homepage), // Convert number to boolean
        published_at: post.published_at || null,
      })
    }
  }

  return {
    // State
    formRef,
    formData,
    uploadProgress,
    isManualSlug,
    formRules,
    statusOptions,
    editorOptions,

    // Methods
    generateSlug,
    handleSlugInput,
    handleBeforeUpload,
    handleUpload,
    removeImage,
    getImageUrl,
    handleImageError,
    resetForm,
    loadPostData,
  }
}
