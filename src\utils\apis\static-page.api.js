import apiAxios from '@/utils/configs/axios.config.js'

// Đường dẫn API base cho static page
const API_PATH = 'cms/static-pages'

/**
 * API wrapper cho quản lý trang tĩnh
 */
const staticPageApi = {
  /**
   * L<PERSON>y danh sách trang tĩnh
   * @param {Object} params - Tham số tìm kiếm, phân trang, sắp xếp
   * @returns {Promise} - Promise chứa kết quả API
   */
  getStaticPages(params) {
    return apiAxios({
      url: API_PATH,
      method: 'get',
      params,
    })
  },

  /**
   * L<PERSON>y chi tiết một trang tĩnh
   * @param {number|string} id - ID của trang tĩnh
   * @returns {Promise} - Promise chứa kết quả API
   */
  getStaticPage(id) {
    return apiAxios({
      url: `${API_PATH}/${id}`,
      method: 'get',
    })
  },

  /**
   * Tạ<PERSON> trang tĩnh mới
   * @param {Object} data - Dữ liệu trang tĩnh
   * @param {string} data.title - Tiêu đề trang
   * @param {string} data.slug - Đường dẫn trang
   * @param {string} data.body - Nội dung HTML của trang
   * @param {number} data.status - Trạng thái (1: kích hoạt, 0: vô hiệu)
   * @param {string} data.meta_title - Tiêu đề SEO
   * @param {string} data.meta_description - Mô tả SEO
   * @param {string} data.meta_keywords - Từ khóa SEO
   * @returns {Promise} - Promise chứa kết quả API
   */
  createStaticPage(data) {
    return apiAxios({
      url: API_PATH,
      method: 'post',
      data,
    })
  },

  /**
   * Cập nhật trang tĩnh
   * @param {number|string} id - ID của trang tĩnh
   * @param {Object} data - Dữ liệu cập nhật
   * @param {string} data.title - Tiêu đề trang
   * @param {string} data.slug - Đường dẫn trang
   * @param {string} data.body - Nội dung HTML của trang
   * @param {number} data.status - Trạng thái (1: kích hoạt, 0: vô hiệu)
   * @param {string} data.meta_title - Tiêu đề SEO
   * @param {string} data.meta_description - Mô tả SEO
   * @param {string} data.meta_keywords - Từ khóa SEO
   * @returns {Promise} - Promise chứa kết quả API
   */
  updateStaticPage(id, data) {
    return apiAxios({
      url: `${API_PATH}/${id}`,
      method: 'put',
      data,
    })
  },

  /**
   * Xóa trang tĩnh
   * @param {number|string} id - ID của trang tĩnh
   * @returns {Promise} - Promise chứa kết quả API
   */
  deleteStaticPage(id) {
    return apiAxios({
      url: `${API_PATH}/${id}`,
      method: 'delete',
    })
  },
}

export default staticPageApi
