<template>
  <div class="relative z-1 bg-white p-6 sm:p-0 dark:bg-gray-900">
    <div class="relative flex h-screen w-full flex-col justify-center lg:flex-row dark:bg-gray-900">
      <div class="flex w-full flex-1 flex-col lg:w-1/2">
        <div class="mx-auto flex w-full max-w-md flex-1 flex-col justify-center">
          <div>
            <div class="mb-0">
              <h1 class="text-title-sm sm:text-title-md mb-2 font-semibold text-gray-800 dark:text-white/90">
                ĐĂNG NHẬP
              </h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">Nhập email và mật khẩu để đăng nhập!</p>
            </div>
            <div>
              <div class="relative py-3 sm:py-5">
                <div class="absolute inset-0 flex items-center">
                  <div class="w-full border-t border-gray-200 dark:border-gray-800"></div>
                </div>
              </div>
              <form @submit.prevent="handleSubmit">
                <div class="space-y-5">
                  <!-- Email -->
                  <div>
                    <label for="email" class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400">
                      Email
                      <span class="text-error-500">*</span>
                    </label>
                    <input
                      v-model="form.email"
                      type="email"
                      id="email"
                      name="email"
                      placeholder="Nhập email. VD:<EMAIL>"
                      @input="clearFieldError('email')"
                      :class="[
                        'dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30',
                        errors.email ? 'border-red-500 dark:border-red-500' : 'border-gray-300 dark:border-gray-700',
                      ]"
                    />
                    <p v-if="errors.email" class="mt-1 text-sm text-red-500">{{ errors.email }}</p>
                  </div>
                  <!-- Password -->
                  <div>
                    <label for="password" class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400">
                      Password
                      <span class="text-error-500">*</span>
                    </label>
                    <div class="relative">
                      <input
                        v-model="form.password"
                        :type="showPassword ? 'text' : 'password'"
                        id="password"
                        placeholder="Nhập mật khẩu"
                        @input="clearFieldError('password')"
                        :class="[
                          'dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border bg-transparent py-2.5 pr-11 pl-4 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30',
                          errors.password
                            ? 'border-red-500 dark:border-red-500'
                            : 'border-gray-300 dark:border-gray-700',
                        ]"
                      />
                      <span
                        @click="togglePasswordVisibility"
                        class="absolute top-1/2 right-4 z-30 -translate-y-1/2 cursor-pointer text-gray-500 dark:text-gray-400"
                      >
                        <inline-svg v-if="!showPassword" :src="showPasswordIcon"></inline-svg>
                        <inline-svg v-else :src="hidePasswordIcon"></inline-svg>
                      </span>
                    </div>
                    <p v-if="errors.password" class="mt-1 text-sm text-red-500">{{ errors.password }}</p>
                  </div>
                  <!-- Checkbox -->
                  <div class="flex items-center justify-between">
                    <div>
                      <label
                        for="rememberMe"
                        class="flex cursor-pointer items-center text-sm font-normal text-gray-700 select-none dark:text-gray-400"
                      >
                        <div class="relative">
                          <input v-model="form.rememberMe" type="checkbox" id="rememberMe" class="sr-only" />
                          <div
                            :class="
                              form.rememberMe
                                ? 'border-brand-500 bg-brand-500'
                                : 'border-gray-300 bg-transparent dark:border-gray-700'
                            "
                            class="mr-3 flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
                          >
                            <span :class="form.rememberMe ? '' : 'opacity-0'">
                              <inline-svg :src="checkedIcon"></inline-svg>
                            </span>
                          </div>
                        </div>
                        Ghi nhớ đăng nhập
                      </label>
                    </div>
                  </div>
                  <!-- Error Message -->
                  <div v-if="generalError" class="rounded-lg bg-red-50 p-3 dark:bg-red-900/20">
                    <p class="text-sm text-red-600 dark:text-red-400">{{ generalError }}</p>
                  </div>
                  <!-- Button -->
                  <div>
                    <button
                      type="submit"
                      :disabled="loading"
                      class="bg-brand-500 shadow-theme-xs hover:bg-brand-600 flex w-full items-center justify-center rounded-lg px-4 py-3 text-sm font-medium text-white transition disabled:cursor-not-allowed disabled:bg-gray-400"
                    >
                      <span v-if="loading" class="mr-2">
                        <svg
                          class="h-4 w-4 animate-spin text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                          ></circle>
                          <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                      </span>
                      {{ loading ? 'Đang đăng nhập...' : 'Đăng Nhập' }}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-brand-950 relative hidden h-full w-full items-center lg:grid lg:w-1/2 dark:bg-white/5">
        <div class="z-1 flex items-center justify-center">
          <common-grid-shape />
          <div class="flex max-w-xs flex-col items-center">
            <router-link to="/" class="mb-4 block">
              <img width="{231}" height="{48}" :src="authLogo" alt="Logo" />
            </router-link>
            <p class="text-center text-gray-400 dark:text-white/60">
              {{ title }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import CommonGridShape from '@/components/common/CommonGridShape.vue'
import showPasswordIcon from '@/assets/images/icons/show-password.svg'
import hidePasswordIcon from '@/assets/images/icons/hide-password.svg'
import checkedIcon from '@/assets/images/icons/checked.svg'
import authLogo from '@/assets/images/logo/auth-logo.svg'
import { getEnv } from '@/utils/helpers/env.helper'
import { useAuthUser } from '@/composables/useAuthUser'
import { authApi } from '@/utils/apis'
import { saveData } from '@/utils/helpers/localStorage.helper.js'
import * as yup from 'yup'
import { validate, DEFAULT_MESSAGES } from '@/utils/helpers/validate.helper.js'

defineOptions({
  name: 'LoginPage',
})

const router = useRouter()
const loading = ref(false)

const form = reactive({
  email: '',
  password: '',
  rememberMe: false,
})

const errors = reactive({
  email: '',
  password: '',
})

const generalError = ref('')
const showPassword = ref(false)
const title = getEnv('VITE_APP_NAME')

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const loginSchema = yup.object().shape({
  email: yup.string().email(DEFAULT_MESSAGES.email).required(DEFAULT_MESSAGES.required),
  password: yup.string().min(6, DEFAULT_MESSAGES.min).required(DEFAULT_MESSAGES.required),
})
const labelMap = { email: 'Email', password: 'Mật khẩu' }

const handleSubmit = async () => {
  // Validate bằng helper chung
  const { errors: validateErrors } = await validate(loginSchema, form, labelMap)
  Object.keys(errors).forEach((key) => (errors[key] = ''))
  generalError.value = ''
  if (Object.keys(validateErrors).length > 0) {
    Object.assign(errors, validateErrors)
    return
  }
  loading.value = true
  try {
    const loginRes = await authApi.login({
      email: form.email,
      password: form.password,
      remember_me: form.rememberMe,
    })

    if (loginRes.data) {
      saveData('access_token', loginRes.data.data.access_token)
    }

    router.push({ name: 'overview' })
  } catch (err) {
    console.error('Login error:', err)
    if (err.response?.data?.errors) {
      handleServerErrors(err.response.data.errors)
    } else if (err.response?.data?.message) {
      generalError.value = err.response.data.message
    } else if (err.message) {
      generalError.value = err.message
    } else {
      generalError.value = 'Đăng nhập thất bại. Vui lòng thử lại.'
    }
  } finally {
    loading.value = false
  }
}

// Handle server validation errors
const handleServerErrors = (serverErrors) => {
  // Clear previous errors
  Object.keys(errors).forEach((key) => {
    errors[key] = ''
  })
  generalError.value = ''

  // Map server errors to form fields
  if (serverErrors.email) {
    errors.email = Array.isArray(serverErrors.email) ? serverErrors.email[0] : serverErrors.email
  }
  if (serverErrors.password) {
    errors.password = Array.isArray(serverErrors.password) ? serverErrors.password[0] : serverErrors.password
  }

  // Handle other server errors as general error
  const otherErrors = Object.keys(serverErrors).filter((key) => !['email', 'password'].includes(key))
  if (otherErrors.length > 0) {
    const firstError = otherErrors[0]
    generalError.value = Array.isArray(serverErrors[firstError])
      ? serverErrors[firstError][0]
      : serverErrors[firstError]
  }
}

// Clear field error when user starts typing
const clearFieldError = (fieldName) => {
  if (errors[fieldName]) {
    errors[fieldName] = ''
  }
  // Clear general error when user starts typing
  if (generalError.value) {
    generalError.value = ''
  }
}
</script>
