import { createRouter, createWebHistory } from 'vue-router'
import { getEnv } from '@/utils/helpers/env.helper.js'
import { getDataByKey } from '@/utils/helpers/localStorage.helper.js'
import { authApi } from '@/utils/apis'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'
import routes from '@/utils/configs/routes.config.js'

const router = createRouter({
  history: createWebHistory(),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0, left: 0 }
    }
  },
})

// Jwt Authentication
router.beforeEach(async (to, from, next) => {
  const publicPages = ['login']
  const isPublicPage = publicPages.includes(to.name)
  const authStore = useAuthStore()
  const { isAuthenticated, authUser } = storeToRefs(authStore)
  const { setAuthUser } = authStore

  // <PERSON><PERSON>m tra authentication bằng auth store
  if (!isAuthenticated.value) {
    // Thử lấy user từ API nếu có token nhưng chưa có user
    const token = getDataByKey('access_token')
    if (token && !authUser.value) {
      try {
        const res = await authApi.getAuthUser()
        if (res.data && res.data.data) {
          setAuthUser(res.data.data)
          // Nếu đang truy cập login và đã có user, redirect về overview
          if (isPublicPage) {
            return next({ name: 'overview' })
          }
          return next() // Đã có user, cho phép vào
        }
      } catch (error) {
        console.error('Failed to get auth user:', error)
        // Nếu API fail và đang truy cập public page, cho phép vào
        if (isPublicPage) {
          return next()
        }
        // Nếu API fail và không phải public page, redirect về login
        return next({ name: 'login' })
      }
    }
    // Chưa authenticated, chỉ cho phép vào public pages
    if (isPublicPage) {
      return next()
    }
    return next({ name: 'login' })
  }

  // Đã authenticated
  if (isPublicPage) {
    // Nếu đã đăng nhập mà truy cập login, redirect về overview
    return next({ name: 'overview' })
  }

  // Đã authenticated và không phải public page, cho phép vào
  return next()
})

router.beforeResolve(async (routeTo, routeFrom, next) => {
  for (const route of routeTo.matched) {
    if (route.meta?.beforeResolve) {
      await new Promise((resolve, reject) => {
        route.meta.beforeResolve(routeTo, routeFrom, (...args) => {
          if (args.length) {
            next(...args)
            reject(new Error('Redirected'))
          } else {
            resolve()
          }
        })
      })
    }
  }

  const title = getEnv('VITE_APP_NAME', 'Admin')
  document.title = `${routeTo.meta.title} | ${title}`
  next()
})

export default router
