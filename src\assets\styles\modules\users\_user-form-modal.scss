// User Form Modal Component Styles
// Extracted from UserFormModal.vue <style> section

:deep(.el-input__wrapper) {
  @apply border-gray-300 bg-gray-50 shadow-sm dark:border-gray-600 dark:bg-gray-800;
  @apply focus:border-brand-500 dark:focus:border-brand-400 focus:ring-brand-500/10 focus:ring-2;
  @apply hover:border-gray-400 dark:hover:border-gray-500;
  @apply transition-all duration-200;
}

:deep(.el-input__wrapper.is-disabled) {
  @apply border-gray-200 bg-gray-100 dark:border-gray-600 dark:bg-gray-700;
  @apply cursor-not-allowed;
}

:deep(.el-input__inner) {
  @apply text-gray-800 placeholder:text-gray-400 dark:text-white/90 dark:placeholder:text-gray-500;
  @apply text-base;
}

:deep(.el-input__inner:disabled) {
  @apply cursor-not-allowed text-gray-500 dark:text-gray-400;
}

:deep(.el-select .el-input__wrapper) {
  @apply border-gray-300 bg-gray-50 shadow-sm dark:border-gray-600 dark:bg-gray-800;
  @apply focus:border-brand-500 dark:focus:border-brand-400 focus:ring-brand-500/10 focus:ring-2;
  @apply hover:border-gray-400 dark:hover:border-gray-500;
  @apply transition-all duration-200;
}

:deep(.el-select .el-input__wrapper.is-disabled) {
  @apply border-gray-200 bg-gray-100 dark:border-gray-600 dark:bg-gray-700;
  @apply cursor-not-allowed;
}

:deep(.el-button--primary) {
  @apply bg-brand-500 hover:bg-brand-600 border-brand-500 hover:border-brand-600;
  @apply shadow-lg transition-all duration-200 hover:shadow-xl;
}

:deep(.el-button--primary:disabled) {
  @apply border-gray-300 bg-gray-300 dark:border-gray-600 dark:bg-gray-600;
  @apply cursor-not-allowed opacity-50;
}

:deep(.el-button--default) {
  @apply border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300;
  @apply hover:border-gray-400 hover:bg-gray-50 dark:hover:border-gray-500 dark:hover:bg-gray-800;
  @apply transition-all duration-200;
}
