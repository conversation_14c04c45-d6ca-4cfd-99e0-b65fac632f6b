# =========================================================================
# GIAI ĐOẠN 1: BUILD ỨNG DỤNG (DEBIAN-BASED)
# =========================================================================
# Sử dụng image Node.js v22.14.0 với kiến trúc x86_64
FROM --platform=linux/amd64 node:22.14.0 AS build

# --- THIẾT LẬP TIMEZONE CHO MÔI TRƯỜNG BUILD ---
# Đặt biến môi trường TZ và cấu hình timezone hệ thống cho Debian/Ubuntu.
# Điều này đảm bảo các log hoặc script trong quá trình build sẽ dùng đúng múi giờ.
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Cài đặt các gói cần thiết để build (nếu một số package npm cần native compilation)
RUN apt-get update && apt-get install -y python3 make g++ --no-install-recommends && rm -rf /var/lib/apt/lists/*

# Đặt thư mục làm việc trong container
WORKDIR /app

# Sao chép file package.json.
COPY package.json ./

# Cài đặt các dependencies của dự án.
RUN npm install --legacy-peer-deps

# Sao chép toàn bộ mã nguồn còn lại của dự án vào container.
COPY . .

# Sao chép file môi trường cho staging vào image và đổi tên thành .env
COPY .env.staging .env

# Thực thi lệnh build của dự án.
RUN npm run build

# =========================================================================
# GIAI ĐOẠN 2: SERVE ỨNG DỤNG VỚI NGINX (ALPINE-BASED)
# =========================================================================
# Sử dụng image Nginx siêu nhẹ
FROM nginx:1.24-alpine

# --- THIẾT LẬP TIMEZONE CHO MÔI TRƯỜNG RUNTIME ---
# Image Alpine mặc định không có tzdata, cần cài đặt nó.
# Sau đó thiết lập timezone tương tự như trên.
# Việc này rất quan trọng để log của Nginx ghi nhận đúng thời gian.
USER root
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime && \
    echo "Asia/Ho_Chi_Minh" > /etc/timezone
ENV TZ=Asia/Ho_Chi_Minh

# Tạo thư mục chứa PID file và cấp quyền
RUN mkdir -p /var/run/nginx && \
    chown -R nginx:nginx /var/run/nginx && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Sao chép file cấu hình Nginx tùy chỉnh vào container.
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# Sao chép các tệp đã được build ở Giai đoạn 1 vào thư mục phục vụ web của Nginx.
COPY --from=build /app/dist /usr/share/nginx/html

# Cấp quyền cần thiết cho user nginx
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chmod -R 755 /var/cache/nginx

# Chuyển sang user không phải root để tăng cường bảo mật
USER nginx

# Mở port 80 của container
EXPOSE 80

# Lệnh mặc định để khởi động Nginx ở chế độ foreground.
CMD ["nginx", "-g", "daemon off;"]
