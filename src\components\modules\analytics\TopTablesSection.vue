<template>
  <div class="top-tables-section">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Bảng TOP NẠP -->
      <el-card shadow="hover" class="table-card">
        <template #header>
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-800">TOP NẠP</h3>
          </div>
        </template>
        <el-table
          :data="topRechargeData"
          style="width: 100%"
          size="small"
          :show-header="true"
          class="custom-table"
          v-loading="isLoadingTopRecharge"
          element-loading-text="Đang tải..."
        >
          <el-table-column prop="accountName" label="Tên tài khoản" min-width="120" />
          <el-table-column prop="totalAmount" label="Tổng tiền nạp" min-width="120">
            <template #default="scope">
              <span class="font-semibold text-green-600">{{ formatCurrency(scope.row.totalAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Hành động" min-width="120" fixed="right" align="center">
            <template #default="scope">
              <div class="action-buttons">
                <ButtonCommon
                  type="info"
                  size="small"
                  :icon="InfoIcon"
                  tooltip="Kiểm tra thông tin tài khoản"
                  @click="handleAction('check-info', scope.row)"
                />
                <ButtonCommon
                  type="primary"
                  size="small"
                  :icon="HistoryIcon"
                  tooltip="Xem lịch sử nạp tiền"
                  @click="handleAction('view-history', scope.row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- Bảng TOP gói nạp phổ biến -->
      <el-card shadow="hover" class="table-card">
        <template #header>
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-800">TOP gói nạp phổ biến</h3>
          </div>
        </template>
        <el-table
          :data="topPackagesData"
          style="width: 100%"
          size="small"
          :show-header="true"
          class="custom-table"
          v-loading="isLoadingTopPackages"
          element-loading-text="Đang tải..."
        >
          <el-table-column prop="packageName" label="Tên gói" min-width="100" />
          <el-table-column prop="denomination" label="Mệnh giá" min-width="100">
            <template #default="scope">
              <span class="font-semibold text-blue-600">{{ formatCurrency(scope.row.denomination) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="purchaseCount" label="Số lần mua" min-width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.purchaseCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="revenue" label="Doanh thu" min-width="120">
            <template #default="scope">
              <span class="font-semibold text-green-600">{{ formatCurrency(scope.row.revenue) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="percentage" label="Tỷ lệ %" min-width="80" align="center">
            <template #default="scope">
              <span class="font-semibold text-purple-600">{{ scope.row.percentage }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- Bảng Lịch sử nạp gần đây -->
      <el-card shadow="hover" class="table-card">
        <template #header>
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-800">Lịch sử nạp gần đây</h3>
          </div>
        </template>
        <el-table
          :data="recentHistoryData"
          style="width: 100%"
          size="small"
          :show-header="true"
          class="custom-table"
          v-loading="isLoadingRecentHistory"
          element-loading-text="Đang tải..."
        >
          <el-table-column type="index" label="STT" width="50" />
          <el-table-column prop="account" label="Tài khoản" min-width="100" />
          <el-table-column prop="server" label="Server" min-width="80" />
          <el-table-column prop="denomination" label="Mệnh giá" min-width="100">
            <template #default="scope">
              <span class="font-semibold text-purple-600">{{ formatCurrency(scope.row.denomination) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="Trạng thái" min-width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 1 ? 'Thành công' : 'Thất bại' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="Thời gian" min-width="120">
            <template #default="scope">
              <span class="text-gray-600">{{ formatDateTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElTag } from 'element-plus'
import { InfoFilled, Clock } from '@element-plus/icons-vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import overviewApi from '@/utils/apis/overview.api.js'
import {
  extractTopRechargeResponse,
  extractTopPackagesResponse,
  extractRecentHistoryResponse,
  extractErrorResponse
} from '@/utils/helpers/response.helper.js'

// Icons for actions
const InfoIcon = InfoFilled
const HistoryIcon = Clock

// Loading states
const isLoadingTopRecharge = ref(false)
const isLoadingTopPackages = ref(false)
const isLoadingRecentHistory = ref(false)

// Data refs for real API data
const topRechargeData = ref([])
const topPackagesData = ref([])
const recentHistoryData = ref([])

// Additional data refs for pagination and summary
const topRechargePagination = ref(null)
const topPackagesSummary = ref(null)
const recentHistoryPagination = ref(null)

// Format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date time
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return 'N/A'
  try {
    const date = new Date(dateTimeStr)
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date)
  } catch (error) {
    return dateTimeStr
  }
}

// Action handlers
const handleAction = (command, row) => {
  switch (command) {
    case 'check-info':
      ElMessage.info(`Kiểm tra thông tin tài khoản: ${row.accountName}`)
      // TODO: Implement check info functionality
      break
    case 'view-history':
      ElMessage.info(`Xem lịch sử nạp của: ${row.accountName}`)
      // TODO: Implement view history functionality
      break
    default:
      break
  }
}

// Load data functions - Sử dụng real API
const loadTopRechargeData = async () => {
  try {
    isLoadingTopRecharge.value = true
    const response = await overviewApi.getTopRecharge()
    const result = extractTopRechargeResponse(response)

    if (result.success) {
      topRechargeData.value = result.data
      topRechargePagination.value = result.pagination
      console.log('Top recharge data loaded successfully:', result.data)
    } else {
      ElMessage.error(result.message || 'Không thể tải dữ liệu top nạp')
    }
  } catch (error) {
    console.error('Error loading top recharge data:', error)
    const errorResult = extractErrorResponse(error)
    ElMessage.error(errorResult.message || 'Không thể tải dữ liệu top nạp')
  } finally {
    isLoadingTopRecharge.value = false
  }
}

const loadTopPackagesData = async () => {
  try {
    isLoadingTopPackages.value = true
    const response = await overviewApi.getTopPackages()
    const result = extractTopPackagesResponse(response)

    if (result.success) {
      topPackagesData.value = result.data
      topPackagesSummary.value = result.summary
      console.log('Top packages data loaded successfully:', result.data)
    } else {
      ElMessage.error(result.message || 'Không thể tải dữ liệu top gói nạp')
    }
  } catch (error) {
    console.error('Error loading top packages data:', error)
    const errorResult = extractErrorResponse(error)
    ElMessage.error(errorResult.message || 'Không thể tải dữ liệu top gói nạp')
  } finally {
    isLoadingTopPackages.value = false
  }
}

const loadRecentHistoryData = async () => {
  try {
    isLoadingRecentHistory.value = true
    const response = await overviewApi.getRecentHistory()
    const result = extractRecentHistoryResponse(response)

    if (result.success) {
      recentHistoryData.value = result.data
      recentHistoryPagination.value = result.pagination
      console.log('Recent history data loaded successfully:', result.data)
    } else {
      ElMessage.error(result.message || 'Không thể tải dữ liệu lịch sử nạp')
    }
  } catch (error) {
    console.error('Error loading recent history data:', error)
    const errorResult = extractErrorResponse(error)
    ElMessage.error(errorResult.message || 'Không thể tải dữ liệu lịch sử nạp')
  } finally {
    isLoadingRecentHistory.value = false
  }
}

onMounted(() => {
  // Load all data when component mounts
  loadTopRechargeData()
  loadTopPackagesData()
  loadRecentHistoryData()
})
</script>

<style lang="scss" scoped>
.top-tables-section {
  margin-top: 2rem;
}

.table-card {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;

  .dark & {
    background-color: #1f2937;
    border-color: #374151;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: #1f2937;

    .dark & {
      color: #f9fafb;
    }
  }
}

.custom-table {
  :deep(.el-table__header) {
    background-color: #f8fafc;

    .dark & {
      background-color: #374151;
    }

    th {
      background-color: #f8fafc !important;
      color: #374151;
      font-weight: 600;

      .dark & {
        background-color: #374151 !important;
        color: #d1d5db;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #f1f5f9;

        .dark & {
          background-color: #374151;
        }
      }
    }

    td {
      border-bottom: 1px solid #e5e7eb;

      .dark & {
        border-bottom-color: #4b5563;
        color: #d1d5db;
      }
    }
  }
}

// Action buttons styling
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

// Responsive design
@media (max-width: 1024px) {
  .grid-cols-1.lg\\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .table-card {
    margin-bottom: 1.5rem;
  }
}

// Dark mode support
.dark {
  .text-gray-800 {
    color: #f9fafb;
  }

  .text-green-600 {
    color: #34d399;
  }

  .text-blue-600 {
    color: #60a5fa;
  }

  .text-purple-600 {
    color: #a78bfa;
  }
}
</style>
