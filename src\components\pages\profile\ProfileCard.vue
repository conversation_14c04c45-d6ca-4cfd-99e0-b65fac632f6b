<template>
  <div>
    <div class="mb-6 rounded-2xl border border-gray-200 p-5 lg:p-6 dark:border-gray-800">
      <div class="flex flex-col gap-5 xl:flex-row xl:items-center xl:justify-between">
        <div class="flex w-full flex-col items-center gap-6 xl:flex-row">
          <div class="h-20 w-20 overflow-hidden rounded-full border border-gray-200 dark:border-gray-800">
            <img :src="userAvatar" :alt="userName" class="h-full w-full object-cover" />
          </div>
          <div class="order-3 xl:order-2">
            <h4 class="mb-2 text-center text-lg font-semibold text-gray-800 xl:text-left dark:text-white/90">
              {{ userName }}
            </h4>
            <div class="flex flex-col items-center gap-1 text-center xl:flex-row xl:gap-3 xl:text-left">
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ userEmail }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive, watch, onMounted } from 'vue'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'

const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)

// Computed properties for user data - reactive to authUser changes
const userName = computed(() => {
  return authUser.value?.name || 'Người dùng'
})

const userEmail = computed(() => {
  return authUser.value?.email || ''
})

const userAvatar = computed(() => {
  const avatarUrl = authUser.value?.avatar
  if (avatarUrl) {
    return avatarUrl
  }
  return 'https://ui-avatars.com/api/?name=' + encodeURIComponent(userName.value) + '&background=3b82f6&color=fff'
})

// Form data - reactive to authUser changes
const profileForm = reactive({
  name: '',
  email: '',
  avatar: '',
})

// Watch for changes in authUser to update form
watch(
  () => authUser.value,
  (newUser) => {
    if (newUser) {
      profileForm.name = newUser.name || ''
      profileForm.email = newUser.email || ''
      profileForm.avatar = newUser.avatar || ''
    }
  },
  { immediate: true, deep: true },
)

// Initialize on mount
onMounted(() => {
  // Component mounted
})
</script>
