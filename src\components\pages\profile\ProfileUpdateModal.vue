<template>
  <Modal v-model="isVisible" title="Cập nhật hồ sơ" width="500px" @close="handleClose">
    <template #body>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Profile Avatar Section -->
          <FormField label="Ảnh đại diện" :icon="User">
            <AvatarUpload
              v-model="avatarPreview"
              :name="formData[FORM_FIELDS.NAME]"
              :validate-file="validateFile"
              :disabled="loading"
              size="medium"
              @change="handleAvatarChange"
              @remove="removeAvatar"
            />
          </FormField>

          <!-- Form Fields -->
          <div class="space-y-6">
            <!-- Name Field -->
            <FormField label="Họ và tên" :icon="User" required :error="errors[FORM_FIELDS.NAME]">
              <el-input
                v-model="formData[FORM_FIELDS.NAME]"
                placeholder="Nhập họ và tên của bạn"
                :disabled="loading"
                size="large"
                clearable
              />
            </FormField>

            <!-- Email Field (Disabled) -->
            <FormField label="Email" :icon="Message" disabled description="Email không thể thay đổi">
              <el-input
                v-model="formData[FORM_FIELDS.EMAIL]"
                type="email"
                placeholder="Email của bạn"
                :disabled="true"
                size="large"
                class="disabled-input"
              />
            </FormField>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-4 border-t border-gray-200 pt-6 dark:border-gray-700">
            <el-button type="default" @click="handleClose" :disabled="loading" class="flex-1" size="large">
              <el-icon class="mr-1"><Close /></el-icon>
              Hủy
            </el-button>

            <el-button
              type="primary"
              :loading="loading"
              :disabled="!canSubmit || !hasChanges"
              class="flex-1"
              size="large"
              @click="handleSubmit"
            >
              <el-icon v-if="!loading" class="mr-1"><Check /></el-icon>
              {{ loading ? 'Đang cập nhật...' : 'Cập nhật' }}
            </el-button>
          </div>
        </form>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { computed, watch, nextTick } from 'vue'
import { ElButton, ElInput, ElIcon } from 'element-plus'
import { Close, User, Message, Check } from '@element-plus/icons-vue'

// Components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import AvatarUpload from '@/components/common/AvatarUpload.vue'

// Composables
import { useUpdateProfile } from '@/composables/useUpdateProfile.js'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'close'])

// Composables - Using unified field names
const {
  loading,
  formData,
  errors,
  selectedFile,
  avatarPreview,
  canSubmit,
  hasChanges,
  initializeForm,
  resetForm,
  handleAvatarChange,
  removeAvatar,
  validateFile,
  submitForm,
  isValidUrl,
  getInitials,
  FORM_FIELDS,
} = useUpdateProfile()

// Computed
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// Methods
const handleClose = () => {
  isVisible.value = false
  emit('close')
  resetForm()
}

const handleSubmit = async () => {
  const result = await submitForm()

  if (result.success) {
    handleClose()
  }
}

// Watchers
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      // Initialize form when modal opens
      nextTick(() => {
        initializeForm()
      })
    } else {
      // Reset form when modal closes
      nextTick(() => {
        resetForm()
      })
    }
  },
)
</script>

<style scoped>
:deep(.el-input__wrapper) {
  @apply border-gray-300 bg-gray-50 shadow-sm dark:border-gray-600 dark:bg-gray-800;
  @apply focus:border-brand-500 dark:focus:border-brand-400 focus:ring-brand-500/10 focus:ring-2;
  @apply hover:border-gray-400 dark:hover:border-gray-500;
  @apply transition-all duration-200;
}

:deep(.el-input__wrapper.is-disabled) {
  @apply border-gray-200 bg-gray-100 dark:border-gray-600 dark:bg-gray-700;
  @apply cursor-not-allowed;
}

:deep(.el-input__inner) {
  @apply text-gray-800 placeholder:text-gray-400 dark:text-white/90 dark:placeholder:text-gray-500;
  @apply text-base;
}

:deep(.el-input__inner:disabled) {
  @apply cursor-not-allowed text-gray-500 dark:text-gray-400;
}

:deep(.el-button--primary) {
  @apply bg-brand-500 hover:bg-brand-600 border-brand-500 hover:border-brand-600;
  @apply shadow-lg transition-all duration-200 hover:shadow-xl;
}

:deep(.el-button--primary:disabled) {
  @apply border-gray-300 bg-gray-300 dark:border-gray-600 dark:bg-gray-600;
  @apply cursor-not-allowed opacity-50;
}

:deep(.el-button--default) {
  @apply border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300;
  @apply hover:border-gray-400 hover:bg-gray-50 dark:hover:border-gray-500 dark:hover:bg-gray-800;
  @apply transition-all duration-200;
}

.disabled-input {
  @apply opacity-75;
}
</style>
