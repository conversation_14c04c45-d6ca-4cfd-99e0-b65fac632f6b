/**
 * Menu Items Management Composable
 * Handles menu items with infinite scroll and pagination
 */

import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { menusApi } from '@/utils/apis/index.js'
import { resetStateHelper } from '@/utils/helpers/object.helper.js'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'

export function useMenuItems() {
  // State
  const loading = ref(false)
  const loadingMore = ref(false)
  const menuItems = ref([])
  const allItems = ref([]) // Store all loaded items
  const currentMenuItem = ref(null)
  const currentMenu = ref(null) // Store menu details
  const hasMorePages = ref(true)
  const currentPage = ref(1)
  const totalItems = ref(0)

  const pagination = reactive({
    currentPage: 1,
    limit: 15,
    total: 0,
    hasMorePages: false,
  })

  // Fetch menu details with all items (from menu items endpoint)
  const fetchMenuWithItems = async (menuId) => {
    try {
      loading.value = true

      // Use the menu items API instead of menu details API
      const response = await menusApi.getMenuItems(menuId)

      if (response.data.success) {
        const responseData = response.data.data
        const items = responseData.data || []
        const paginationData = responseData.pagination || {}

        // Store menu ID for reference
        currentMenu.value = { id: menuId }

        // Sort items by order first, then by created_at
        const sortedItems = sortMenuItems(items)
        menuItems.value = sortedItems

        // Flatten the tree structure to get all items for other operations
        allItems.value = flattenMenuTree(sortedItems)
        totalItems.value = paginationData.total || items.length

        // Update pagination
        pagination.total = paginationData.total || 0
        pagination.currentPage = paginationData.current_page || 1
        pagination.hasMorePages = paginationData.has_more_pages || false
        hasMorePages.value = paginationData.has_more_pages || false

        return { data: items, menu: { id: menuId }, pagination: paginationData }
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải thông tin menu')
      }
    } catch (error) {
      console.error('Error fetching menu with items:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải thông tin menu')
      throw error
    } finally {
      loading.value = false
    }
  }

  // Fetch menu items with pagination (from menu items endpoint)
  const fetchMenuItems = async (menuId, params = {}, append = false) => {
    try {
      if (append) {
        loadingMore.value = true
      } else {
        loading.value = true
        menuItems.value = []
        allItems.value = []
        currentPage.value = 1
      }

      const requestParams = {
        page: append ? currentPage.value + 1 : 1,
        limit: pagination.limit,
        ...params,
      }

      const response = await menusApi.getMenuItems(menuId, requestParams)

      if (response.data.success) {
        const responseData = response.data.data
        const newItems = responseData.data || []
        const paginationData = responseData.pagination || {}

        if (append) {
          // For pagination, we need to merge the tree structures carefully
          // Since API returns tree structure, we need to append new root items
          // and merge any children appropriately
          const sortedNewItems = sortMenuItems(newItems)
          menuItems.value = [...menuItems.value, ...sortedNewItems]
          const newFlatItems = flattenMenuTree(sortedNewItems)
          allItems.value = [...allItems.value, ...newFlatItems]
          currentPage.value = paginationData.current_page || 1
        } else {
          // Replace items for initial load - API already returns tree structure
          const sortedItems = sortMenuItems(newItems)
          menuItems.value = sortedItems
          allItems.value = flattenMenuTree(sortedItems) // Keep flat version for other operations
          currentPage.value = 1
        }

        // Update pagination info
        pagination.currentPage = paginationData.current_page || 1
        pagination.total = paginationData.total || 0
        pagination.hasMorePages = paginationData.has_more_pages || false

        totalItems.value = paginationData.total || 0
        hasMorePages.value = paginationData.has_more_pages || false

        return { data: newItems, pagination: paginationData }
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải danh sách menu items')
      }
    } catch (error) {
      console.error('Error fetching menu items:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải danh sách menu items')
      throw error
    } finally {
      loading.value = false
      loadingMore.value = false
    }
  }

  // Load more items for infinite scroll
  const loadMoreItems = async (menuId) => {
    if (!hasMorePages.value || loadingMore.value) {
      return
    }

    await fetchMenuItems(menuId, {}, true)
  }

  // Create menu item
  const createMenuItem = async (itemData) => {
    try {
      loading.value = true

      const apiData = {
        menu_id: itemData.menu_id, // ID của menu mà item thuộc về
        title: itemData.title,
        slug: itemData.slug || generateSlugWithDash(itemData.title),
        parent_id: itemData.parent_id || null,
        order: itemData.order || 0,
        target: itemData.target || '_self',
        status: Boolean(itemData.status),
        custom_url: itemData.custom_url || '',
        linkable_id: itemData.linkable_id || null,
        linkable_type: itemData.linkable_type || null,
      }

      console.log('apiData gửi lên server', apiData)

      const response = await menusApi.createMenuItem(itemData.menu_id, apiData)

      if (response.data.success) {
        const newItem = response.data.data
        ElMessage.success(response.data.message || 'Thêm menu item thành công')

        // Reload menu data to get fresh tree structure
        if (currentMenu.value?.id) {
          await fetchMenuWithItems(currentMenu.value.id)
        }

        return newItem
      } else {
        // Nếu response không success, throw error để xử lý ở catch block
        const error = new Error(response.data.message || 'Lỗi khi thêm menu item')
        error.response = { data: response.data } // Preserve response data
        throw error
      }
    } catch (error) {
      console.error('Error creating menu item:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)

        throw { validationErrors: errors }
      } else {
        const errorMessage = message || 'Không thể thêm menu item'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  // Update menu item
  const updateMenuItem = async (itemId, itemData) => {
    try {
      loading.value = true


      // Debug: Check if the item exists in our current data
      const flatItems = flattenMenuTree(menuItems.value)
      const existingItem = flatItems.find((item) => item.id == itemId)

      const apiData = {
        menu_id: itemData.menu_id, // ID của menu mà item thuộc về
        title: itemData.title,
        slug: itemData.slug || generateSlugWithDash(itemData.title),
        parent_id: itemData.parent_id || null,
        order: itemData.order || 0,
        target: itemData.target || '_self',
        status: Boolean(itemData.status),
        custom_url: itemData.custom_url || '',
        linkable_id: itemData.linkable_id || null,
        linkable_type: itemData.linkable_type || null,
      }

      try {
        const verifyResponse = await menusApi.getMenuItemById(itemId, itemData.menu_id)
      } catch (verifyError) {
        console.error('Error verifying menu item:', verifyError)
      }


      console.log('apiData gửi lên server', apiData)
      const response = await menusApi.updateMenuItem(itemId, itemData.menu_id, apiData)

      if (response.data.success) {
        const updatedItem = response.data.data
        ElMessage.success(response.data.message || 'Cập nhật menu item thành công')

        // Reload menu data to get fresh tree structure
        if (currentMenu.value?.id) {
          await fetchMenuWithItems(currentMenu.value.id)
        }

        if (currentMenuItem.value && currentMenuItem.value.id === itemId) {
          currentMenuItem.value = updatedItem
        }

        return updatedItem
      } else {
        // Nếu response không success, throw error để xử lý ở catch block
        const error = new Error(response.data.message || 'Lỗi khi cập nhật menu item')
        error.response = { data: response.data } // Preserve response data
        throw error
      }
    } catch (error) {
      console.error('Error updating menu item:', error)
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
      })

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)

        throw { validationErrors: errors }
      } else {
        const errorMessage = message || 'Không thể cập nhật menu item'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  // Delete menu item
  const deleteMenuItem = async (itemId) => {
    try {
      loading.value = true

      // Find the item to get menuId
      const itemToDelete = allItems.value.find((item) => item.id === itemId)
      if (!itemToDelete) {
        throw new Error('Menu item không tồn tại')
      }

      const response = await menusApi.deleteMenuItem(itemId, itemToDelete.menu_id)

      // Check for 204 No Content or success response
      if (response.status === 204 || (response.data && response.data.success)) {
        ElMessage.success('Xóa menu item thành công')

        // Reload menu data to get fresh tree structure
        if (currentMenu.value?.id) {
          await fetchMenuWithItems(currentMenu.value.id)
        }

        if (currentMenuItem.value && currentMenuItem.value.id === itemId) {
          currentMenuItem.value = null
        }

        return true
      } else {
        throw new Error('Không thể xóa menu item')
      }
    } catch (error) {
      if (error === 'cancel') {
        return
      }

      // Handle 204 success in catch block
      if (error.response?.status === 204) {
        ElMessage.success('Xóa menu item thành công')

        // Reload menu data to get fresh tree structure
        if (currentMenu.value?.id) {
          await fetchMenuWithItems(currentMenu.value.id)
        }

        if (currentMenuItem.value && currentMenuItem.value.id === itemId) {
          currentMenuItem.value = null
        }

        return true
      }

      console.error('Error deleting menu item:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể xóa menu item')
    } finally {
      loading.value = false
    }
  }

  // Get menu item by ID
  const getMenuItemById = async (itemId) => {
    try {
      loading.value = true

      // Find the item to get menuId
      const existingItem = allItems.value.find((item) => item.id === itemId)
      if (!existingItem) {
        throw new Error('Menu item không tồn tại')
      }

      const response = await menusApi.getMenuItemById(itemId, existingItem.menu_id)

      if (response.data.success) {
        currentMenuItem.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải menu item')
      }
    } catch (error) {
      console.error('Error fetching menu item:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải menu item')
      throw error
    } finally {
      loading.value = false
    }
  }

  const flattenMenuTree = (tree) => {
    if (!tree || !Array.isArray(tree)) return []

    const result = []

    const flatten = (items) => {
      items.forEach((item) => {
        // Add current item (without children to avoid circular reference)
        const { children, ...itemWithoutChildren } = item
        result.push(itemWithoutChildren)

        // Recursively flatten children
        if (children && Array.isArray(children) && children.length > 0) {
          flatten(children)
        }
      })
    }

    flatten(tree)
    return result
  }

  // Build tree structure from flat array (kept for legacy/utility purposes)
  // Note: The API now returns tree structure directly, so this is mainly for other operations
  const buildMenuTree = (items) => {
    if (!items || !Array.isArray(items)) return []

    const tree = []
    const itemMap = {}

    // Create a map of all items
    items.forEach((item) => {
      itemMap[item.id] = { ...item, children: [] }
    })

    // Build the tree
    items.forEach((item) => {
      if (item.parent_id && itemMap[item.parent_id]) {
        itemMap[item.parent_id].children.push(itemMap[item.id])
      } else {
        tree.push(itemMap[item.id])
      }
    })

    return tree
  }

  const findItemInTree = (items, itemId) => {
    if (!items) return null

    for (const item of items) {
      if (item.id === itemId) {
        return item
      }
      if (item.children && item.children.length > 0) {
        const found = findItemInTree(item.children, itemId)
        if (found) return found
      }
    }
    return null
  }

  // Sort menu items by order first, then by created_at
  const sortMenuItems = (items) => {
    if (!items || !Array.isArray(items)) return []

    const sortItems = (itemList) => {
      return itemList.sort((a, b) => {
        // First sort by order (ascending)
        const orderA = a.order || 0
        const orderB = b.order || 0
        
        if (orderA !== orderB) {
          return orderA - orderB
        }
        
        // If order is the same, sort by created_at (newest first)
        const dateA = new Date(a.created_at || 0)
        const dateB = new Date(b.created_at || 0)
        return dateB - dateA
      }).map(item => {
        // Recursively sort children
        if (item.children && item.children.length > 0) {
          item.children = sortItems(item.children)
        }
        return item
      })
    }

    return sortItems(items)
  }

  // Reset state
  const defaultState = {
    menuItems: [],
    allItems: [],
    currentMenuItem: null,
    currentMenu: null,
    currentPage: 1,
    hasMorePages: true,
    totalItems: 0,
    pagination: {
      currentPage: 1,
      limit: 15,
      total: 0,
      hasMorePages: false,
    },
  }

  const resetState = () => {
    menuItems.value = defaultState.menuItems
    allItems.value = defaultState.allItems
    currentMenuItem.value = defaultState.currentMenuItem
    currentMenu.value = defaultState.currentMenu
    currentPage.value = defaultState.currentPage
    hasMorePages.value = defaultState.hasMorePages
    totalItems.value = defaultState.totalItems
    resetStateHelper(pagination, defaultState.pagination)
  }

  return {
    // State
    loading,
    loadingMore,
    menuItems,
    allItems,
    currentMenuItem,
    currentMenu,
    hasMorePages,
    currentPage,
    totalItems,
    pagination,

    // Methods
    fetchMenuWithItems, // New method
    fetchMenuItems,
    loadMoreItems,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    getMenuItemById,

    // Utilities
    flattenMenuTree,
    buildMenuTree,
    findItemInTree,
    sortMenuItems,
    resetState,
  }
}
