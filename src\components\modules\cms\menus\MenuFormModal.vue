<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa Menu' : 'Tạo <PERSON>'"
    width="500px"
    :before-close="handleClose"
    :z-index="100000"
    append-to-body
    class="menu-form-modal"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
      hide-required-asterisk
    >
      <el-form-item prop="name">
        <template #label>
          <span>Tên <PERSON> <span class="required-asterisk">*</span></span>
        </template>
        <el-input
          v-model="formData.name"
          placeholder="Nhập tên menu (VD: <PERSON>u đầu trang)"
          maxlength="100"
          show-word-limit
          @input="handleNameInput"
          @blur="handleFieldBlur('name')"
        />
        <div class="form-help">
          Tên hiển thị cho admin, dùng để phân biệt các menu
        </div>
      </el-form-item>

      <el-form-item prop="location_key">
        <template #label>
          <span>Location Key <span class="required-asterisk">*</span></span>
        </template>
        <el-input
          v-model="formData.location_key"
          placeholder="Nhập location key (VD: header, footer)"
          maxlength="50"
          show-word-limit
          :disabled="isEdit"
          @input="validateLocationKey"
          @blur="handleFieldBlur('location_key')"
        />

        <div class="form-help">
          Key để frontend gọi API. Chỉ chứa chữ thường, số và dấu gạch ngang.
        </div>
      </el-form-item>
    </el-form>

    <ButtonModalCommon 
        :loading="loading"
        :can-submit="true"
        cancel-text="Hủy"
        :submit-text="isEdit ? 'Cập nhật' : 'Thêm mới'"
        @cancel="handleCancel"
        @submit="handleSubmit"
      />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useMenus } from '@/composables/modules/cms/useMenus.js'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'
import { useFormValidation, validationPresets } from '@/composables/useFormValidation.js'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'
import '@/assets/styles/modules/cms/_menu-form-modal.scss'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  menu: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Composables
const { createMenu, updateMenu, loading } = useMenus()
const { 
  formRef, 
  clearFieldValidation, 
  clearAllValidation, 
  validateForm,
  handleInputChange,
  handleFieldBlur,
  autoGenerateField
} = useFormValidation()

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isEdit = computed(() => !!props.menu)

// Form data
const formData = reactive({
  name: '',
  location_key: '',
})

// Form rules
const formRules = {
  name: [
    { required: true, message: 'Vui lòng nhập tên menu', trigger: 'blur' },
    { min: 2, max: 100, message: 'Tên menu phải từ 2-100 ký tự', trigger: 'blur' },
  ],
  location_key: [
    { required: true, message: 'Vui lòng nhập location key', trigger: 'blur' },
    {
      pattern: /^[a-z0-9-]+$/,
      message: 'Location key chỉ chứa chữ thường, số và dấu gạch ngang',
      trigger: 'blur',
    },
    { min: 2, max: 50, message: 'Location key phải từ 2-50 ký tự', trigger: 'blur' },
  ],
}

// Methods
const resetForm = () => {
  formData.name = ''
  formData.location_key = ''
  clearAllValidation()
}

const populateForm = () => {
  if (props.menu) {
    formData.name = props.menu.name
    formData.location_key = props.menu.location_key
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const handleSubmit = async () => {
  try {
    const valid = await validateForm()
    if (!valid) return

    if (isEdit.value) {
      await updateMenu(props.menu.id, formData)
    } else {
      await createMenu(formData)
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('Error submitting form:', error)
  }
}

// Handle name input
const handleNameInput = () => {
  // Clear validation for name field
  handleInputChange('name', formData.name, validationPresets.name)
  
  // Auto-generate location_key and clear its validation
  if (!isEdit.value) {
    formData.location_key = autoGenerateField(
      'name', 
      'location_key', 
      formData.name, 
      generateSlugWithDash,
      { clearTargetValidation: true }
    )
  }
}

// Validate location key field
const validateLocationKey = (value) => {
  handleInputChange('location_key', value, validationPresets.slug)
}

// Watch for changes
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        populateForm()
      })
    }
  },
)
</script>
