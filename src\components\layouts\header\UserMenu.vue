<template>
  <div class="relative" ref="dropdownRef">
    <button class="flex items-center text-gray-700 dark:text-gray-400" @click.prevent="toggleDropdown">
      <span class="mr-3 h-11 w-11 overflow-hidden rounded-full">
        <img :src="avatarSrc" alt="User" />
      </span>

      <span class="text-theme-sm mr-1 block font-medium">{{ userDisplayName }}</span>

      <ChevronDownIcon :class="{ 'rotate-180': dropdownOpen }" />
    </button>

    <!-- Dropdown Start -->
    <div
      v-if="dropdownOpen"
      class="absolute right-0 mt-[17px] flex w-[260px] flex-col rounded-2xl border border-gray-200 bg-white p-3 shadow-2xl dark:border-gray-700 dark:bg-gray-900"
    >
      <div>
        <span class="text-theme-sm block font-medium text-gray-700 dark:text-gray-400">{{ userDisplayName }}</span>
        <span class="text-theme-xs mt-0.5 block text-gray-500 dark:text-gray-400">{{ userEmail }}</span>
      </div>

      <ul class="flex flex-col gap-1 border-b border-gray-200 pt-4 pb-3 dark:border-gray-700">
        <li v-for="item in menuItems" :key="item.href">
          <router-link
            :to="item.href"
            class="group text-theme-sm flex items-center gap-3 rounded-lg px-3 py-2 font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
          >
            <!-- SVG icon would go here -->
            <component :is="item.icon" class="text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300" />
            {{ item.text }}
          </router-link>
        </li>
      </ul>
      <button
        @click="logout"
        :disabled="loading"
        class="group text-theme-sm mt-3 flex w-full items-center gap-3 rounded-lg px-3 py-2 font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-700 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
      >
        <LogoutIcon class="text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300" />
        {{ loading ? 'Đang đăng xuất...' : 'Đăng xuất' }}
      </button>
    </div>
    <!-- Dropdown End -->
  </div>
</template>

<script setup>
import avatarUser from '@/assets/images/samples/user/owner.jpg'
import { ChevronDownIcon, LogoutIcon, UserCircleIcon } from '@/components/icons/index.js'
import { RouterLink, useRouter } from 'vue-router'
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useAuthStore } from '@/state/index.js'
import { useAuthUser } from '@/composables/useAuthUser.js'
import { authApi } from '@/utils/apis'
import { storeToRefs } from 'pinia'

const router = useRouter()
const authStore = useAuthStore()
const { authUser, userDisplayName } = storeToRefs(authStore)
const { loading } = useAuthUser()

const dropdownOpen = ref(false)
const dropdownRef = ref(null)

const avatarSrc = computed(() => {
  if (authUser.value && authUser.value.avatar) {
    return authUser.value.avatar
  }
  return avatarUser
})

const userEmail = computed(() => {
  return authUser.value?.email || ''
})

const menuItems = [{ href: '/profile', icon: UserCircleIcon, text: 'Thông tin cá nhân' }]

const toggleDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value
}

const closeDropdown = () => {
  dropdownOpen.value = false
}

const logout = async () => {
  try {
    // Đóng dropdown trước khi logout
    closeDropdown()
    // Thực hiện logout
    await authApi.logoutUser()
    authStore.logout()
    router.push({ name: 'login' })
  } catch (error) {
    console.error('Logout error:', error)
    // Vẫn redirect về login ngay cả khi logout API fail
    router.push({ name: 'login' })
  }
}

const handleClickOutside = (event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
