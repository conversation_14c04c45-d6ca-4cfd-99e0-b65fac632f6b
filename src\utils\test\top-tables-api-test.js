/**
 * Test file để kiểm tra các API cho TopTablesSection
 * Chạy file này để test các API endpoints
 */

import overviewApi from '@/utils/apis/overview.api.js'
import { 
  extractTopRechargeResponse, 
  extractTopPackagesResponse, 
  extractRecentHistoryResponse,
  extractErrorResponse 
} from '@/utils/helpers/response.helper.js'

// Test Top Recharge API
export const testTopRechargeAPI = async () => {
  console.log('🧪 Testing Top Recharge API...')
  try {
    const response = await overviewApi.getTopRecharge()
    const result = extractTopRechargeResponse(response)
    
    console.log('✅ Top Recharge API Response:', {
      success: result.success,
      message: result.message,
      dataCount: result.data?.length || 0,
      sampleData: result.data?.[0] || null,
      pagination: result.pagination
    })
    
    return result
  } catch (error) {
    console.error('❌ Top Recharge API Error:', error)
    const errorResult = extractErrorResponse(error)
    console.error('Error details:', errorResult)
    return null
  }
}

// Test Top Packages API
export const testTopPackagesAPI = async () => {
  console.log('🧪 Testing Top Packages API...')
  try {
    const response = await overviewApi.getTopPackages()
    const result = extractTopPackagesResponse(response)
    
    console.log('✅ Top Packages API Response:', {
      success: result.success,
      message: result.message,
      dataCount: result.data?.length || 0,
      sampleData: result.data?.[0] || null,
      summary: result.summary
    })
    
    return result
  } catch (error) {
    console.error('❌ Top Packages API Error:', error)
    const errorResult = extractErrorResponse(error)
    console.error('Error details:', errorResult)
    return null
  }
}

// Test Recent History API
export const testRecentHistoryAPI = async () => {
  console.log('🧪 Testing Recent History API...')
  try {
    const response = await overviewApi.getRecentHistory()
    const result = extractRecentHistoryResponse(response)
    
    console.log('✅ Recent History API Response:', {
      success: result.success,
      message: result.message,
      dataCount: result.data?.length || 0,
      sampleData: result.data?.[0] || null,
      pagination: result.pagination
    })
    
    return result
  } catch (error) {
    console.error('❌ Recent History API Error:', error)
    const errorResult = extractErrorResponse(error)
    console.error('Error details:', errorResult)
    return null
  }
}

// Test tất cả APIs
export const testAllTopTablesAPIs = async () => {
  console.log('🚀 Starting Top Tables APIs Test Suite...')
  
  const results = {
    topRecharge: await testTopRechargeAPI(),
    topPackages: await testTopPackagesAPI(),
    recentHistory: await testRecentHistoryAPI()
  }
  
  console.log('📊 Test Results Summary:', {
    topRecharge: results.topRecharge?.success ? '✅ Success' : '❌ Failed',
    topPackages: results.topPackages?.success ? '✅ Success' : '❌ Failed',
    recentHistory: results.recentHistory?.success ? '✅ Success' : '❌ Failed'
  })
  
  return results
}

// Để test trong browser console:
// import { testAllTopTablesAPIs } from '@/utils/test/top-tables-api-test.js'
// testAllTopTablesAPIs()
