/* Menu Items Panel */
.menu-items-panel {
  display: flex;
  flex-direction: column;
  height: 650px; /* Match total MenuSidebar height (search + menu list) */
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #ffffff;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  min-height: 200px;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.items-count {
  padding: 0.5rem 1rem;
  text-align: center;
  font-size: 0.75rem;
  color: #9ca3af;
  border-top: 1px solid #f3f4f6;
  margin-top: 1rem;
  background-color: #f9fafb;
}

/* Custom scrollbar */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode support */
.dark .menu-items-panel {
  border-color: #374151;
  background-color: rgba(255, 255, 255, 0.03);
}

.dark .panel-header {
  border-bottom-color: #374151;
  background-color: rgba(17, 24, 39, 0.5);
}

.dark .panel-title {
  color: #ffffff;
}

.dark .empty-text {
  color: #9ca3af;
}

.dark .items-count {
  color: #6b7280;
  border-top-color: #374151;
}

/* Dark mode scrollbar */
.dark .panel-content::-webkit-scrollbar-track {
  background: #374151;
}

.dark .panel-content::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .panel-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .menu-list-wrapper .grid {
    height: auto !important;
  }

  .menu-items-panel {
    height: auto !important;
    max-height: 500px;
  }
}