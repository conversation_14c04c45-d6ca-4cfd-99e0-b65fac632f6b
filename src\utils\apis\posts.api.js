import apiAxios from '@/utils/configs/axios.config.js'

const postsApi = {
  getPosts(params = {}) {
    // Clean empty params
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== '' && value !== null && value !== undefined),
    )

    return apiAxios({
      method: 'get',
      url: 'cms/posts',
      params: cleanParams,
    })
  },

  createPost(postData) {
    // Nếu cover_image là File object thì gửi form-data
    if (postData.cover_image instanceof File) {
      const formData = new FormData()

      // Append tất cả các field, loại bỏ cover_image_preview
      Object.entries(postData).forEach(([key, value]) => {
        if (key !== 'cover_image_preview' && value !== null && value !== undefined) {
          formData.append(key, value)
        }
      })

      return apiAxios({
        method: 'post',
        url: 'cms/posts',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // Loại bỏ cover_image_preview khỏi JSON data
      const { cover_image_preview, ...cleanData } = postData

      return apiAxios({
        method: 'post',
        url: 'cms/posts',
        data: cleanData,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }
  },

  getPostById(id) {
    return apiAxios({
      method: 'get',
      url: `cms/posts/${id}`,
    })
  },

  updatePost(id, postData) {
    // Nếu cover_image là File object thì sử dụng Method Spoofing
    if (postData.cover_image instanceof File) {
      const formData = new FormData()

      // Thêm _method="PUT" cho Method Spoofing
      formData.append('_method', 'PUT')

      // Append tất cả các field, loại bỏ cover_image_preview
      Object.entries(postData).forEach(([key, value]) => {
        if (key !== 'cover_image_preview' && value !== null && value !== undefined) {
          formData.append(key, value)
        }
      })

      // Gửi POST request với _method=PUT
      return apiAxios({
        method: 'post',
        url: `cms/posts/${id}`,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // Loại bỏ cover_image_preview khỏi JSON data
      const { cover_image_preview, ...cleanData } = postData

      // Gửi PUT request bình thường
      return apiAxios({
        method: 'put',
        url: `cms/posts/${id}`,
        data: cleanData,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }
  },

  deletePost(id) {
    return apiAxios({
      method: 'delete',
      url: `cms/posts/${id}`,
    })
  },

  updatePostStatus(id, postData) {
    return apiAxios({
      method: 'put',
      url: `cms/posts/${id}`,
      data: postData,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },

  updatePostAttributes(id, attributes) {
    return apiAxios({
      method: 'patch',
      url: `cms/posts/${id}/attributes`,
      data: attributes,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },
}

export default postsApi
