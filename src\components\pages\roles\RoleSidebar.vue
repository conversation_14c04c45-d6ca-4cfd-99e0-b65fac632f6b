<template>
  <div class="role-sidebar">
    <!-- Search and Create Button -->
    <div class="flex items-center gap-3">
      <el-input
        v-model="searchQuery"
        placeholder="Tìm kiếm vai trò..."
        clearable
        class="flex-1"
        @keyup="handleKeyup"
        @input="handleInput"
        @clear="handleResetFilters"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #suffix>
          <el-button v-if="searchQuery" type="text" size="small" @click="handleSearch" class="mr-1">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
      <el-button type="primary" size="default" @click="handleCreateRole" v-if="props.canCreate">
        <PlusIcon class="mr-1 h-4 w-4" />
        Tạo vai trò
      </el-button>
    </div>

    <!-- Search Results Info -->
    <div v-if="searchQuery && !loading" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Tìm thấy {{ roles.length }} vai trò{{ roles.length !== 1 ? '' : '' }}
    </div>

    <!-- Roles List with fixed height -->
    <div
      class="role-list-scroll mt-5 h-[550px] rounded-lg border border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900/50"
    >
      <div v-loading="shouldShowLoading" class="p-2">
        <div
          v-for="role in roles"
          :key="role.id"
          class="mb-2 flex cursor-pointer items-center justify-between rounded-lg border p-3 transition-all duration-200 hover:shadow-md dark:hover:shadow-lg"
          :class="{
            'border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg dark:border-blue-500 dark:from-blue-900/30 dark:to-indigo-900/30 dark:shadow-blue-500/20':
              selectedRole?.id === role.id,
            'border-gray-200 bg-white hover:border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500':
              selectedRole?.id !== role.id,
          }"
          @click="handleSelectRole(role)"
        >
          <!-- Role Info -->
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ role.name || 'Không xác định' }}
              </div>
              <!-- Selected indicator -->
              <div v-if="selectedRole?.id === role.id" class="flex items-center gap-1">
                <div
                  v-if="loadingPermissions"
                  class="h-3 w-3 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
                ></div>
                <div v-else class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
                <span class="text-xs font-medium text-blue-600 dark:text-blue-400">
                  {{ loadingPermissions ? 'Đang tải...' : 'Đang chọn' }}
                </span>
              </div>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ role.description || 'Đang cập nhật...' }}
            </div>
          </div>

          <!-- Action Buttons - Only show for non-protected roles -->
          <div class="flex items-center gap-2" v-if="!role.is_protected">
            <el-button type="primary" size="small" @click.stop="handleEditRole(role)" circle v-if="props.canEdit">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button type="danger" size="small" @click.stop="handleDeleteRole(role)" circle v-if="props.canDelete">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>

          <!-- Protected Role Indicator -->
          <div v-if="role.is_protected" class="flex items-center gap-1">
            <span class="text-xs font-medium text-amber-600 dark:text-amber-400">🔒 Được bảo vệ</span>
          </div>
        </div>

        <!-- Empty state -->
        <div v-if="!loading && roles.length === 0" class="py-8 text-center text-gray-500 dark:text-gray-400">
          <div class="flex flex-col items-center gap-2">
            <div class="text-lg">🔍</div>
            <div class="font-medium">
              {{ searchQuery ? 'Không tìm thấy vai trò nào' : 'Không có vai trò nào' }}
            </div>
            <div class="text-sm">
              {{ searchQuery ? `Không có vai trò nào phù hợp với "${searchQuery}"` : 'Hãy tạo vai trò đầu tiên' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onUnmounted } from 'vue'
import { Search, Edit, Delete } from '@element-plus/icons-vue'
import { PlusIcon } from '@/components/icons/index.js'

// Props
const props = defineProps({
  roles: {
    type: Array,
    default: () => [],
  },
  selectedRole: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  realTimePermissionCounts: {
    type: Object,
    default: () => ({}),
  },
  loadingPermissions: {
    type: Boolean,
    default: false,
  },
  canCreate: {
    type: Boolean,
    default: true,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
  canDelete: {
    type: Boolean,
    default: true,
  },
})

// Emits
const emit = defineEmits(['create-role', 'edit-role', 'delete-role', 'select-role', 'search', 'reset-filters'])

// Local state
const searchQuery = ref('')
const searchTimeout = ref(null)

// Computed property to show loading only when necessary
const shouldShowLoading = computed(() => {
  // Only show loading when roles list is being fetched (search, reset, etc.)
  // Not when just selecting a role
  return props.loading && props.roles.length === 0
})

// Watch for filters changes
watch(
  () => props.filters.search,
  (newValue) => {
    searchQuery.value = newValue || ''
  },
)

// Methods
const handleCreateRole = () => {
  emit('create-role')
}

const handleEditRole = (role) => {
  emit('edit-role', role)
}

const handleDeleteRole = (role) => {
  emit('delete-role', role)
}

const handleSelectRole = (role) => {
  emit('select-role', role)
}

const handleKeyup = (event) => {
  if (event.key === 'Enter') {
    emit('search', searchQuery.value)
  }
}

const handleSearch = () => {
  emit('search', searchQuery.value)
}

const handleInput = () => {
  // Debounced search on input change
  clearTimeout(searchTimeout.value)
  searchTimeout.value = setTimeout(() => {
    emit('search', searchQuery.value)
  }, 300)
}

const getRolePermissionCount = (role) => {
  // Use real-time count if available
  if (props.realTimePermissionCounts && props.realTimePermissionCounts[role.id] !== undefined) {
    return props.realTimePermissionCounts[role.id]
  }

  // Fallback to API count
  return role.permissions_count || 0
}

const handleResetFilters = () => {
  searchQuery.value = ''
  emit('reset-filters')
}

// Cleanup timeout on unmount
onUnmounted(() => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
})
</script>

<style lang="scss" scoped>
.role-list-scroll {
  overflow-y: hidden;
  transition: box-shadow 0.2s;
}
.role-list-scroll:hover {
  overflow-y: auto;
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.08);
}
.role-list-scroll::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}
.role-list-scroll:hover::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
}
.dark .role-list-scroll:hover::-webkit-scrollbar-thumb {
  background: #334155;
}
</style>
