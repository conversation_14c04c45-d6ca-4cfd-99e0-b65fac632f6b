# <PERSON><PERSON><PERSON> h<PERSON>nh Docker Compose cho môi trường Staging
services:
  # Service cho ứng dụng Admin UI (VueJS)
  admin-ui:
    build:
      context: .
      dockerfile: Dockerfile

    image: jx1-admin-ui-staging
    container_name: jx1-admin-ui-staging

    restart: unless-stopped

    env_file:
      - .env.staging

    ports:
      - '8081:80'

    networks:
      - jx1_admin-ui-staging

networks:
  jx1_admin-ui-staging:
    driver: bridge
