/*
  Users Composable
  Quản lý logic business cho module users
*/

import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as yup from 'yup'
import { usersApi } from '@/utils/apis'
import { USER_STATUS, USER_STATUS_OPTIONS, PAGINATION, VALIDATION } from '@/utils/configs/constant.config.js'

// Validation schemas
const createUserSchema = yup.object({
  name: yup
    .string()
    .required('Tên người dùng là bắt buộc')
    .min(VALIDATION.NAME_MIN_LENGTH, 'Tên phải có ít nhất 2 ký tự'),
  email: yup.string().email('Email không hợp lệ').required('Email là bắt buộc'),
  password: yup
    .string()
    .required('Mật khẩu là bắt buộc')
    .min(VALIDATION.PASSWORD_MIN_LENGTH, '<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự'),
  password_confirmation: yup
    .string()
    .required('<PERSON><PERSON><PERSON> nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], 'Mật khẩu xác nhận không khớp'),
  phone: yup.string().matches(/^[0-9+\-\s()]*$/, 'Số điện thoại không hợp lệ'),
  role_ids: yup.array().of(yup.number()).min(1, 'Phải chọn ít nhất một vai trò'),
  status: yup
    .number()
    .oneOf([USER_STATUS.ACTIVE, USER_STATUS.INACTIVE, USER_STATUS.SUSPENDED], 'Trạng thái không hợp lệ'),
})

const updateUserSchema = yup.object({
  name: yup
    .string()
    .required('Tên người dùng là bắt buộc')
    .min(VALIDATION.NAME_MIN_LENGTH, 'Tên phải có ít nhất 2 ký tự'),
  phone: yup.string().matches(/^[0-9+\-\s()]*$/, 'Số điện thoại không hợp lệ'),
  role_ids: yup.array().of(yup.number()).min(1, 'Phải chọn ít nhất một vai trò'),
  status: yup
    .number()
    .oneOf([USER_STATUS.ACTIVE, USER_STATUS.INACTIVE, USER_STATUS.SUSPENDED], 'Trạng thái không hợp lệ'),
})

const passwordSchema = yup.object({
  password: yup
    .string()
    .required('Mật khẩu là bắt buộc')
    .min(VALIDATION.PASSWORD_MIN_LENGTH, 'Mật khẩu phải có ít nhất 6 ký tự'),
  password_confirmation: yup
    .string()
    .required('Xác nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], 'Mật khẩu xác nhận không khớp'),
})

export function useUsers() {
  // State
  const users = ref([])
  const user = ref(null)
  const loading = ref(false)
  const saving = ref(false)
  const deleting = ref(false)
  const filters = ref({
    search: '',
    status: null,
    role_id: null,
    page: PAGINATION.DEFAULT_PAGE,
    per_page: PAGINATION.DEFAULT_PER_PAGE,
  })
  const pagination = ref({
    current_page: PAGINATION.DEFAULT_PAGE,
    last_page: 1,
    per_page: PAGINATION.DEFAULT_PER_PAGE,
    total: 0,
    from: 0,
    to: 0,
  })
  const errors = ref({})

  // State cho roles
  const allRoles = ref([])
  const loadingAllRoles = ref(false)

  // Computed
  const hasUsers = computed(() => users.value.length > 0)
  const isFiltered = computed(() => {
    return filters.value.search || filters.value.status || filters.value.role_id
  })

  // Helper methods
  const handleValidationError = (error) => {
    if (error.name === 'ValidationError') {
      errors.value = error.errors.reduce((acc, err) => {
        acc[err.path] = err.message
        return acc
      }, {})
    } else {
      errors.value = error.response?.data?.errors || { general: error.message || 'Có lỗi xảy ra' }
    }
  }

  const cleanFilters = (apiFilters) => {
    const cleaned = { ...apiFilters }

    // Convert status filter to number if it's not empty
    if (cleaned.status !== '' && cleaned.status !== null && cleaned.status !== undefined) {
      cleaned.status = parseInt(cleaned.status)
    } else {
      delete cleaned.status
    }

    // Handle role_id filter
    if (cleaned.role_id !== '' && cleaned.role_id !== null && cleaned.role_id !== undefined) {
      cleaned.role_id = parseInt(cleaned.role_id)
    } else {
      delete cleaned.role_id
    }

    return cleaned
  }

  // Methods
  const fetchUsers = async () => {
    try {
      loading.value = true
      errors.value = {}

      const apiFilters = cleanFilters(filters.value)
      const response = await usersApi.getUsers(apiFilters)

      users.value = response.data.data.users.data
      pagination.value = {
        current_page: response.data.data.pagination.current_page,
        last_page: response.data.data.pagination.last_page,
        per_page: response.data.data.pagination.per_page,
        total: response.data.data.pagination.total,
        from: response.data.data.pagination.from,
        to: response.data.data.pagination.to,
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      handleValidationError(error)
      ElMessage.error('Không thể tải danh sách người dùng')
    } finally {
      loading.value = false
    }
  }

  const createUser = async (userData) => {
    try {
      saving.value = true
      errors.value = {}

      await createUserSchema.validate(userData)
      const response = await usersApi.createUser(userData)

      ElMessage.success('Tạo người dùng thành công')
      return response.data.data.user
    } catch (error) {
      console.error('Error creating user:', error)
      handleValidationError(error)
      ElMessage.error('Không thể tạo người dùng')
      throw error
    } finally {
      saving.value = false
    }
  }

  const updateUser = async (id, userData) => {
    try {
      saving.value = true
      errors.value = {}

      await updateUserSchema.validate(userData)
      const response = await usersApi.updateUser(id, userData)

      ElMessage.success('Cập nhật người dùng thành công')
      return response.data.data.user
    } catch (error) {
      console.error('Error updating user:', error)
      handleValidationError(error)
      ElMessage.error('Không thể cập nhật người dùng')
      throw error
    } finally {
      saving.value = false
    }
  }

  const deleteUser = async (id) => {
    try {
      await ElMessageBox.confirm('Bạn có chắc chắn muốn xóa người dùng này?', 'Xác nhận xóa', {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      })

      deleting.value = true
      await usersApi.deleteUser(id)

      ElMessage.success('Xóa người dùng thành công')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Error deleting user:', error)
        ElMessage.error('Không thể xóa người dùng')
      }
      throw error
    } finally {
      deleting.value = false
    }
  }

  const changeUserPassword = async (id, passwordData) => {
    try {
      saving.value = true
      errors.value = {}

      await passwordSchema.validate(passwordData)
      await usersApi.changeUserPassword(id, passwordData)

      ElMessage.success('Thay đổi mật khẩu người dùng thành công')
    } catch (error) {
      console.error('Error changing user password:', error)
      handleValidationError(error)
      ElMessage.error('Không thể thay đổi mật khẩu người dùng')
      throw error
    } finally {
      saving.value = false
    }
  }

  const resetFilters = () => {
    filters.value = {
      search: '',
      status: null,
      role_id: null,
      page: PAGINATION.DEFAULT_PAGE,
      per_page: PAGINATION.DEFAULT_PER_PAGE,
    }
    clearErrors()
  }

  const clearErrors = () => {
    errors.value = {}
  }

  // Lấy tất cả roles
  const fetchAllRoles = async () => {
    loadingAllRoles.value = true
    try {
      const response = await usersApi.getAllRoles()
      allRoles.value = response.data.data.roles.data || []
      return allRoles.value
    } catch (error) {
      console.error('Error fetching all roles:', error)
      ElMessage.error('Không thể tải danh sách vai trò')
      allRoles.value = []
      throw error
    } finally {
      loadingAllRoles.value = false
    }
  }

  return {
    // State
    users,
    user,
    loading,
    saving,
    deleting,
    filters,
    pagination,
    errors,
    allRoles,
    loadingAllRoles,

    // Computed
    hasUsers,
    isFiltered,

    // Constants
    USER_STATUS,
    USER_STATUS_OPTIONS,

    // Methods
    fetchUsers,
    createUser,
    updateUser,
    changeUserPassword,
    deleteUser,
    resetFilters,
    clearErrors,
    fetchAllRoles,
  }
}
