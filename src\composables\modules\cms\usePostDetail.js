/**
 * Post Detail Composable
 * Handles post detail view logic
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { usePosts } from './usePosts.js'

export function usePostDetail() {
  // State
  const loading = ref(false)
  const currentPost = ref(null)

  // Composables
  const { getPostById, deletePost, formatDate, getStatusLabel, getStatusType } = usePosts()

  // Computed
  const hasSeoInfo = computed(() => {
    return (
      currentPost.value &&
      (currentPost.value.meta_title || currentPost.value.meta_description || currentPost.value.meta_keywords)
    )
  })

  // Methods
  const loadPost = async (id) => {
    try {
      loading.value = true
      const post = await getPostById(id)
      currentPost.value = post
      return post
    } catch (error) {
      console.error('Error loading post:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const handleDelete = async () => {
    try {
      // Call deletePost from usePosts and directly return its result
      const result = await deletePost(currentPost.value.id)
      return result // This will be true, null, or false based on usePosts.js
    } catch (error) {
      // This catch block should ideally only be hit if deletePost (from usePosts)
      // throws an unexpected error that it doesn't handle itself.
      console.error('Error in usePostDetail handleDelete:', error)
      return false
    }
  }

  const getImageUrl = (imagePath) => {
    if (!imagePath) return ''
    if (imagePath.startsWith('http')) return imagePath
    return `${import.meta.env.VITE_API_BASE_URL}${imagePath}`
  }

  const handleImageError = () => {
    ElMessage.error('Không thể tải ảnh')
  }

  return {
    // State
    loading,
    currentPost,

    // Computed
    hasSeoInfo,

    // Methods
    loadPost,
    handleDelete,
    getImageUrl,
    handleImageError,
    formatDate,
    getStatusLabel,
    getStatusType,
  }
}
