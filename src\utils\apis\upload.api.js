import apiAxios from '@/utils/configs/axios.config.js'

const uploadApi = {
  uploadFile(file, folder = 'general') {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('folder', folder)

    return apiAxios({
      method: 'post',
      url: '/upload/file',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  uploadImage(file, folder = 'images') {
    const formData = new FormData()
    formData.append('image', file)
    formData.append('folder', folder)

    return apiAxios({
      method: 'post',
      url: '/upload/image',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  uploadMultiple(files, folder = 'general') {
    const formData = new FormData()
    files.forEach((file) => {
      formData.append('files[]', file)
    })
    formData.append('folder', folder)

    return apiAxios({
      method: 'post',
      url: '/upload/multiple',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  deleteFile(fileName) {
    return apiAxios({
      method: 'delete',
      url: '/upload/file',
      data: { fileName },
    })
  },

  getUploadedFiles(folder = null) {
    return apiAxios({
      method: 'get',
      url: '/upload/files',
      params: folder ? { folder } : {},
    })
  },
}

export default uploadApi
