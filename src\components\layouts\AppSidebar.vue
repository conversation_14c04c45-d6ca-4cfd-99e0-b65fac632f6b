<template>
  <aside
    :class="[
      'fixed top-0 left-0 z-99999 mt-16 flex h-screen flex-col border-r border-gray-200 bg-white px-5 text-gray-900 transition-all duration-300 ease-in-out lg:mt-0 dark:border-gray-800 dark:bg-gray-900',
      {
        'lg:w-[290px]': isExpanded || isMobileOpen || isHovered,
        'lg:w-[90px]': !isExpanded && !isHovered,
        'w-[290px] translate-x-0': isMobileOpen,
        '-translate-x-full': !isMobileOpen,
        'lg:translate-x-0': true,
      },
    ]"
    @mouseenter="!isExpanded && (isHovered = true)"
    @mouseleave="isHovered = false"
  >
    <div :class="['flex py-8', !isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start']">
      <router-link to="/">
        <img
          v-if="isExpanded || isHovered || isMobileOpen"
          class="dark:hidden"
          src="/src/assets/images/logo/logo.svg"
          alt="Logo"
          width="150"
          height="40"
        />
        <img
          v-if="isExpanded || isHovered || isMobileOpen"
          class="hidden dark:block"
          src="/src/assets/images/logo/logo-dark.svg"
          alt="Logo"
          width="150"
          height="40"
        />
        <img v-else src="/src/assets/images/logo/logo-icon.svg" alt="Logo" width="32" height="32" />
      </router-link>
    </div>
    <div class="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
      <nav class="mb-6">
        <div class="flex flex-col gap-4">
          <div>
            <h2
              :class="[
                'mb-4 flex text-xs leading-[20px] text-gray-400 uppercase',
                !isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start',
              ]"
            >
              <template v-if="isExpanded || isHovered || isMobileOpen"> Menu </template>
              <HorizontalDots v-else />
            </h2>
            <ul class="flex flex-col gap-4">
              <li v-for="(item, index) in filteredMenu" :key="item.id">
                <button
                  v-if="item.children && item.children.length > 0"
                  @click="toggleSubmenu(index)"
                  :class="[
                    'menu-item group w-full',
                    {
                      'menu-item-active': isSubmenuOpen(index),
                      'menu-item-inactive': !isSubmenuOpen(index),
                    },
                    !isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start',
                  ]"
                >
                  <span :class="[isSubmenuOpen(index) ? 'menu-item-icon-active' : 'menu-item-icon-inactive']">
                    <component :is="item.icon" />
                  </span>
                  <span v-if="isExpanded || isHovered || isMobileOpen" class="menu-item-text">{{ item.title }}</span>
                  <ChevronDownIcon
                    v-if="isExpanded || isHovered || isMobileOpen"
                    :class="[
                      'ml-auto h-5 w-5 transition-transform duration-200',
                      {
                        'text-brand-500 rotate-180': isSubmenuOpen(index),
                      },
                    ]"
                  />
                </button>
                <router-link
                  v-else
                  :to="item.route"
                  :class="[
                    'menu-item group',
                    {
                      'menu-item-active': isActive(item.route.name),
                      'menu-item-inactive': !isActive(item.route.name),
                    },
                  ]"
                >
                  <span :class="[isActive(item.route.name) ? 'menu-item-icon-active' : 'menu-item-icon-inactive']">
                    <component :is="item.icon" />
                  </span>
                  <span v-if="isExpanded || isHovered || isMobileOpen" class="menu-item-text">{{ item.title }}</span>
                  <span
                    v-if="item.badge && (isExpanded || isHovered || isMobileOpen)"
                    :class="[
                      'ml-auto rounded-full px-2 py-1 text-xs font-medium',
                      {
                        'bg-red-100 text-red-600': item.badge.type === 'danger',
                        'bg-blue-100 text-blue-600': item.badge.type === 'info',
                        'bg-green-100 text-green-600': item.badge.type === 'success',
                        'bg-yellow-100 text-yellow-600': item.badge.type === 'warning',
                      },
                    ]"
                  >
                    {{ item.badge.count }}
                  </span>
                </router-link>
                <transition
                  @enter="startTransition"
                  @after-enter="endTransition"
                  @before-leave="startTransition"
                  @after-leave="endTransition"
                >
                  <div v-show="isSubmenuOpen(index) && (isExpanded || isHovered || isMobileOpen)">
                    <ul class="mt-2 ml-9 space-y-1">
                      <li v-for="subItem in item.children" :key="subItem.id">
                        <router-link
                          :to="subItem.route"
                          :class="[
                            'menu-dropdown-item',
                            {
                              'menu-dropdown-item-active': isActive(subItem.route.name),
                              'menu-dropdown-item-inactive': !isActive(subItem.route.name),
                            },
                          ]"
                        >
                          {{ subItem.title }}
                        </router-link>
                      </li>
                    </ul>
                  </div>
                </transition>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </aside>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import ChevronDownIcon from '@/components/icons/ChevronDownIcon.vue'
import HorizontalDots from '@/components/icons/HorizontalDots.vue'
import { useSidebar } from '@/composables/useSidebar'
import { useAuthStore } from '@/state/index.js'
import { menuConfig } from '@/utils/configs/menu.config.js'
import { filterMenuByPermissions, getUserPermissions } from '@/utils/helpers/permission.helper.js'
import { storeToRefs } from 'pinia'

const route = useRoute()
const { isExpanded, isMobileOpen, isHovered, openSubmenu } = useSidebar()
const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)

// Lọc menu theo quyền user
const filteredMenu = computed(() => {
  const userPermissions = getUserPermissions(authUser.value)
  return filterMenuByPermissions(menuConfig, userPermissions)
})

const isActive = (routeName) => route.name === routeName

const toggleSubmenu = (itemIndex) => {
  const key = `menu-${itemIndex}`
  openSubmenu.value = openSubmenu.value === key ? null : key
}

const isAnySubmenuRouteActive = computed(() => {
  return filteredMenu.value.some(
    (item) => item.children && item.children.some((subItem) => isActive(subItem.route.name)),
  )
})

const isSubmenuOpen = (itemIndex) => {
  const key = `menu-${itemIndex}`
  const item = filteredMenu.value[itemIndex]
  return (
    openSubmenu.value === key ||
    (isAnySubmenuRouteActive.value && item.children?.some((subItem) => isActive(subItem.route.name)))
  )
}

const startTransition = (el) => {
  el.style.height = 'auto'
  const height = el.scrollHeight
  el.style.height = '0px'
  el.offsetHeight
  el.style.height = height + 'px'
}

const endTransition = (el) => {
  el.style.height = ''
}
</script>
