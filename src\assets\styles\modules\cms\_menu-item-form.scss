.menu-item-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #374151;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
  
  :deep(.el-input-number .el-input__inner) {
    text-align: left;
  }
  
  .form-help {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
  }

  .required-asterisk {
    color: #ef4444;
    font-weight: 600;
    margin-left: 4px;
  }

  /* Hide Element Plus default required asterisk */
  :deep(.el-form-item.is-required .el-form-item__label::before) {
    display: none !important;
  }

  :deep(.el-form-item.is-required::before) {
    display: none !important;
  }

  :deep(.el-form-item__label::before) {
    display: none !important;
  }
  
  .submit-button {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
    font-weight: 600 !important;
  }
  
  .submit-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5) !important;
  }
  
  // Dark mode support
  .dark {
    :deep(.el-form-item__label) {
      color: #e0e7ef;
    }
    .form-help {
      color: #a1a1aa;
    }
    .submit-button {
      background: linear-gradient(135deg, #2563eb 0%, #1e293b 100%) !important;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4) !important;
    }
    .submit-button:hover {
      box-shadow: 0 6px 16px rgba(37, 99, 235, 0.5) !important;
    }
  } 