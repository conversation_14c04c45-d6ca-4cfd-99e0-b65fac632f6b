<template>
  <div>
    <div class="rounded-2xl border border-gray-200 p-5 lg:p-6 dark:border-gray-800">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div>
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6 dark:text-white/90">Thông tin cá nhân</h4>

          <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Họ và tên</p>
              <p class="text-sm font-medium text-gray-800 dark:text-white/90">{{ userName }}</p>
            </div>

            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Email</p>
              <p class="text-sm font-medium text-gray-800 dark:text-white/90">{{ userEmail }}</p>
            </div>

            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Vai trò</p>
              <p class="text-sm font-medium text-gray-800 dark:text-white/90">{{ userRole }}</p>
            </div>

            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">Trạng thái</p>
              <span :class="statusClass">{{ userStatus }}</span>
            </div>
          </div>
        </div>

        <div class="flex gap-3">
          <button class="edit-button" @click="showProfileModal = true">
            <svg
              class="fill-current"
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z"
                fill=""
              />
            </svg>
            Cập nhật hồ sơ
          </button>

          <button class="edit-button" @click="$emit('change-password')">
            <svg
              class="fill-current"
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.5 6.75V5.25C13.5 2.85 11.65 1 9.25 1H8.75C6.35 1 4.5 2.85 4.5 5.25V6.75C3.675 6.75 3 7.425 3 8.25V14.25C3 15.075 3.675 15.75 4.5 15.75H13.5C14.325 15.75 15 15.075 15 14.25V8.25C15 7.425 14.325 6.75 13.5 6.75ZM6 5.25C6 3.675 7.175 2.5 8.75 2.5H9.25C10.825 2.5 12 3.675 12 5.25V6.75H6V5.25ZM13.5 14.25H4.5V8.25H13.5V14.25ZM9 10.5C8.175 10.5 7.5 11.175 7.5 12C7.5 12.825 8.175 13.5 9 13.5C9.825 13.5 10.5 12.825 10.5 12C10.5 11.175 9.825 10.5 9 10.5Z"
                fill=""
              />
            </svg>
            Thay đổi mật khẩu
          </button>
        </div>
      </div>
    </div>
    <!-- Profile Update Modal -->
    <ProfileUpdateModal v-model="showProfileModal" @close="showProfileModal = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'
import ProfileUpdateModal from './ProfileUpdateModal.vue'

// Emits
const emit = defineEmits(['change-password'])

const authStore = useAuthStore()
const { authUser } = storeToRefs(authStore)

const showProfileModal = ref(false)

// Computed properties for user data - reactive to authUser changes
const userName = computed(() => {
  return authUser.value?.name || 'Không có'
})

const userEmail = computed(() => {
  return authUser.value?.email || 'Không có'
})

const userRole = computed(() => {
  if (!authUser.value?.roles || authUser.value.roles.length === 0) return []
  return authUser.value.roles
})

const userStatus = computed(() => {
  const status = authUser.value?.status || 'UNKNOWN'
  switch (status) {
    case 'ACTIVE':
      return 'Hoạt động'
    case 'INACTIVE':
      return 'Không hoạt động'
    case 'SUSPENDED':
      return 'Tạm khóa'
    default:
      return 'Không xác định'
  }
})

const statusClass = computed(() => {
  const status = authUser.value?.status || 'UNKNOWN'
  const baseClass = 'text-xs px-2 py-1 rounded-full font-medium'
  switch (status) {
    case 'ACTIVE':
      return `${baseClass} bg-green-100 text-green-800`
    case 'INACTIVE':
      return `${baseClass} bg-gray-100 text-gray-800`
    case 'SUSPENDED':
      return `${baseClass} bg-red-100 text-red-800`
    default:
      return `${baseClass} bg-gray-100 text-gray-800`
  }
})

// Initialize on mount
onMounted(() => {
  // Component mounted
})
</script>
