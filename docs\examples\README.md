# 📚 Examples - <PERSON><PERSON> dụ mẫu

Th<PERSON> mục này chứa các ví dụ mẫu và templates chuẩn theo kiến trúc dự án.

## 📁 Danh sách Files

### 🎯 Templates (Mẫu chuẩn)

- [`ComponentTemplate.vue`](./ComponentTemplate.vue) - Template component chuẩn
- [`ComposableTemplate.js`](./ComposableTemplate.js) - Template composable chuẩn
- [`TestTemplate.test.js`](./TestTemplate.test.js) - Template test chuẩn

### 🏗️ Real Examples (Ví dụ thực tế)

- [`UserFormExample.vue`](./UserFormExample.vue) - Modal form quản lý người dùng

## 🚀 Cách sử dụng

### 1. Tạo Component mới

```bash
# Copy template
cp docs/examples/ComponentTemplate.vue src/components/pages/feature/FeatureModal.vue

# Tùy chỉnh theo nhu cầu
```

### 2. Tạo Composable mới

```bash
# Copy template
cp docs/examples/ComposableTemplate.js src/composables/useFeature.js

# Cập nhật logic business
```

### 3. Tạo Test mới (Optional)

```bash
# Copy template (khuyến khích cho logic phức tạp)
cp docs/examples/TestTemplate.test.js src/composables/useFeature.test.js

# Viết test cases cho business logic quan trọng
```

## 🎨 Patterns chính

### Component Pattern

```vue
<!-- UI chỉ handle presentation -->
<template>
  <Modal v-model="isVisible" @close="handleClose">
    <FormField :error="errors.field">
      <el-input v-model="formData.field" />
    </FormField>
  </Modal>
</template>

<script setup>
// Business logic từ composable
const { formData, errors, submitForm } = useFeature()
</script>
```

### Composable Pattern

```javascript
// Business logic tách biệt
export const useFeature = () => {
  const formData = reactive({ field: '' })
  const submitForm = async () => {
    /* logic */
  }
  return { formData, submitForm }
}
```

### Test Pattern

```javascript
// Test comprehensive
describe('useFeature', () => {
  it('nên validate form đúng', async () => {
    const { validateForm } = useFeature()
    expect(await validateForm()).toBe(true)
  })
})
```

## 🔄 Workflow Development

1. **Phân tích requirement** → Xác định business logic
2. **Tạo composable** → Implement logic business
3. **Viết tests** → Đảm bảo logic đúng (optional, khuyến khích cho logic phức tạp)
4. **Tạo component** → Implement UI
5. **Integration** → Kết nối component với composable
6. **Review** → Kiểm tra theo checklist

## 💡 Best Practices

### ✅ Nên làm

- Sử dụng templates làm base
- Tách biệt UI và business logic
- Unified field names với backend
- Test coverage cho logic quan trọng (optional)
- Consistent error handling

### ❌ Không nên làm

- Business logic trong component
- Mapping field names
- Hardcode values
- Inconsistent patterns
- Over-testing cho logic đơn giản

## 📖 Tham khảo

- [Architecture Documentation](../ARCHITECTURE.md)
- [Component Guidelines](../ARCHITECTURE.md#component-architecture)
- [Composable Guidelines](../ARCHITECTURE.md#composables-pattern)
- [Testing Guidelines](../ARCHITECTURE.md#testing-guidelines)
