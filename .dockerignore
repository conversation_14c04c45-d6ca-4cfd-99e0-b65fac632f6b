# <PERSON><PERSON>n không cho các file và thư mục không cần thiết đư<PERSON>c sao chép vào Docker image

# Lịch sử Git
.git
.gitignore

# Dependencies
node_modules

# Build output
dist

# Các file môi trường cục bộ
.env
.env.*
!/.env.staging
!/.env.production
!/.env.example

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Thư mục hệ thống
.DS_Store
Thumbs.db

# <PERSON><PERSON><PERSON> hình editor
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
