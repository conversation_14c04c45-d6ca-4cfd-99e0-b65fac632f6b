import apiAxios from '@/utils/configs/axios.config.js'

const settingsApi = {
  getSettings(params = {}) {
    return apiAxios({
      method: 'get',
      url: '/site-settings',
      params,
    })
  },

  createSetting(settingData) {
    return apiAxios({
      method: 'post',
      url: '/site-settings',
      data: settingData,
    })
  },

  getSettingById(id) {
    return apiAxios({
      method: 'get',
      url: `/site-settings/${id}`,
    })
  },

  updateSetting(id, settingData) {
    return apiAxios({
      method: 'put',
      url: `/site-settings/${id}`,
      data: settingData,
    })
  },

  deleteSetting(id) {
    return apiAxios({
      method: 'delete',
      url: `/site-settings/${id}`,
    })
  },

  // Batch update multiple settings (if supported by API)
  updateSettings(settings) {
    return apiAxios({
      method: 'put',
      url: '/site-settings/batch',
      data: { settings },
    })
  },
}

export default settingsApi
