<template>
  <div class="menu-item-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      size="default"
      class="relative"
      hide-required-asterisk
    >
      <el-form-item prop="title">
        <template #label>
          <span>Tiêu đề <span class="required-asterisk">*</span></span>
        </template>
        <el-input
          v-model="formData.title"
          placeholder="Nhập tiêu đề menu item"
          maxlength="100"
          @input="handleTitleInput"
          @blur="handleTitleBlur"
        />
      </el-form-item>

      <el-form-item prop="slug">
        <template #label>
          <span>Slug <span class="required-asterisk">*</span></span>
        </template>
        <el-input 
          v-model="formData.slug" 
          placeholder="Slug sẽ được tạo tự động từ tiêu đề" 
          maxlength="100"
          @input="handleSlugInput"
          @blur="handleSlugBlur"
        />
        <div class="form-help"><PERSON><PERSON> trống để tự động tạo từ tiêu đề</div>
      </el-form-item>

      <el-form-item label="Menu cha">
        <el-select
          v-model="formData.parent_id"
          placeholder="Chọn menu cha (để trống nếu là menu gốc)"
          clearable
          class="w-full"
          popper-class="menu-item-form-dropdown"
        >
          <el-option
            label="Chọn menu cha"
            :value="null"
          />
          <el-option
            v-for="item in parentOptions"
            :key="item.id"
            :label="item.title"
            :value="item.id"
            :disabled="item.id === formData.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item prop="target">
        <template #label>
          <span>Target <span class="required-asterisk">*</span></span>
        </template>
        <el-select
          v-model="formData.target"
          placeholder="Chọn target"
          class="w-full"
          popper-class="menu-item-form-dropdown"
          @change="handleTargetChange"
        >
          <el-option label="Cùng tab (_self)" value="_self" />
          <el-option label="Tab mới (_blank)" value="_blank" />
        </el-select>
      </el-form-item>

      <el-form-item label="Thứ tự hiển thị" prop="order">
        <el-input-number 
          v-model="formData.order" 
          :min="0" 
          placeholder="Thứ tự hiển thị (mặc định 0)" 
          class="w-full"
          @change="handleOrderChange"
          @blur="handleOrderBlur"
        />
        <div class="form-help">Số nhỏ hơn sẽ hiển thị trước</div>
      </el-form-item>

      <el-form-item label="Loại liên kết">
        <el-select
          v-model="formData.linkable_type"
          placeholder="Chọn loại liên kết"
          class="w-full"
          popper-class="menu-item-form-dropdown"
          @change="handleLinkableTypeChange"
        >
          <el-option label="URL tùy chỉnh" value="" />
          <el-option label="Bài viết" :value="1" />
          <el-option label="Danh mục" :value="2" />
          <el-option label="Trang tĩnh" :value="3" />
        </el-select>
        <div class="form-help">Chọn loại nội dung để liên kết đến</div>
      </el-form-item>

      <el-form-item v-if="formData.linkable_type" prop="linkable_id">
        <template #label>
          <span>Đối tượng liên kết <span class="required-asterisk">*</span></span>
        </template>
        <el-select
          v-model="formData.linkable_id"
          placeholder="Chọn đối tượng cụ thể"
          class="w-full"
          popper-class="menu-item-form-dropdown"
          filterable
          :loading="loadingLinkableItems"
          @change="handleLinkableIdChange"
        >
                     <el-option 
             v-for="item in linkableItems" 
             :key="item.id" 
             :label="getLinkableItemLabel(item)" 
             :value="item.id" 
           />
        </el-select>
        <div class="form-help">Chọn đối tượng cụ thể để liên kết đến</div>
      </el-form-item>

      <el-form-item v-if="!formData.linkable_type" prop="custom_url">
        <template #label>
          <span>URL tùy chỉnh</span>
        </template>
        <el-input
          v-model="formData.custom_url"
          placeholder="Nhập URL tùy chỉnh (VD: /lien-he, https://example.com)"
          maxlength="255"
          @input="handleCustomUrlInput"
          @blur="handleCustomUrlBlur"
        />
        <div class="form-help">Đường dẫn tùy chỉnh nếu không liên kết đến đối tượng cụ thể</div>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="formData.status"> Hiển thị menu item </el-checkbox>
      </el-form-item>

      <ButtonModalCommon 
        :loading="loading"
        :can-submit="true"
        cancel-text="Hủy"
        :submit-text="isEdit ? 'Cập nhật' : 'Thêm mới'"
        @cancel="handleCancel"
        @submit="handleSubmit"
      />
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'
import { useFormValidation, validationPresets } from '@/composables/useFormValidation.js'
import { LINKABLE_TYPE } from '@/utils/configs/constant.config.js'
import { menusApi } from '@/utils/apis/index.js'
import { extractItemsFromResponse } from '@/utils/helpers/response.helper.js'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'
import '@/assets/styles/modules/cms/_menu-item-form.scss'

const props = defineProps({
  item: {
    type: Object,
    default: null,
  },
  menuId: {
    type: [Number, String],
    required: true,
  },
  menuItems: {
    type: Array,
    default: () => [],
  },
  currentMenu: {
    type: Object,
    default: null,
  },
  linkableTypes: {
    type: Array,
    default: () => [],
  },
  createMenuItem: {
    type: Function,
    required: true,
  },
  updateMenuItem: {
    type: Function,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['success', 'cancel'])

const {
  formRef,
  clearFieldValidation,
  clearMultipleFieldsValidation,
  clearAllValidation,
  validateField,
  validateForm,
  smartValidateField,
  handleInputChange,
  handleFieldBlur,
  createFieldHandlers,
  autoGenerateField
} = useFormValidation()

const isEdit = computed(() => !!props.item)

const parentOptions = computed(() => {
  const flattenItems = (items, level = 0) => {
    const result = []
    for (const item of items) {
      if (props.item && (item.id === props.item.id || isChildOf(item, props.item.id))) {
        continue
      }

      result.push({
        ...item,
        title: '  '.repeat(level) + item.title,
        level,
      })

      if (item.children && item.children.length > 0) {
        result.push(...flattenItems(item.children, level + 1))
      }
    }
    return result
  }

  return flattenItems(props.menuItems)
})

const formData = reactive({
  title: '',
  slug: '',
  parent_id: null,
  order: 0,
  target: '_self',
  linkable_id: null,
  linkable_type: '',
  custom_url: '',
  status: true,
})

const linkableItems = ref([])
const loadingLinkableItems = ref(false)

const formRules = {
  title: [
    { required: true, message: 'Vui lòng nhập tiêu đề', trigger: 'blur' },
    { min: 1, max: 100, message: 'Tiêu đề phải từ 1-100 ký tự', trigger: 'blur' },
  ],
  slug: [
    { required: true, message: 'Vui lòng nhập slug', trigger: 'blur' },
    { min: 1, max: 100, message: 'Slug phải từ 1-100 ký tự', trigger: 'blur' },
    {
      pattern: /^[a-z0-9_-]+$/,
      message: 'Slug chỉ được chứa chữ thường, số, dấu gạch dưới và gạch ngang',
      trigger: 'blur',
    },
  ],
  target: [{ required: true, message: 'Vui lòng chọn target', trigger: 'change' }],
  order: [
    { type: 'number', message: 'Thứ tự phải là số', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Thứ tự phải lớn hơn hoặc bằng 0', trigger: 'blur' },
  ],
  linkable_id: [
    {
      validator: (rule, value, callback) => {
        if (formData.linkable_type && !value) {
          callback(new Error('Vui lòng chọn đối tượng liên kết'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  custom_url: [
    {
      pattern: /^(https?:\/\/.+|\/.+)$/,
      message: 'URL phải bắt đầu bằng http://, https:// hoặc /',
      trigger: 'blur',
    },
  ],
}

const handleTitleInput = (value) => {
  if (value && value.trim()) {
    const newSlug = generateSlugWithDash(value)
    formData.slug = newSlug
    
    clearFieldValidation('slug')
  }
  
  handleInputChange('title', value, validationPresets.required)
}

const handleTitleBlur = () => {
  handleFieldBlur('title')
}

const handleSlugInput = (value) => {
  handleInputChange('slug', value, validationPresets.slug)
}

const handleSlugBlur = () => {
  handleFieldBlur('slug')
}

const handleTargetChange = (value) => {
  if (value) {
    clearFieldValidation('target')
  } else {
    validateField('target')
  }
}

const handleOrderChange = (value) => {
  if (value !== null && value >= 0) {
    clearFieldValidation('order')
  } else {
    validateField('order')
  }
}

const handleOrderBlur = () => {
  handleFieldBlur('order')
}

const handleLinkableTypeChange = async () => {
  formData.linkable_id = null
  linkableItems.value = []

  if (formData.linkable_type) {

    formData.custom_url = ''
    clearFieldValidation('custom_url')
    
    await fetchLinkableItems()
  }
}

const fetchLinkableItems = async () => {
  try {
    loadingLinkableItems.value = true

    let type = formData.linkable_type
    if (type) {
      const response = await menusApi.getDataLinkables(type)
      

      const { success, items, message } = extractItemsFromResponse(response)
      
      if (success) {
        linkableItems.value = items
      } else {
        throw new Error(message || 'Không thể tải dữ liệu liên kết')
      }
    }
  } catch (error) {
    console.error('Error fetching linkable items:', error)
    linkableItems.value = []
  } finally {
    loadingLinkableItems.value = false
  }
}      

const getLinkableItemLabel = (item) => {

  return item.label || item.title || item.name || 'Unknown'
}

const handleLinkableIdChange = (value) => {
  if (formData.linkable_type && value) {
    clearFieldValidation('linkable_id')
  } else if (formData.linkable_type && !value) {
    validateField('linkable_id')
  }
}

const handleCustomUrlInput = (value) => {
  if (!formData.linkable_type) {
    handleInputChange('custom_url', value, { required: false, pattern: /^(https?:\/\/.+|\/.+)$/ })
  }
}

const handleCustomUrlBlur = () => {
  if (!formData.linkable_type) {
    handleFieldBlur('custom_url')
  }
}

const resetForm = () => {
  formData.title = ''
  formData.slug = ''
  formData.parent_id = null
  formData.order = 0
  formData.target = '_self'
  formData.linkable_id = null
  formData.linkable_type = ''
  formData.custom_url = ''
  formData.status = true

  linkableItems.value = []
  loadingLinkableItems.value = false

  clearAllValidation()
}

const populateForm = async () => {
  if (props.item) {
    formData.title = props.item.title || ''
    formData.slug = props.item.slug || ''
    formData.parent_id = props.item.parent_id
    formData.order = props.item.order || 0
    formData.target = props.item.target || '_self'
    formData.custom_url = props.item.custom_url || ''
    formData.status = props.item.status !== false

    if (props.item.linkable) {
      formData.linkable_type = props.item.linkable.type || ''
      formData.linkable_id = props.item.linkable.id || null
      
      if (formData.linkable_type) {
        await fetchLinkableItems()
      }
    } else {
      formData.linkable_id = props.item.linkable_id || null
      formData.linkable_type = props.item.linkable_type || ''
      
      if (formData.linkable_type) {
        await fetchLinkableItems()
      }
    }
  }
}

const isChildOf = (item, parentId) => {
  if (item.parent_id === parentId) return true
  if (item.children) {
    return item.children.some((child) => isChildOf(child, parentId))
  }
  return false
}

const handleCancel = () => {
  emit('cancel')
  resetForm()
}

const handleSubmit = async () => {
  try {
    const valid = await validateForm()
    if (!valid) return

    const submitData = {
      menu_id: props.menuId,
      title: formData.title,
      slug: formData.slug,
      parent_id: formData.parent_id,
      order: formData.order,
      target: formData.target,
      linkable_id: formData.linkable_id,
      linkable_type: formData.linkable_type,
      custom_url: formData.custom_url,
      status: formData.status ? 1 : 0, 
    }
    

    if (isEdit.value) {
      await props.updateMenuItem(props.item.id, submitData)
    } else {
      await props.createMenuItem(submitData)
    }

    emit('success')
    resetForm()
  } catch (error) {
    console.error('Error submitting form:', error)
  }
}

watch(
  () => props.item,
  () => {
    nextTick(async () => {
      if (props.item) {
        await populateForm()
      } else {
        resetForm()
      }
    })
  },
  { immediate: true },
)
</script>

<style scoped>
.menu-item-form :deep(.menu-item-form-dropdown) {
  z-index: 3000 !important;
}

.menu-item-form {
  position: relative;
  z-index: 1;
}

.menu-item-form :deep(.el-form-item) {
  margin-bottom: 30px !important;
}

.menu-item-form :deep(.el-form-item__error) {
  margin-bottom: 20px !important;
}

.menu-item-form :deep(.el-form-item__content) {
  position: relative;
}

.form-help {
  margin-top: 6px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}
</style>
