<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa trang tĩnh' : 'Thêm trang tĩnh mới'"
    width="1000px"
    destroy-on-close
    @closed="resetForm"
  >
    <div class="p-6">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" class="static-page-form">
        <!-- Thông tin cơ bản -->
        <div class="space-y-4">
          <h3 class="mb-4 text-lg font-medium">Thông tin cơ bản</h3>
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <el-form-item label="Tiêu đề" prop="title">
              <el-input v-model="form.title" placeholder="Nhập tiêu đề trang" @input="generateSlug" />
            </el-form-item>

            <el-form-item label="Đường dẫn" prop="slug">
              <el-input v-model="form.slug" placeholder="Đường dẫn" readonly />
            </el-form-item>
          </div>

          <el-form-item label="Trạng thái" prop="status">
            <el-select v-model="form.status" class="w-full">
              <el-option :value="1" label="Kích hoạt" />
              <el-option :value="0" label="Vô hiệu" />
            </el-select>
          </el-form-item>
        </div>

        <!-- Nội dung -->
        <div class="mt-6 space-y-4">
          <h3 class="mb-4 text-lg font-medium">Nội dung</h3>

          <el-form-item label="Nội dung trang" prop="body">
            <!-- Sử dụng QuillEditor thay cho textarea -->
            <div class="rounded border border-gray-300">
              <QuillEditor
                v-model:content="form.body"
                content-type="html"
                :options="editorOptions"
                style="height: 400px"
              />
            </div>
          </el-form-item>
        </div>

        <!-- SEO Meta -->
        <div class="mt-6 mb-6 space-y-8">
          <h3 class="mb-4 text-lg font-medium">Thông tin SEO</h3>
          <div>
            <el-form-item label="Meta Title" prop="meta_title">
              <el-input v-model="form.meta_title" placeholder="Tiêu đề hiển thị trên trình duyệt" />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="Meta Description" prop="meta_description">
              <el-input
                v-model="form.meta_description"
                type="textarea"
                :rows="3"
                placeholder="Mô tả ngắn về trang (hiển thị trong kết quả tìm kiếm)"
              />
            </el-form-item>
          </div>

          <div>
            <el-form-item label="Meta Keywords" prop="meta_keywords">
              <el-input v-model="form.meta_keywords" placeholder="Từ khóa, phân cách bằng dấu phẩy" />
            </el-form-item>
          </div>
        </div>
      </el-form>

      <ButtonModalCommon
        :loading="loading"
        :can-submit="true"
        cancel-text="Hủy"
        :submit-text="isEdit ? 'Cập nhật' : 'Thêm mới'"
        @cancel="dialogVisible = false"
        @submit="submitForm"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useStaticPage } from '@/composables/modules/cms'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import DOMPurify from 'dompurify'
import ButtonCommon from '~/components/common/ButtonCommon.vue'
import ButtonModalCommon from '~/components/common/ButtonModalCommon.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  page: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'saved'])

// Refs
const formRef = ref(null)
const loading = ref(false)
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// Form data
const defaultForm = {
  title: '',
  slug: '',
  body: '',
  status: 1,
  meta_title: '',
  meta_description: '',
  meta_keywords: '',
}

const form = reactive({ ...defaultForm })

// Computed
const isEdit = computed(() => !!props.page?.id)

// Rules
const rules = {
  title: [{ required: true, message: 'Vui lòng nhập tiêu đề', trigger: 'blur' }],
  slug: [{ required: true, message: 'Vui lòng nhập đường dẫn', trigger: 'blur' }],
  body: [{ required: true, message: 'Vui lòng nhập nội dung', trigger: 'blur' }],
  status: [{ required: true, message: 'Vui lòng chọn trạng thái', trigger: 'change' }],
}

// Editor options
const editorOptions = {
  theme: 'snow',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link', 'image', 'video'],
    ],
  },
  placeholder: 'Nhập nội dung trang...',
}

// Methods
const resetForm = () => {
  Object.assign(form, defaultForm)
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const generateSlug = () => {
  form.slug = generateSlugWithDash(form.title)
}

const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // Làm sạch nội dung HTML trước khi lưu
        const sanitizedData = {
          ...form,
          body: DOMPurify.sanitize(form.body),
        }

        if (isEdit.value) {
          await useStaticPage().updateStaticPage(props.page.id, sanitizedData)
        } else {
          await useStaticPage().createStaticPage(sanitizedData)
        }

        dialogVisible.value = false
        emit('saved')
      } catch (error) {
        console.error('Error saving static page:', error)
        ElMessage.error('Không thể lưu trang')
      } finally {
        loading.value = false
      }
    }
  })
}

// Watch for page prop changes to update form
watch(
  () => props.page,
  (newPage) => {
    if (newPage) {
      Object.keys(form).forEach((key) => {
        if (key in newPage) {
          form[key] = newPage[key]
        }
      })
    } else {
      resetForm()
    }
  },
  { immediate: true, deep: true },
)
</script>

<style scoped>
.static-page-form :deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
