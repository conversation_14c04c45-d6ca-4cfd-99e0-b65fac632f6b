import { getDataBy<PERSON><PERSON>, destroyData<PERSON>y<PERSON><PERSON> } from './localStorage.helper.js'

/**
 * Kiểm tra xem user đã đăng nhập chưa
 * @returns {boolean}
 */
export const isLoggedIn = () => {
  const token = getDataByKey('token')
  const user = getDataByKey('authUser')
  return !!(token && user)
}

/**
 * Lấy thông tin user từ localStorage
 * @returns {object|null}
 */
export const getCurrentUser = () => {
  const userData = getDataByKey('authUser')
  if (!userData) return null

  try {
    return JSON.parse(userData)
  } catch (error) {
    console.error('Error parsing user data:', error)
    return null
  }
}

/**
 * Lấy token từ localStorage
 * @returns {string|null}
 */
export const getToken = () => {
  return getDataByKey('token')
}

/**
 * Xóa tất cả dữ liệu authentication
 */
export const clearAuthData = () => {
  destroyDataBy<PERSON><PERSON>('token')
  destroyDataByKey('authUser')
}

/**
 * Kiểm tra xem token có hợp lệ không (basic check)
 * @returns {boolean}
 */
export const isTokenValid = () => {
  const token = getToken()
  if (!token) return false

  // Basic check - có thể mở rộng để check JWT expiration
  return token.length > 10
}
