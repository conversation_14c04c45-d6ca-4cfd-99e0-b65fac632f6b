import apiAxios from '@/utils/configs/axios.config.js'

const rolesApi = {
  // Get roles list with filters
  getRoles(params = {}) {
    return apiAxios({
      method: 'get',
      url: '/roles',
      params: params,
    })
  },

  // Get role by ID
  getRole(id) {
    return apiAxios({
      method: 'get',
      url: `/roles/${id}`,
    })
  },

  // Create new role
  createRole(data) {
    return apiAxios({
      method: 'post',
      url: '/roles',
      data: data,
    })
  },

  // Update role
  updateRole(id, data) {
    return apiAxios({
      method: 'post',
      url: `/roles/${id}`,
      data: data,
    })
  },

  // Delete role
  deleteRole(id) {
    return apiAxios({
      method: 'delete',
      url: `/roles/${id}`,
    })
  },

  // Assign permissions to role
  assignPermissions(id, data) {
    return apiAxios({
      method: 'post',
      url: `/roles/${id}/assign-permissions`,
      data: data,
    })
  },

  // Revoke permissions from role
  revokePermissions(id, data) {
    return apiAxios({
      method: 'post',
      url: `/roles/${id}/revoke-permissions`,
      data: data,
    })
  },
}

export default rolesApi
