import * as yup from 'yup'

export const DEFAULT_MESSAGES = {
  required: '${label} không được bỏ trống.',
  email: '${label} không đúng định dạng.',
  url: '${label} không đúng định dạng URL.',
  min: '${label} không được ít hơn ${min} ký tự.',
  max: '${label} không được vượt quá ${max} ký tự.',
  oneOf: '${label} không hợp lệ.',
  // Password specific messages
  passwordMatch: 'Mật khẩu xác nhận không khớp.',
  passwordCurrent: 'Mật khẩu hiện tại không được bỏ trống.',
  passwordNew: 'Mật khẩu mới không được bỏ trống.',
  passwordStrength: 'Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn.',
  passwordSame: '<PERSON><PERSON>t khẩu mới phải khác với mật khẩu hiện tại.',
  // Profile specific messages
  nameRequired: 'Họ và tên không được bỏ trống.',
  emailRequired: 'Email không được bỏ trống.',
  emailFormat: 'Email không đúng định dạng.',
  avatarUrl: 'Link ảnh đại diện không đúng định dạng URL.',
}

// Password strength checker function
export const checkPasswordStrength = (password) => {
  let strength = 0
  if (password.length >= 8) strength++
  if (/[A-Z]/.test(password) && /[a-z]/.test(password)) strength++
  if (/\d/.test(password) && /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) strength++
  return strength
}

// Custom Yup method for password confirmation
yup.addMethod(yup.string, 'confirmPassword', function (ref, msg) {
  return this.test({
    name: 'confirmPassword',
    exclusive: false,
    params: { ref },
    message: msg || DEFAULT_MESSAGES.passwordMatch,
    test(value) {
      const other = this.resolve(ref)
      return !value || !other || value === other
    },
  })
})

// Custom Yup method for password strength
yup.addMethod(yup.string, 'passwordStrength', function (minStrength = 2, msg) {
  return this.test({
    name: 'passwordStrength',
    exclusive: false,
    message: msg || DEFAULT_MESSAGES.passwordStrength,
    test(value) {
      if (!value) return true // Let required() handle empty values
      return checkPasswordStrength(value) >= minStrength
    },
  })
})

// Custom Yup method for password not same as current
yup.addMethod(yup.string, 'notSameAs', function (ref, msg) {
  return this.test({
    name: 'notSameAs',
    exclusive: false,
    params: { ref },
    message: msg || DEFAULT_MESSAGES.passwordSame,
    test(value) {
      const other = this.resolve(ref)
      return !value || !other || value !== other
    },
  })
})

export async function validate(schema, data, labelMap = {}) {
  try {
    const value = await schema.validate(data, { abortEarly: false })
    return { value, errors: {} }
  } catch (err) {
    const errors = {}
    if (err.inner && Array.isArray(err.inner)) {
      err.inner.forEach((e) => {
        const label = labelMap[e.path] || e.path
        errors[e.path] = e.message.replace('${label}', label)
      })
    } else if (err.path) {
      const label = labelMap[err.path] || err.path
      errors[err.path] = err.message.replace('${label}', label)
    }
    return { value: null, errors }
  }
}
