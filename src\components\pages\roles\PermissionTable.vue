<template>
  <div class="permission-table flex h-full flex-col">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
      <!-- Compact Header Section -->
      <div
        class="sticky top-0 z-10 bg-gradient-to-r from-blue-50 to-indigo-50 px-3 py-2 sm:px-4 sm:py-3 dark:from-gray-800 dark:to-gray-900"
      >
        <!-- Role Info Slot (if provided) -->
        <div v-if="$slots['role-info']" class="pt-1">
          <slot name="role-info" />
        </div>
      </div>

      <!-- Permissions Content -->
      <div class="">
        <div v-if="!localPermissions || localPermissions.length === 0" class="py-4 text-center sm:py-6 md:py-8">
          <div class="flex flex-col items-center gap-2 sm:gap-3">
            <div
              class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 sm:h-12 sm:w-12 dark:bg-gray-800"
            >
              <el-icon class="text-lg text-gray-400 sm:text-xl"><Document /></el-icon>
            </div>
            <div class="text-sm font-medium text-gray-700 sm:text-base dark:text-gray-300">
              Không có dữ liệu quyền hạn
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Chưa có quyền hạn nào được định nghĩa</div>
          </div>
        </div>

        <div v-else-if="!selectedRole" class="py-4 text-center sm:py-6 md:py-8">
          <div class="flex flex-col items-center gap-2 sm:gap-3">
            <div
              class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 sm:h-12 sm:w-12 dark:bg-blue-900"
            >
              <el-icon class="text-lg text-blue-500 sm:text-xl"><User /></el-icon>
            </div>
            <div class="text-sm font-medium text-gray-700 sm:text-base dark:text-gray-300">
              Vui lòng chọn một vai trò
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Chọn vai trò từ danh sách bên trái để xem phân quyền
            </div>
          </div>
        </div>

        <!-- Tree Table -->
        <transition name="table-fade" mode="out-in" appear>
          <div
            v-if="localPermissions && localPermissions.length > 0 && selectedRole"
            key="table"
            class="table-wrapper overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900"
          >
            <el-table
              ref="tableRef"
              :data="tableData"
              row-key="id"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              :lazy="true"
              :load="loadNode"
              :default-expand-all="true"
              :expand-row-keys="expandedKeys"
              @expand-change="handleExpandChange"
              border
              stripe
              class="permission-tree-table w-full"
              :max-height="tableMaxHeight"
              :height="tableMaxHeight"
              :show-expand-column="false"
              :header-cell-style="tableStyles.headerCellStyle"
              :cell-style="tableStyles.cellStyle"
              v-loading="permissionsLoading"
              element-loading-text="Đang tải quyền hạn..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
            >
              <!-- Permission Group Column -->
              <el-table-column prop="name" label="Nhóm quyền" min-width="280" sm:min-width="320" fixed="left">
                <template #default="{ row }">
                  <div class="flex items-center gap-2 py-1 sm:gap-3">
                    <!-- Level Indentation -->
                    <div class="flex items-center">
                      <div
                        v-for="level in getRowLevel(row)"
                        :key="level"
                        class="h-0.5 w-6 bg-gradient-to-r from-blue-300 to-transparent sm:w-8 dark:from-blue-600"
                      ></div>
                    </div>

                    <!-- Expand/Collapse Icon -->
                    <div v-if="row.hasChildren || (row.children && row.children.length > 0)" class="flex-shrink-0">
                      <el-tooltip
                        :content="isRowExpanded(row) ? 'Thu gọn nhóm' : 'Mở rộng nhóm'"
                        placement="top"
                        effect="dark"
                        :show-after="500"
                      >
                        <div
                          class="expand-button-wrapper"
                          @click="toggleRowExpansion(row)"
                          @keydown.enter="toggleRowExpansion(row)"
                          @keydown.space="toggleRowExpansion(row)"
                          tabindex="0"
                          role="button"
                          :aria-label="isRowExpanded(row) ? 'Thu gọn nhóm' : 'Mở rộng nhóm'"
                          :aria-expanded="isRowExpanded(row)"
                        >
                          <el-icon class="expand-icon" :class="{ 'is-expanded': isRowExpanded(row) }">
                            <ArrowRightIcon />
                          </el-icon>
                        </div>
                      </el-tooltip>
                    </div>
                    <div v-else class="w-3 flex-shrink-0 sm:w-4"></div>

                    <!-- Group Icon -->
                    <div
                      class="flex h-6 w-6 items-center justify-center rounded-lg bg-gradient-to-br from-blue-100 to-indigo-100 sm:h-7 sm:w-7 dark:from-blue-900 dark:to-indigo-900"
                    >
                      <el-icon class="text-xs text-blue-600 sm:text-sm dark:text-blue-400">
                        <Folder />
                      </el-icon>
                    </div>

                    <!-- Group Info -->
                    <div class="flex min-w-0 flex-1 flex-col">
                      <span class="text-sm font-semibold text-gray-900 sm:text-base dark:text-white">{{
                        row.name
                      }}</span>
                      <span class="truncate text-xs text-gray-500 sm:text-sm dark:text-gray-400">{{ row.code }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- Dynamic Permission Columns -->
              <el-table-column
                v-for="permissionType in availablePermissionTypes"
                :key="permissionType"
                :label="getPermissionTypeLabel(permissionType)"
                :width="getColumnWidth(permissionType)"
                align="center"
                :header-cell-style="isRoleProtected ? { color: '#d97706', fontWeight: '600' } : undefined"
              >
                <template #default="{ row }">
                  <div class="flex justify-center py-1">
                    <el-tooltip
                      v-if="getPermissionByType(row, permissionType)"
                      :content="
                        isRoleProtected
                          ? `Vai trò được bảo vệ - Không thể thay đổi quyền hạn`
                          : getPermissionByType(row, permissionType).label
                      "
                      placement="top"
                      effect="dark"
                    >
                      <el-checkbox
                        :model-value="getPermissionActiveState(row, permissionType)"
                        @change="handlePermissionChange(getPermissionByType(row, permissionType).id, $event)"
                        size="large"
                        :disabled="!props.canAssign || isCheckboxDisabled(getPermissionByType(row, permissionType).id)"
                        :class="['permission-checkbox', { 'protected-role-checkbox': isRoleProtected }]"
                      />
                    </el-tooltip>
                    <span v-else class="text-base text-gray-300 dark:text-gray-600">-</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

import { ArrowRight as ArrowRightIcon } from '@element-plus/icons-vue'
import { Folder, Document, User } from '@element-plus/icons-vue'
import { useRoles } from '@/composables/modules/users/useRoles.js'

// Props
const props = defineProps({
  selectedRole: {
    type: Object,
    default: null,
  },
  permissions: {
    type: Array,
    default: () => [],
  },
  permissionsLoading: {
    type: Boolean,
    default: false,
  },
  canAssign: {
    type: Boolean,
    default: true,
  },
})

// Emits
const emit = defineEmits(['permission-changed'])

// Composables
const { assignPermissions, revokePermissions, saving: savingPermissions } = useRoles()

// Local reactive state for permissions
const localPermissions = ref([])

// Tree table state
const tableData = ref([])
const expandedKeys = ref([])
const tableRef = ref(null)

// Loading state for individual permission changes
const changingPermissions = ref(new Set())

// Reactive dark mode state
const isDarkMode = ref(false)

// Function to check dark mode
const checkDarkMode = () => {
  isDarkMode.value = document.documentElement.classList.contains('dark')
}

// Check if selected role is protected
const isRoleProtected = computed(() => {
  if (!props.selectedRole) return false

  const isProtectedValue = props.selectedRole.is_protected
  return isProtectedValue === 1 || isProtectedValue === '1' || isProtectedValue === true || isProtectedValue === 'true'
})

// Count active permissions from local state
const activePermissionCount = computed(() => {
  if (!localPermissions.value || localPermissions.value.length === 0) return 0

  const countActivePermissions = (groups) => {
    let count = 0
    for (const group of groups) {
      // Count direct active permissions
      if (group.permissions) {
        count += group.permissions.filter((p) => p.active).length
      }
      // Count children active permissions
      if (group.children) {
        count += countActivePermissions(group.children)
      }
    }
    return count
  }

  return countActivePermissions(localPermissions.value)
})

// Check if any checkbox should be disabled
const hasDisabledCheckboxes = computed(() => {
  return isRoleProtected.value || savingPermissions.value || changingPermissions.value.size > 0
})

// Method to check if a specific checkbox should be disabled
const isCheckboxDisabled = (permissionId) => {
  return savingPermissions.value || isRoleProtected.value || changingPermissions.value.has(permissionId)
}

// Computed property for table styles that react to theme changes
const tableStyles = computed(() => ({
  headerCellStyle: {
    backgroundColor: isDarkMode.value ? '#0f172a' : '#f8fafc',
    color: isDarkMode.value ? '#f8fafc' : '#374151',
    fontWeight: '600',
    fontSize: '14px',
    borderBottom: isDarkMode.value ? '1px solid #1e293b' : '1px solid #e5e7eb',
    padding: '12px 16px',
  },
  cellStyle: {
    backgroundColor: isDarkMode.value ? '#0f172a' : '#ffffff',
    color: isDarkMode.value ? '#f8fafc' : '#374151',
    borderBottom: isDarkMode.value ? '1px solid #1e293b' : '1px solid #f3f4f6',
    padding: '10px 16px',
  },
}))

// Computed properties

const availablePermissionTypes = computed(() => {
  const types = new Set()

  const extractTypes = (groups) => {
    for (const group of groups) {
      if (group.permissions) {
        group.permissions.forEach((permission) => {
          types.add(permission.type)
        })
      }
      if (group.children) {
        extractTypes(group.children)
      }
    }
  }

  extractTypes(localPermissions.value)

  // Sort types in a logical order
  const typeOrder = ['view', 'create', 'edit', 'delete', 'export']
  const sortedTypes = Array.from(types).sort((a, b) => {
    const aIndex = typeOrder.indexOf(a)
    const bIndex = typeOrder.indexOf(b)
    if (aIndex === -1 && bIndex === -1) return a.localeCompare(b)
    if (aIndex === -1) return 1
    if (bIndex === -1) return -1
    return aIndex - bIndex
  })

  return sortedTypes
})

// Table max height for fixed header
const tableMaxHeight = computed(() => {
  // Calculate available height for table
  const viewportHeight = window.innerHeight
  const headerHeight = 120 // Approximate header height
  const padding = 80 // Padding and margins
  const minHeight = 300
  const maxHeight = 600 // Maximum height to prevent excessive expansion

  const calculatedHeight = viewportHeight - headerHeight - padding
  return Math.min(Math.max(calculatedHeight, minHeight), maxHeight)
})

// Handle window resize for responsive table height
const handleResize = () => {
  // Force table to recalculate height
  if (tableRef.value) {
    tableRef.value.doLayout()
  }
}

// Add resize listener
onMounted(() => {
  window.addEventListener('resize', handleResize)

  // Check initial dark mode state
  checkDarkMode()

  // Create MutationObserver to watch for theme changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        checkDarkMode()
        // Force table to re-render with new styles
        nextTick(() => {
          if (tableRef.value) {
            tableRef.value.doLayout()
          }
        })
      }
    })
  })

  // Start observing document element for class changes
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class'],
  })

  // Store observer for cleanup
  window.themeObserver = observer

  // Watch for dark mode changes and force table update
  watch(isDarkMode, () => {
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value.doLayout()
      }
    })
  })

  // Prevent page scroll on table interactions
  const tableBody = tableRef.value?.$el?.querySelector('.el-table__body-wrapper')
  if (tableBody) {
    tableBody.addEventListener('scroll', (e) => {
      e.stopPropagation()
    })

    tableBody.addEventListener('wheel', (e) => {
      e.stopPropagation()
    })
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // Cleanup theme observer
  if (window.themeObserver) {
    window.themeObserver.disconnect()
    delete window.themeObserver
  }
})

// Methods
const hasGroupPermissions = (group) => {
  if (!group) return false

  // Check if group has permissions or children with permissions
  const hasDirectPermissions = group.permissions && Array.isArray(group.permissions) && group.permissions.length > 0

  const hasChildrenWithPermissions =
    group.children &&
    Array.isArray(group.children) &&
    group.children.some((child) => {
      return child && hasGroupPermissions(child)
    })

  return hasDirectPermissions || hasChildrenWithPermissions
}

const transformDataForTreeTable = () => {
  if (!localPermissions.value || !Array.isArray(localPermissions.value)) {
    tableData.value = []
    return
  }

  const transformGroup = (group) => {
    if (!group || !group.code) {
      return null
    }

    const hasChildren = group.children && Array.isArray(group.children) && group.children.length > 0

    return {
      id: group.code,
      name: group.name || '',
      code: group.code,
      permissions: group.permissions || [],
      hasChildren: hasChildren,
      children: hasChildren ? group.children.map(transformGroup).filter(Boolean) : undefined,
      level: 0,
    }
  }

  // Transform permission_groups to tree table data
  tableData.value = localPermissions.value
    .filter((group) => group && hasGroupPermissions(group))
    .map(transformGroup)
    .filter(Boolean)
}

// Watch for permissions changes
watch(
  () => props.permissions,
  (newPermissions) => {
    // Deep clone permissions to local state
    if (newPermissions && Array.isArray(newPermissions)) {
      localPermissions.value = JSON.parse(JSON.stringify(newPermissions))
    } else {
      localPermissions.value = []
    }
    // Transform data for tree table
    transformDataForTreeTable()
  },
  { immediate: true, deep: true },
)

watch(
  () => props.selectedRole,
  (newRole) => {
    // Reset expanded keys when role changes
    expandedKeys.value = []
    // Transform data for tree table
    transformDataForTreeTable()
  },
  { immediate: true },
)

const loadNode = (row, treeNode, resolve) => {
  // In lazy mode, we need to load children when expanding
  if (row.hasChildren && !row.children) {
    // Find the original group data
    const findGroup = (groups, code) => {
      for (const group of groups) {
        if (group.code === code) return group
        if (group.children) {
          const found = findGroup(group.children, code)
          if (found) return found
        }
      }
      return null
    }

    const originalGroup = findGroup(localPermissions.value, row.code)

    if (originalGroup && originalGroup.children) {
      const children = originalGroup.children
        .filter((child) => hasGroupPermissions(child))
        .map((child) => {
          const hasChildren = child.children && child.children.length > 0
          return {
            id: child.code,
            name: child.name,
            code: child.code,
            permissions: child.permissions || [],
            hasChildren: hasChildren,
            children: undefined, // Don't load grandchildren in lazy mode
            level: row.level + 1,
          }
        })

      resolve(children)
    } else {
      resolve([])
    }
  } else {
    resolve([])
  }
}

const handleExpandChange = (row, expandedRows) => {
  // Ensure expandedRows is an array before calling map
  if (Array.isArray(expandedRows)) {
    expandedKeys.value = expandedRows.map((row) => row.id)
  } else {
    expandedKeys.value = []
  }

  // Prevent page scroll when expanding
  nextTick(() => {
    const tableBody = tableRef.value?.$el?.querySelector('.el-table__body-wrapper')
    if (tableBody) {
      // Ensure focus stays within table
      tableBody.focus()

      // Prevent any page scroll
      const currentScrollY = window.scrollY
      if (window.scrollY !== currentScrollY) {
        window.scrollTo(0, currentScrollY)
      }
    }
  })
}

const getPermissionByType = (row, type) => {
  if (!row || !row.permissions || !Array.isArray(row.permissions)) return null

  const permission = row.permissions.find((p) => p && p.type === type)
  if (!permission) return null

  return {
    ...permission,
    active: permission.active || false,
  }
}

const getPermissionActiveState = (row, type) => {
  if (!row || !type) return false
  const permission = getPermissionByType(row, type)
  return permission ? permission.active : false
}

const updateLocalPermissionState = (permissionId, active) => {
  // Update permission in the local permissions array
  const updatePermissionInGroups = (groups) => {
    for (const group of groups) {
      if (group.permissions) {
        const permission = group.permissions.find((p) => p.id === permissionId)
        if (permission) {
          permission.active = active
          return true
        }
      }

      // Check children groups
      if (group.children && updatePermissionInGroups(group.children)) {
        return true
      }
    }
    return false
  }

  updatePermissionInGroups(localPermissions.value)

  // Also update table data
  const updatePermissionInTableData = (data) => {
    for (const row of data) {
      if (row.permissions) {
        const permission = row.permissions.find((p) => p.id === permissionId)
        if (permission) {
          permission.active = active
          return true
        }
      }

      if (row.children && updatePermissionInTableData(row.children)) {
        return true
      }
    }
    return false
  }

  updatePermissionInTableData(tableData.value)
}

const handlePermissionChange = async (permissionId, checked) => {
  if (!props.selectedRole || !permissionId) return

  // Check if role is protected
  if (isRoleProtected.value) {
    ElMessage.warning('Không thể chỉnh sửa quyền hạn của vai trò được bảo vệ')
    return
  }

  // Add to changing permissions set
  changingPermissions.value.add(permissionId)

  try {
    // Cập nhật local state ngay lập tức để UI responsive
    updateLocalPermissionState(permissionId, checked)

    // Force reactivity update
    await nextTick()

    // Emit event immediately for real-time UI update
    emit('permission-changed')

    // Call API in background (non-blocking)
    if (checked) {
      assignPermissions(props.selectedRole.id, [permissionId])
        .catch((error) => {
          console.error('❌ Background permission assignment failed:', error)
          ElMessage.error('Không thể cập nhật quyền hạn')
          // Revert local state if API call failed
          updateLocalPermissionState(permissionId, !checked)
          emit('permission-changed')
        })
        .finally(() => {
          // Remove from changing permissions set
          changingPermissions.value.delete(permissionId)
        })
    } else {
      revokePermissions(props.selectedRole.id, [permissionId])
        .catch((error) => {
          console.error('❌ Background permission revocation failed:', error)
          ElMessage.error('Không thể cập nhật quyền hạn')
          // Revert local state if API call failed
          updateLocalPermissionState(permissionId, !checked)
          emit('permission-changed')
        })
        .finally(() => {
          // Remove from changing permissions set
          changingPermissions.value.delete(permissionId)
        })
    }
  } catch (error) {
    console.error('❌ Permission change error:', error)
    ElMessage.error('Không thể cập nhật quyền hạn')

    // Revert local state if API call failed
    updateLocalPermissionState(permissionId, !checked)
    await nextTick()
    emit('permission-changed')

    // Remove from changing permissions set
    changingPermissions.value.delete(permissionId)
  }
}

const getPermissionTypeLabel = (type) => {
  const labels = {
    view: 'Xem',
    create: 'Tạo',
    edit: 'Sửa',
    delete: 'Xóa',
    export: 'Xuất',
  }
  return labels[type] || type.charAt(0).toUpperCase() + type.slice(1)
}

const getRowLevel = (row) => {
  // Calculate level based on the row's depth in the tree
  if (!row) return 0
  const level = row.level || 0
  return level
}

const isRowExpanded = (row) => {
  if (!row || !row.id) return false
  return expandedKeys.value.includes(row.id)
}

const toggleRowExpansion = (row) => {
  if (!row || !row.id) {
    return
  }

  if (isRowExpanded(row)) {
    // Collapse
    expandedKeys.value = expandedKeys.value.filter((key) => key !== row.id)
  } else {
    // Expand
    expandedKeys.value.push(row.id)

    // Ensure the expanded row is visible in the table viewport without scrolling the page
    nextTick(() => {
      const tableBody = tableRef.value?.$el?.querySelector('.el-table__body-wrapper')
      if (tableBody) {
        // Find the expanded row element
        const rowElement = tableBody.querySelector(`[data-row-key="${row.id}"]`)
        if (rowElement) {
          // Calculate if the row is visible in the table viewport
          const tableRect = tableBody.getBoundingClientRect()
          const rowRect = rowElement.getBoundingClientRect()

          // If row is below the visible area, scroll within table only
          if (rowRect.bottom > tableRect.bottom) {
            const scrollTop = rowElement.offsetTop - tableBody.offsetHeight / 2
            tableBody.scrollTo({
              top: scrollTop,
              behavior: 'smooth',
            })
          }

          // Prevent page scroll by focusing on table body
          tableBody.focus()
        }
      }
    })
  }
}

// Responsive column width calculation
const getColumnWidth = (permissionType) => {
  // Check if we're on mobile
  const isMobile = window.innerWidth < 768
  const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024

  if (isMobile) {
    return 80 // Smaller width for mobile
  } else if (isTablet) {
    return 100 // Medium width for tablet
  } else {
    return 120 // Full width for desktop
  }
}

// Expose computed properties for parent components
defineExpose({
  activePermissionCount,
  isRoleProtected,
  hasDisabledCheckboxes,
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/users/_permission-table.scss' as *;

/* Force hide default expand icons */
:deep(.el-table__expand-icon) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

:deep(.el-table__expand-icon .el-icon) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

:deep(.el-table__expand-icon .el-icon svg) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

:deep(.el-table__expand-column) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: none !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
  font-size: 0 !important;
  line-height: 0 !important;
}
</style>
