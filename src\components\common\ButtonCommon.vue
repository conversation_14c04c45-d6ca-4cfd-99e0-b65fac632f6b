<template>
  <el-tooltip v-if="shouldShowTooltip" :content="tooltip" :disabled="!tooltip || disabled || loading">
    <button :class="buttonClasses" :disabled="disabled || loading" :type="htmlType" @click="handleClick">
      <!-- Loading icon -->
      <div v-if="loading" class="loading-spinner">
        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="m12 2a10 10 0 0 1 10 10h-4a6 6 0 0 0-6-6z"></path>
        </svg>
      </div>

      <!-- Icon (slot or component) -->
      <component v-else-if="icon && !loading" :is="icon" :class="iconClasses" />
      <slot v-else-if="!loading" name="icon"></slot>

      <!-- Button text -->
      <span v-if="$slots.default || text" :class="textClasses">
        <slot>{{ text }}</slot>
      </span>
    </button>
  </el-tooltip>

  <button v-else :class="buttonClasses" :disabled="disabled || loading" :type="htmlType" @click="handleClick">
    <!-- Loading icon -->
    <div v-if="loading" class="loading-spinner">
      <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="m12 2a10 10 0 0 1 10 10h-4a6 6 0 0 0-6-6z"></path>
      </svg>
    </div>

    <!-- Icon (slot or component) -->
    <component v-else-if="icon && !loading" :is="icon" :class="iconClasses" />
    <slot v-else-if="!loading" name="icon"></slot>

    <!-- Button text -->
    <span v-if="$slots.default || text" :class="textClasses">
      <slot>{{ text }}</slot>
    </span>
  </button>
</template>

<script setup>
import { computed, useSlots } from 'vue'
import { ElTooltip } from 'element-plus'

const slots = useSlots()

const props = defineProps({
  // Button variants
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info', 'text'].includes(value),
  },

  // Button sizes
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value),
  },

  // Button styles
  variant: {
    type: String,
    default: 'solid',
    validator: (value) => ['solid', 'outline', 'ghost', 'gradient'].includes(value),
  },

  // States
  disabled: {
    type: Boolean,
    default: false,
  },

  loading: {
    type: Boolean,
    default: false,
  },

  // Content
  text: {
    type: String,
    default: '',
  },

  icon: {
    type: [String, Object],
    default: null,
  },

  iconPosition: {
    type: String,
    default: 'left',
    validator: (value) => ['left', 'right'].includes(value),
  },

  // Tooltip
  tooltip: {
    type: String,
    default: '',
  },

  // HTML attributes
  htmlType: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value),
  },

  // Custom styling
  rounded: {
    type: Boolean,
    default: false,
  },

  block: {
    type: Boolean,
    default: false,
  },

  shadow: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['click'])

// Determine if tooltip should be shown
const shouldShowTooltip = computed(() => {
  // Show tooltip if:
  // 1. Tooltip text is provided AND
  // 2. Button has icon but no text content (icon-only button)
  return props.tooltip && (props.icon || slots.icon) && !props.text && !slots.default
})

// Base button classes
const baseClasses = computed(() => [
  // Base styling
  'inline-flex items-center justify-center',
  'font-medium transition-all duration-200 ease-in-out',
  'border focus:outline-none',
  'overflow-hidden',

  // Size classes
  {
    'px-2 py-1 text-xs min-h-[24px]': props.size === 'small',
    'px-3 py-2 text-sm min-h-[32px]': props.size === 'medium',
    'px-4 py-3 text-base min-h-[40px]': props.size === 'large',
  },

  // Shape classes
  {
    rounded: !props.rounded,
    'rounded-full': props.rounded,
  },

  // Block vs inline
  {
    'w-full': props.block,
    'w-auto': !props.block,
  },

  // States
  {
    'cursor-not-allowed opacity-50': props.disabled || props.loading,
    'cursor-pointer': !props.disabled && !props.loading,
  },
])

// Type-specific classes
const typeClasses = computed(() => {
  // Special styling for small info and danger buttons (like ActionButtons)
  if (props.size === 'small') {
    if (props.type === 'info') {
      return 'bg-[#409eff] text-white border-[#409eff] hover:bg-[#66b1ff] hover:border-[#66b1ff] active:bg-[#337ecc] active:border-[#337ecc]'
    }
    if (props.type === 'danger') {
      return 'bg-[#f56c6c] text-white border-[#f56c6c] hover:bg-[#f78989] hover:border-[#f78989] active:bg-[#c45656] active:border-[#c45656]'
    }
  }

  const baseColors = {
    default: {
      solid:
        'bg-gradient-to-br from-gray-50 to-gray-100 text-gray-700 border-gray-200 hover:from-gray-100 hover:to-gray-200 hover:border-gray-300',
      outline:
        'bg-transparent text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400',
      ghost:
        'bg-transparent text-gray-700 border-transparent hover:bg-gray-50 hover:border-gray-200',
      gradient:
        'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 border-gray-300 hover:from-gray-200 hover:to-gray-300',
    },
    primary: {
      solid:
        'bg-[#409eff] text-white border-[#409eff] hover:bg-[#66b1ff] hover:border-[#66b1ff] active:bg-[#3a8ee6] active:border-[#3a8ee6]',
      outline:
        'bg-transparent text-[#409eff] border-[#409eff] hover:bg-[#409eff] hover:text-white hover:border-[#409eff]',
      ghost:
        'bg-transparent text-[#409eff] border-transparent hover:bg-[#ecf5ff] hover:border-[#409eff]',
      gradient:
        'bg-[#409eff] text-white border-[#409eff] hover:bg-[#66b1ff] hover:border-[#66b1ff]',
    },
    success: {
      solid:
        'bg-gradient-to-br from-green-500 to-green-600 text-white border-green-600 hover:from-green-600 hover:to-green-700 hover:border-green-700',
      outline:
        'bg-transparent text-green-600 border-green-600 hover:bg-green-50 hover:border-green-700',
      ghost:
        'bg-transparent text-green-600 border-transparent hover:bg-green-50 hover:border-green-200',
      gradient:
        'bg-gradient-to-br from-green-400 to-green-600 text-white border-green-500 hover:from-green-500 hover:to-green-700',
    },
    warning: {
      solid:
        'bg-gradient-to-br from-amber-500 to-amber-600 text-white border-amber-600 hover:from-amber-600 hover:to-amber-700 hover:border-amber-700',
      outline:
        'bg-transparent text-amber-600 border-amber-600 hover:bg-amber-50 hover:border-amber-700',
      ghost:
        'bg-transparent text-amber-600 border-transparent hover:bg-amber-50 hover:border-amber-200',
      gradient:
        'bg-gradient-to-br from-amber-400 to-amber-600 text-white border-amber-500 hover:from-amber-500 hover:to-amber-700',
    },
    danger: {
      solid:
        'bg-gradient-to-br from-red-500 to-red-600 text-white border-red-600 hover:from-red-600 hover:to-red-700 hover:border-red-700',
      outline: 'bg-transparent text-red-600 border-red-600 hover:bg-red-50 hover:border-red-700',
      ghost: 'bg-transparent text-red-600 border-transparent hover:bg-red-50 hover:border-red-200',
      gradient:
        'bg-gradient-to-br from-red-400 to-red-600 text-white border-red-500 hover:from-red-500 hover:to-red-700',
    },
    info: {
      solid:
        'bg-gradient-to-br from-indigo-500 to-indigo-600 text-white border-indigo-600 hover:from-indigo-600 hover:to-indigo-700 hover:border-indigo-700',
      outline:
        'bg-transparent text-indigo-600 border-indigo-600 hover:bg-indigo-50 hover:border-indigo-700',
      ghost:
        'bg-transparent text-indigo-600 border-transparent hover:bg-indigo-50 hover:border-indigo-200',
      gradient:
        'bg-gradient-to-br from-indigo-400 to-indigo-600 text-white border-indigo-500 hover:from-indigo-500 hover:to-indigo-700',
    },
    text: {
      solid:
        'bg-transparent text-gray-600 border-transparent hover:bg-gray-100 hover:text-gray-800',
      outline:
        'bg-transparent text-gray-600 border-transparent hover:bg-gray-100 hover:text-gray-800',
      ghost:
        'bg-transparent text-gray-600 border-transparent hover:bg-gray-100 hover:text-gray-800',
      gradient:
        'bg-transparent text-gray-600 border-transparent hover:bg-gray-100 hover:text-gray-800',
    },
  }

  return baseColors[props.type]?.[props.variant] || baseColors.default.solid
})

// Shadow classes
const shadowClasses = computed(() => {
  if (!props.shadow || props.disabled || props.loading) return ''

  const shadows = {
    default: 'shadow-sm hover:shadow-md',
    primary: 'shadow-sm hover:shadow-md',
    success: 'shadow-sm hover:shadow-md',
    warning: 'shadow-sm hover:shadow-md',
    danger: 'shadow-sm hover:shadow-md',
    info: 'shadow-sm hover:shadow-md',
    text: '',
  }

  return shadows[props.type] || shadows.default
})

// Dark mode classes - keep same colors as light mode
const darkModeClasses = computed(() => {
  // For small info and danger buttons, keep the same colors as light mode
  if (props.size === 'small') {
    if (props.type === 'info') {
      return '' // Use the same colors as defined in typeClasses
    }
    if (props.type === 'danger') {
      return '' // Use the same colors as defined in typeClasses
    }
  }

  // For other cases, only adjust text colors for better readability in dark mode
  const darkColors = {
    default: '', // Keep same colors as light mode
    primary: '', // Keep same colors as light mode
    success: '', // Keep same colors as light mode
    warning: '', // Keep same colors as light mode
    danger: '', // Keep same colors as light mode
    info: '', // Keep same colors as light mode
    text: 'dark:text-gray-300 dark:hover:text-gray-100', // Only adjust text color for text variant
  }

  return darkColors[props.type] || darkColors.default
})

// Computed final classes
const buttonClasses = computed(() => [
  ...baseClasses.value,
  typeClasses.value,
  shadowClasses.value,
  darkModeClasses.value,
])

// Icon classes
const iconClasses = computed(() => [
  {
    'h-3 w-3': props.size === 'small',
    'h-4 w-4': props.size === 'medium',
    'h-5 w-5': props.size === 'large',
  },
  {
    'order-2 ml-1': props.iconPosition === 'right',
    'mr-1': props.iconPosition === 'left' && props.text,
  },
])

// Text classes for spacing when icon is present
const textClasses = computed(() => [
  {
    'order-1': props.iconPosition === 'right',
  },
])

// Click handler
const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Focus ring customization - removed outline and ring */
button:focus,
button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

.dark button:focus,
.dark button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

/* Disabled state improvements */
button:disabled {
  cursor: not-allowed;
}

/* Animation improvements */
button {
  transform-origin: center;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}
</style>
