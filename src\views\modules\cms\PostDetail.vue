<template>
  <div class="post-detail-wrapper">
    <PageBreadcrumb
      :page-title="currentPageTitle"
      :breadcrumbs="[
        { label: 'Quản lý CMS', to: '/cms' },
        { label: 'Quản lý bài viết', to: '/cms/posts' },
      ]"
    />

    <!-- Action Buttons - Only show in admin view -->
    <div
      v-if="isAdminView"
      class="mb-6 flex items-center justify-between rounded-xl border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900"
    >
      <el-button @click="$router.push('/cms/posts')" class="flex items-center gap-2">
        <ArrowLeft class="h-4 w-4" />
        Quay lại
      </el-button>
      <div class="flex items-center gap-3" v-if="currentPost">
        <el-button @click="$router.push(`/cms/posts/edit/${currentPost.id}`)" type="primary">
          <Edit class="mr-2 h-4 w-4" />
          Chỉnh sửa
        </el-button>
        <el-button type="danger" @click="handleDelete">
          <TrashIcon class="mr-2 h-4 w-4" />
          Xóa
        </el-button>
      </div>
    </div>

    <!-- Content -->
    <div v-loading="loading">
      <div v-if="currentPost" class="space-y-6">
        <!-- Header Section -->
        <div class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="mb-4 flex items-center gap-3">
                <el-tag :type="getStatusType(currentPost.status)" size="large">
                  {{ getStatusLabel(currentPost.status) }}
                </el-tag>
                <el-tag v-if="currentPost.is_hot" type="danger" size="large">🔥 Hot</el-tag>
                <el-tag v-if="currentPost.show_on_homepage" type="success" size="large">🏠 Trang chủ</el-tag>
                <el-tag v-if="currentPost.is_published" type="warning" size="large">📄 Đã xuất bản</el-tag>
              </div>
              
              <h1 class="mb-4 text-3xl font-bold text-gray-900 dark:text-white">
                {{ currentPost.title }}
              </h1>
              
              <div class="mb-4 flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <div class="flex items-center gap-2">
                  <UserCircleIcon class="h-4 w-4" />
                  <span>{{ currentPost.created_by?.name || '—' }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <FolderIcon class="h-4 w-4" />
                  <span>{{ currentPost.category?.name || 'Chưa phân loại' }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <CalenderIcon class="h-4 w-4" />
                  <span>{{ formatDate(currentPost.created_at) }}</span>
                </div>
                <div class="flex items-center gap-2" v-if="currentPost.reading_time">
                  <span>📖 {{ currentPost.reading_time }} phút đọc</span>
                </div>
              </div>
              
              <div class="text-sm text-blue-600 dark:text-blue-400">
                <strong>Slug:</strong> {{ currentPost.slug }}
              </div>
            </div>
            
            <!-- Cover Image -->
            <div v-if="currentPost.cover_image" class="ml-6 flex-shrink-0">
              <div class="h-32 w-32 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                <img
                  :src="getImageUrl(currentPost.cover_image)"
                  :alt="currentPost.title"
                  class="h-full w-full object-cover"
                  @error="handleImageError"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <!-- Main Content (2/3) -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Excerpt -->
            <div v-if="currentPost.excerpt" class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
              <h3 class="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                📋 Tóm tắt
              </h3>
              <div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
                <p class="text-gray-700 dark:text-gray-300">
                  {{ currentPost.excerpt }}
                </p>
              </div>
            </div>

            <!-- Content -->
            <div class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
              <h3 class="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                📄 Nội dung
              </h3>
              <div class="prose prose-lg dark:prose-invert max-w-none">
                <div
                  class="content-body leading-relaxed text-gray-700 dark:text-gray-300"
                  v-html="currentPost.body"
                ></div>
              </div>
            </div>

            <!-- SEO Information - Only show in admin view -->
            <div
              v-if="hasSeoInfo && isAdminView"
              class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900"
            >
              <h3 class="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                🔍 Thông tin SEO
              </h3>
              <div class="space-y-4">
                <div v-if="currentPost.meta_title">
                  <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Title</label>
                  <div class="mt-1 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800">
                    <p class="text-gray-900 dark:text-white">{{ currentPost.meta_title }}</p>
                  </div>
                </div>
                <div v-if="currentPost.meta_description">
                  <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Description</label>
                  <div class="mt-1 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800">
                    <p class="text-gray-900 dark:text-white">{{ currentPost.meta_description }}</p>
                  </div>
                </div>
                <div v-if="currentPost.meta_keywords">
                  <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Keywords</label>
                  <div class="mt-1 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800">
                    <p class="text-gray-900 dark:text-white">{{ currentPost.meta_keywords }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar (1/3) -->
          <div class="space-y-6">
            <!-- Statistics -->
            <div class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
              <h3 class="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                📊 Thống kê
              </h3>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Trạng thái</span>
                  <el-tag :type="getStatusType(currentPost.status)" size="small">
                    {{ getStatusLabel(currentPost.status) }}
                  </el-tag>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Tin hot</span>
                  <el-tag :type="currentPost.is_hot ? 'danger' : 'info'" size="small">
                    {{ currentPost.is_hot ? 'Có' : 'Không' }}
                  </el-tag>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Hiển thị trang chủ</span>
                  <el-tag :type="currentPost.show_on_homepage ? 'success' : 'info'" size="small">
                    {{ currentPost.show_on_homepage ? 'Có' : 'Không' }}
                  </el-tag>
                </div>
                <div class="flex items-center justify-between" v-if="currentPost.reading_time">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Thời gian đọc</span>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">{{ currentPost.reading_time }} phút</span>
                </div>
              </div>
            </div>

            <!-- Author Information - Only show in admin view -->
            <div
              v-if="isAdminView"
              class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900"
            >
              <h3 class="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                👥 Tác giả
              </h3>
              <div class="space-y-4">
                <div>
                  <div class="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Người tạo</div>
                  <div class="flex items-center gap-2">
                    <UserCircleIcon class="h-4 w-4 text-gray-500" />
                    <span class="text-gray-900 dark:text-white">{{ currentPost.created_by?.name || '—' }}</span>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">{{ currentPost.created_by?.email || '—' }}</div>
                </div>
                <div v-if="currentPost.updated_by && currentPost.updated_by.id !== currentPost.created_by?.id">
                  <div class="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Người cập nhật</div>
                  <div class="flex items-center gap-2">
                    <UserCircleIcon class="h-4 w-4 text-gray-500" />
                    <span class="text-gray-900 dark:text-white">{{ currentPost.updated_by.name }}</span>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">{{ currentPost.updated_by.email }}</div>
                </div>
              </div>
            </div>

            <!-- Timeline -->
            <div class="rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
              <h3 class="mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                ⏰ Thời gian
              </h3>
              <div class="space-y-3">
                <div>
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Ngày tạo</div>
                  <div class="text-sm text-gray-900 dark:text-white">{{ formatDate(currentPost.created_at) }}</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Cập nhật lần cuối</div>
                  <div class="text-sm text-gray-900 dark:text-white">{{ formatDate(currentPost.updated_at) }}</div>
                </div>
                <div v-if="currentPost.published_at">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Ngày xuất bản</div>
                  <div class="text-sm text-gray-900 dark:text-white">
                    {{ formatDate(currentPost.published_at) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, Edit, Delete, User, Folder, Calendar, Clock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { usePostDetail } from '@/composables/modules/cms/usePostDetail.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import { UserCircleIcon, FolderIcon, CalenderIcon, TrashIcon } from '@/components/icons/index.js'

// Page title
const currentPageTitle = ref('Chi tiết bài viết')

// Router
const router = useRouter()
const route = useRoute()

// Composables
const {
  loading,
  currentPost,
  hasSeoInfo,
  loadPost,
  handleDelete: deletePostAction,
  getImageUrl,
  handleImageError,
  formatDate,
  getStatusLabel,
  getStatusType,
} = usePostDetail()

// Computed
const isAdminView = computed(() => {
  return route.name === 'cms-posts-detail'
})

// Methods
const handleDelete = async () => {
  const result = await deletePostAction()

  if (result === true) {
    router.push('/cms/posts')
  }
}

// Initialize
onMounted(async () => {
  // Admin route: /cms/posts/:id
  if (route.params.id) {
    await loadPost(route.params.id)
  }
})
</script>

<style lang="scss">
@use '@/assets/styles/modules/cms/post-detail';

.post-detail-wrapper {
  .content-body {
    img {
      @apply max-w-full h-auto rounded-lg;
    }
    
    h1, h2, h3, h4, h5, h6 {
      @apply font-semibold text-gray-900 dark:text-white;
    }
    
    h1 { @apply text-2xl mb-4; }
    h2 { @apply text-xl mb-3; }
    h3 { @apply text-lg mb-2; }
    
    p {
      @apply mb-4 leading-relaxed;
    }
    
    ul, ol {
      @apply mb-4 pl-6;
    }
    
    li {
      @apply mb-1;
    }
    
    blockquote {
      @apply border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 p-4 my-4 italic;
    }
    
    code {
      @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm;
    }
    
    pre {
      @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto;
    }
  }
}
</style>
