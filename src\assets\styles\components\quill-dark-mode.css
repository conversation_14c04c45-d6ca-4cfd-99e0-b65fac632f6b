/* Quill Editor Dark Mode Styles */
/* <PERSON><PERSON> dụng CSS thuần để override Quill Editor trong dark mode */

.dark .ql-editor {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

.dark .ql-editor p {
  color: #f1f5f9 !important;
}

.dark .ql-editor h1,
.dark .ql-editor h2,
.dark .ql-editor h3,
.dark .ql-editor h4,
.dark .ql-editor h5,
.dark .ql-editor h6 {
  color: #f1f5f9 !important;
}

.dark .ql-editor strong {
  color: #f1f5f9 !important;
}

.dark .ql-editor em {
  color: #f1f5f9 !important;
}

.dark .ql-editor u {
  color: #f1f5f9 !important;
}

.dark .ql-editor ol,
.dark .ql-editor ul {
  color: #f1f5f9 !important;
}

.dark .ql-editor li {
  color: #f1f5f9 !important;
}

.dark .ql-editor blockquote {
  color: #f1f5f9 !important;
  border-left-color: #64748b !important;
}

.dark .ql-editor.ql-blank::before {
  color: #94a3b8 !important;
  font-style: italic !important;
}

.dark .ql-toolbar {
  background-color: #334155 !important;
  border-bottom-color: #475569 !important;
}

.dark .ql-toolbar .ql-stroke {
  stroke: #e2e8f0 !important;
}

.dark .ql-toolbar .ql-fill {
  fill: #e2e8f0 !important;
}

.dark .ql-toolbar .ql-picker-label {
  color: #e2e8f0 !important;
}

.dark .ql-toolbar .ql-picker-options {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .ql-toolbar .ql-picker-item {
  color: #e2e8f0 !important;
}

.dark .ql-toolbar .ql-picker-item:hover {
  background-color: #475569 !important;
}

.dark .ql-container {
  border-color: #475569 !important;
}

/* Tooltip dark mode */
.dark .ql-tooltip {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

.dark .ql-tooltip input {
  background-color: #1e293b !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark .ql-tooltip a {
  color: #60a5fa !important;
}
