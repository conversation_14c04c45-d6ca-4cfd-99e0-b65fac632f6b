import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as yup from 'yup'
import { validate, DEFAULT_MESSAGES } from '@/utils/helpers/validate.helper.js'
import { useAuthStore } from '@/state/index.js'
import { useAuthUser } from '@/composables/useAuthUser.js'
import authApi from '@/utils/apis/auth.api.js'
import { storeToRefs } from 'pinia'

// Form field names - unified with backend
export const FORM_FIELDS = {
  NAME: 'name',
  EMAIL: 'email',
  AVATAR_URL: 'avatar',
}

// Validation schema
const profileSchema = yup.object().shape({
  [FORM_FIELDS.NAME]: yup
    .string()
    .required(DEFAULT_MESSAGES.nameRequired)
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
    .max(100, 'Họ và tên không được vượt quá 100 ký tự'),
})

// Label mapping for error messages
const labelMap = {
  [FORM_FIELDS.NAME]: 'Họ và tên',
}

/**
 * Composable for profile update functionality
 * Handles form state, validation, file upload, and API calls
 */
export const useUpdateProfile = () => {
  // Store
  const authStore = useAuthStore()
  const { authUser } = storeToRefs(authStore)
  const { handleSetAuthUser } = useAuthUser()

  // Loading states
  const loading = ref(false)
  const uploadLoading = ref(false)

  // Form data - unified field names
  const formData = reactive({
    [FORM_FIELDS.NAME]: '',
    [FORM_FIELDS.EMAIL]: '',
    [FORM_FIELDS.AVATAR_URL]: '',
  })

  // Form errors
  const errors = reactive({
    [FORM_FIELDS.NAME]: '',
    [FORM_FIELDS.EMAIL]: '',
    [FORM_FIELDS.AVATAR_URL]: '',
  })

  // File upload state
  const selectedFile = ref(null)
  const avatarPreview = ref('')

  // Computed properties
  const canSubmit = computed(() => {
    return formData[FORM_FIELDS.NAME].trim() !== '' && !loading.value && !uploadLoading.value
  })

  const hasChanges = computed(() => {
    if (!authUser.value) return false

    return formData[FORM_FIELDS.NAME] !== authUser.value.name || selectedFile.value !== null
  })

  // Utility functions
  const isValidUrl = (string) => {
    try {
      new URL(string)
      return true
    } catch {
      return false
    }
  }

  const getInitials = (name) => {
    if (!name) return ''
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // File validation
  const validateFile = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
      ElMessage.error('Chỉ chấp nhận file ảnh!')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('Kích thước ảnh không được vượt quá 2MB!')
      return false
    }
    return true
  }

  // Clear all errors
  const clearErrors = () => {
    Object.keys(errors).forEach((key) => {
      errors[key] = ''
    })
  }

  // Initialize form with current user data
  const initializeForm = () => {
    if (authUser.value) {
      formData[FORM_FIELDS.NAME] = authUser.value.name || ''
      formData[FORM_FIELDS.EMAIL] = authUser.value.email || ''
      formData[FORM_FIELDS.AVATAR_URL] = authUser.value.avatar || ''

      // Also initialize avatarPreview with current avatar URL
      avatarPreview.value = authUser.value.avatar || ''
    }
  }

  // Reset form to initial state
  const resetForm = () => {
    clearErrors()
    selectedFile.value = null
    // Call initializeForm which will set avatarPreview correctly
    initializeForm()
  }

  // Handle avatar file change
  const handleAvatarChange = (file) => {
    if (!file?.raw) {
      ElMessage.error('Không thể đọc file!')
      return false
    }

    if (!validateFile(file.raw)) {
      return false
    }

    selectedFile.value = file.raw

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      avatarPreview.value = e.target.result
    }
    reader.onerror = () => {
      ElMessage.error('Không thể đọc file ảnh!')
    }
    reader.readAsDataURL(file.raw)

    return false
  }

  // Remove avatar
  const removeAvatar = () => {
    selectedFile.value = null
    avatarPreview.value = ''
  }

  // Validate form
  const validateForm = async () => {
    clearErrors()

    const { errors: validateErrors } = await validate(profileSchema, formData, labelMap)

    if (Object.keys(validateErrors).length > 0) {
      Object.assign(errors, validateErrors)
      return false
    }

    return true
  }

  // Submit form
  const submitForm = async () => {
    // Validate form
    if (!(await validateForm())) {
      return { success: false, message: 'Dữ liệu không hợp lệ' }
    }

    // Check if there are changes
    if (!hasChanges.value) {
      ElMessage.info('Không có thay đổi nào để cập nhật')
      return { success: false, message: 'Không có thay đổi' }
    }

    loading.value = true

    try {
      // Prepare FormData
      const formDataToSend = new FormData()
      formDataToSend.append(FORM_FIELDS.NAME, formData[FORM_FIELDS.NAME])

      if (selectedFile.value) {
        formDataToSend.append('avatar', selectedFile.value)
      }

      // Update profile
      const response = await authApi.updateProfile(formDataToSend)

      // Fetch fresh user data and update store
      try {
        const userResponse = await authApi.getAuthUser()

        // Handle different response structures
        let userData
        if (userResponse.data && userResponse.data.data) {
          userData = userResponse.data.data
        } else if (userResponse.data) {
          userData = userResponse.data
        } else {
          userData = userResponse
        }

        // Update store
        handleSetAuthUser(userData)
      } catch (fetchError) {
        console.error('Error fetching updated user:', fetchError)
        // Fallback: update with form data
        const fallbackUser = {
          ...authUser.value,
          name: formData[FORM_FIELDS.NAME],
          avatar: selectedFile.value ? avatarPreview.value : authUser.value?.avatar,
        }
        handleSetAuthUser(fallbackUser)
      }

      ElMessage.success('Cập nhật hồ sơ thành công!')
      return { success: true, message: 'Cập nhật thành công' }
    } catch (error) {
      console.error('Update profile error:', error)

      // Handle validation errors from server
      if (error.status === 422 && error.data?.errors) {
        // Map server errors to form errors
        Object.keys(error.data.errors).forEach((field) => {
          if (Object.prototype.hasOwnProperty.call(errors, field)) {
            errors[field] = error.data.errors[field][0]
          }
        })

        return { success: false, message: 'Dữ liệu không hợp lệ' }
      }

      // Handle other errors
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật hồ sơ!'
      ElMessage.error(errorMessage)
      return { success: false, message: errorMessage }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    uploadLoading,
    formData,
    errors,
    selectedFile,
    avatarPreview,

    // Computed
    canSubmit,
    hasChanges,

    // Methods
    initializeForm,
    resetForm,
    clearErrors,
    handleAvatarChange,
    removeAvatar,
    validateForm,
    submitForm,

    // Utilities
    isValidUrl,
    getInitials,
    validateFile,

    // Constants
    FORM_FIELDS,
  }
}
