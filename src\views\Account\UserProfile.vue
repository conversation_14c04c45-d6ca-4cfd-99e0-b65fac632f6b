<template>
  <div class="profile-wrapper">
    <PageBreadcrumb :pageTitle="currentPageTitle" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <profile-card />
      <personal-info-card @change-password="showChangePasswordModal = true" />
    </div>

    <!-- Change Password Modal -->
    <ChangePasswordModal v-model="showChangePasswordModal" @close="showChangePasswordModal = false" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ProfileCard from '@/components/pages/profile/ProfileCard.vue'
import PersonalInfoCard from '@/components/pages/profile/PersonalInfoCard.vue'
import ChangePasswordModal from '@/components/pages/profile/ChangePasswordModal.vue'

const currentPageTitle = ref('Thông Tin Cá Nhân')
const showChangePasswordModal = ref(false)
</script>
