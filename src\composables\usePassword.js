import { reactive } from 'vue'

export function usePassword() {
  // Password visibility states
  const passwordVisibility = reactive({
    current: false,
    new: false,
    confirm: false,
  })

  // Toggle password visibility
  const toggleVisibility = (field) => {
    passwordVisibility[field] = !passwordVisibility[field]
  }

  // Reset all states
  const reset = () => {
    passwordVisibility.current = false
    passwordVisibility.new = false
    passwordVisibility.confirm = false
  }

  return {
    // States
    passwordVisibility,

    // Methods
    toggleVisibility,
    reset,
  }
}
