<template>
  <div class="user-form-example">
    <h2>User Form with Avatar Upload Example</h2>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Avatar Upload -->
      <div>
        <h3>Basic Avatar Upload</h3>
        <AvatarUpload
          v-model="userForm.avatar"
          :name="userForm.name"
          @change="handleAvatarChange"
          @remove="handleAvatarRemove"
          @error="handleAvatarError"
        />
      </div>

      <!-- Advanced Avatar Upload with all features -->
      <div>
        <h3>Advanced Avatar Upload</h3>
        <AvatarUpload
          v-model="userForm.profilePicture"
          :name="userForm.name"
          size="large"
          :max-size="2 * 1024 * 1024"
          :allowed-types="['image/jpeg', 'image/png']"
          :validate-file="customValidateFile"
          :show-file-info="true"
          :allow-drag="true"
          accept="image/jpeg,image/png"
          @change="handleProfilePictureChange"
          @remove="handleProfilePictureRemove"
          @error="handleError"
        />
      </div>

      <!-- User Info -->
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField label="Tên người dùng" required>
          <el-input v-model="userForm.name" placeholder="Nhập tên người dùng" />
        </FormField>

        <FormField label="Email" required>
          <el-input v-model="userForm.email" type="email" placeholder="Nhập email" />
        </FormField>
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end space-x-3">
        <el-button @click="resetForm">Reset</el-button>
        <el-button type="primary" native-type="submit" :loading="loading"> Lưu thông tin </el-button>
      </div>
    </form>

    <!-- Debug Info -->
    <div class="mt-8 rounded-lg bg-gray-100 p-4 dark:bg-gray-800">
      <h4>Form Data (Debug):</h4>
      <pre class="text-sm">{{ JSON.stringify(userForm, null, 2) }}</pre>

      <h4 class="mt-4">File Info:</h4>
      <pre class="text-sm">{{ JSON.stringify(fileInfo, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElInput, ElButton, ElMessage } from 'element-plus'
import AvatarUpload from '@/components/common/AvatarUpload.vue'
import FormField from '@/components/common/FormField.vue'

// Form state
const userForm = reactive({
  name: 'John Doe',
  email: '<EMAIL>',
  avatar: '',
  profilePicture: '',
})

const fileInfo = ref({
  avatar: null,
  profilePicture: null,
})

const loading = ref(false)

// Avatar handlers
const handleAvatarChange = (fileData) => {
  fileInfo.value.avatar = fileData
  ElMessage.success('Avatar đã được cập nhật')
}

const handleAvatarRemove = () => {
  fileInfo.value.avatar = null
  ElMessage.info('Avatar đã được xóa')
}

const handleAvatarError = (error) => {
  ElMessage.error(error)
}

// Profile picture handlers
const handleProfilePictureChange = (fileData) => {
  fileInfo.value.profilePicture = fileData
  ElMessage.success('Ảnh đại diện đã được cập nhật')
}

const handleProfilePictureRemove = () => {
  fileInfo.value.profilePicture = null
  ElMessage.info('Ảnh đại diện đã được xóa')
}

const handleError = (error) => {
  console.error('Upload error:', error)
  ElMessage.error(error)
}

// Custom validation function
const customValidateFile = (file) => {
  // Custom validation logic
  if (file.size > 2 * 1024 * 1024) {
    return 'File không được vượt quá 2MB'
  }

  // Check image dimensions (example)
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      if (img.width < 200 || img.height < 200) {
        resolve('Ảnh phải có kích thước tối thiểu 200x200px')
      } else if (img.width > 2000 || img.height > 2000) {
        resolve('Ảnh không được vượt quá 2000x2000px')
      } else {
        resolve(true)
      }
    }
    img.src = URL.createObjectURL(file)
  })
}

// Form handlers
const handleSubmit = async () => {
  loading.value = true

  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success('Thông tin đã được lưu thành công!')
  } catch (error) {
    ElMessage.error('Có lỗi xảy ra khi lưu thông tin')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  userForm.name = ''
  userForm.email = ''
  userForm.avatar = ''
  userForm.profilePicture = ''
  fileInfo.value = {
    avatar: null,
    profilePicture: null,
  }
  ElMessage.info('Form đã được reset')
}
</script>

<style scoped>
.user-form-example {
  @apply mx-auto max-w-4xl p-6;
}

h2 {
  @apply mb-6 text-2xl font-bold text-gray-900 dark:text-gray-100;
}

h3 {
  @apply mb-4 text-lg font-semibold text-gray-800 dark:text-gray-200;
}

h4 {
  @apply mb-2 text-base font-medium text-gray-700 dark:text-gray-300;
}

pre {
  @apply overflow-x-auto;
}
</style>
