import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), vueJsx(), vueDevTools()],

  // Cấu hình server của bạn
  server: {
    host: '0.0.0.0',
    port: parseInt(process.env.ADMIN_UI_PORT || '5173'),
    open: false,
    historyFallback: true,
    watch: {
      usePolling: true,
    },
  },

  // C<PERSON>u hình alias đường dẫn của bạn
  resolve: {
    alias: {
      '~': fileURLToPath(new URL('./src', import.meta.url)),
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },

  // <PERSON>ấ<PERSON> hình build để tối ưu kích thước bundle
  build: {
    // Tăng giới hạn cảnh báo chunk size
    chunkSizeWarningLimit: 1000,

    rollupOptions: {
      output: {
        // Cấu hình manual chunks để tách code hiệu quả
        manualChunks: {
          // Tách Vue framework
          'vue-vendor': ['vue', 'vue-router', 'pinia'],

          // Tách Element Plus UI library
          'element-plus': ['element-plus', '@element-plus/icons-vue'],

          // Tách các thư viện utility
          utils: ['axios', 'lodash', 'yup', 'dompurify'],

          // Tách editor và các component đặc biệt
          editor: ['@vueup/vue-quill'],

          // Tách các thư viện khác
          misc: ['vue-draggable-next', 'vue-inline-svg'],
        },

        // Cấu hình tên file output
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            return 'assets/[name]-[hash].js'
          }
          return 'assets/chunk-[hash].js'
        },
      },
    },

    // Tối ưu minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
})
