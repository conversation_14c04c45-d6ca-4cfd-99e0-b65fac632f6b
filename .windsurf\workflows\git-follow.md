---
description: SaboGame Git Follow
---

# 🌿 Git Workflow

## 1. <PERSON><PERSON><PERSON><PERSON>

- **`main`**:
  - <PERSON><PERSON><PERSON><PERSON> chứa mã nguồn production ổn định.
  - **KHÔNG** đư<PERSON><PERSON> merge trực tiếp vào nh<PERSON> này (ngoại trừ merge từ nhánh `release` hoặc `hotfix`).
- **`develop`**:
  - <PERSON><PERSON><PERSON><PERSON> ch<PERSON>h để tích hợp các tính năng/sửa lỗi đã hoàn thành.
  - Là nhánh nguồn để tạo các nhánh `feature`/`fix`.
  - Nên luôn ở trạng thái ổn định tương đối.

---

## 2. <PERSON>uy Tắc Tạo Nhánh Làm Việc

### Các Loại <PERSON> & C<PERSON>u Trúc Tên Nhánh:

- **Feature Branch:**
  - Mục đích: Phát triển tính năng mới.
  - Nguồn: Checkout từ `develop`.
  - C<PERSON>u trúc: `feature/{prefix-cố-định}/{tên-task-ngắn-gọn}`
  - <PERSON><PERSON> dụ: `feature/phuong/login-api`
- **Fix Branch:**
  - Mục đích: Sửa lỗi không khẩn cấp (lỗi trên `develop` hoặc trong quá trình phát triển).
  - Nguồn: Checkout từ `develop`.
  - Cấu trúc: `fix/{prefix-cố-định}/{tên-task-ngắn-gọn}`
  - Ví dụ: `fix/long/500-error-login`
- **Release Branch:** _(Dùng cho quy trình release cherry-pick)_
  - Mục đích: Chuẩn bị cho bản release, chứa các commit được chọn lọc.
  - Nguồn: Checkout từ `main`.
  - Cấu trúc: `release/{tên-release}`
  - Ví dụ: `release/v1.1.0`
- **Hotfix Branch:** _(Dùng cho sửa lỗi khẩn cấp trên production)_
  - Mục đích: Sửa lỗi khẩn cấp trên `main`.
  - Nguồn: Checkout từ `main`.
  - Cấu trúc: `hotfix/{prefix-cố-định}/{tên-lỗi-ngắn-gọn}`
  - Ví dụ: `hotfix/hoa/critical-payment-bug`

### Quy Ước Prefix Cố Định Từng Thành Viên:

| Thành viên                         | Prefix   |
| :--------------------------------- | :------- |
| Phương                             | `phuong` |
| Long                               | `long`   |
| Hoa                                | `hoa`    |
| _...(Thêm các thành viên khác)..._ |          |

> **Lưu ý:**
>
> - Các thành viên tự đặt prefix riêng cho mình theo tên (hoặc nickname ngắn gọn, không dấu).
> - Prefix đặt 1 lần duy nhất → dùng xuyên suốt toàn bộ project.
> - Không tự ý đổi prefix.
> - `{tên-task-ngắn-gọn}` hoặc `{tên-lỗi-ngắn-gọn}`: Viết rõ ràng, súc tích, không dấu, lowercase, các từ nối với nhau bằng dấu gạch ngang (`-`).

---

## 3. Quy Tắc Commit & Push

1.  **Cập Nhật Nhánh:** Trước khi chuẩn bị commit cuối hoặc tạo PR, hãy cập nhật nhánh của bạn với `develop` mới nhất bằng `rebase`.
    ```bash
    # Đang ở nhánh feature/fix/...
    git fetch origin develop
    git rebase origin/develop
    # Giải quyết xung đột (nếu có)
    ```
2.  **Commit:**
    - **Squash Commits:** Mỗi PR khi merge vào `develop` chỉ nên tương ứng với **1 commit duy nhất** trên nhánh `develop`. Nếu nhánh của bạn có nhiều commit nhỏ trong quá trình phát triển, hãy dùng `git rebase -i HEAD~N` (với N là số commit cần squash) để gộp chúng lại thành một commit có ý nghĩa **trước khi** tạo PR.
    - **Commit Message:** Đặt tên commit rõ ràng, mô tả đúng nội dung thay đổi, tuân theo chuẩn [Conventional Commits](https://www.conventionalcommits.org/).
      - **Ví dụ:**
        ```
        feat: create login api for users
        fix(auth): validate email format according to RFC 5322
        refactor(service): clean up and optimize user service logic
        docs: update api documentation for login endpoint
        ```
3.  **Push:** Push nhánh của bạn lên remote repository.
    ```bash
    # Lần đầu push
    git push -u origin feature/phuong/login-api
    # Sau khi rebase/squash (cần force push an toàn)
    git push --force-with-lease origin feature/phuong/login-api
    ```

---

## 4. Tạo Pull Request (PR) vào `develop`

1.  **Tạo PR:** Từ nhánh `feature`/`fix` của bạn nhắm vào nhánh `develop`.
2.  **Mô Tả PR:** Cần đầy đủ thông tin:
    - Tóm tắt thay đổi chính.
    - Hướng dẫn test (các bước kiểm tra, môi trường test...).
    - Screenshot / Clip minh hoạ (nếu liên quan đến UI/UX).
    - Link task Trello hoặc công cụ quản lý dự án tương ứng.
3.  **Tự Động Hóa (CI):** Nếu có thể, tích hợp CI để tự động chạy tests, linting,... trên PR giúp đảm bảo chất lượng sớm.

---

## 5. Quy Trình Merge vào `develop`

1.  **Kiểm Tra & Test:** Đảm bảo code trên nhánh của bạn sạch sẽ, đã được kiểm tra kỹ lưỡng (tự test hoặc QA test).
2.  **Review Code:** PR phải được review và chấp thuận (approved) bởi ít nhất 1 thành viên khác trong team.
3.  **Merge:**
    - Khi PR đã sẵn sàng và được chấp thuận.
    - Sử dụng tính năng **Squash and Merge** trên nền tảng (GitHub, GitLab, Bitbucket) khi merge PR vào `develop`.
    - **Lý do:** Đảm bảo quy tắc "1 commit trên `develop` cho mỗi PR" được thực thi, giữ lịch sử `develop` gọn gàng và dễ theo dõi.

---

## 6. Quy Trình Release Lên `main` (Dùng Cherry-pick)

> ⚠️ **Lưu ý đặc biệt:** Tuyệt đối **KHÔNG** merge trực tiếp từ `develop` vào `main`. Quy trình này sử dụng cherry-pick để chọn lọc chính xác các commit đã hoàn thiện và được kiểm thử kỹ lưỡng từ `develop` đưa lên `main`.

### Quy trình chuẩn:

1.  **Xác định Commits:** Lập danh sách các **hash commit** trên nhánh `develop` tương ứng với các feature/fix đã hoàn thành, đã được test kỹ và sẵn sàng để release. Đây là các commit _sau khi đã được squash và merge_ vào `develop`.
2.  **Tạo Nhánh Release:** Checkout nhánh `release` mới từ `main` mới nhất.
    ```bash
    git checkout main
    git pull origin main
    git checkout -b release/vX.Y.Z # Thay X.Y.Z bằng phiên bản dự kiến
    ```
3.  **Cherry-pick Commits:** Áp dụng các commit đã xác định ở bước 1 vào nhánh `release`. Thực hiện theo thứ tự logic (thường là thứ tự thời gian chúng được merge vào `develop`).
    ```bash
    git cherry-pick {hash-commit-1}
    git cherry-pick {hash-commit-2}
    # ... tiếp tục với các commit khác cần release
    # Giải quyết xung đột nếu có sau mỗi lần cherry-pick
    ```
4.  **Test Nhánh Release:** Kiểm tra kỹ lưỡng nhánh `release` trên môi trường Staging hoặc tương đương để đảm bảo tất cả hoạt động chính xác sau khi cherry-pick.
5.  **Push Nhánh Release:** Đẩy nhánh `release` lên remote.
    ```bash
    git push -u origin release/vX.Y.Z
    ```
6.  **Tạo PR vào `main`:** Tạo Pull Request từ nhánh `release/vX.Y.Z` nhắm vào nhánh `main`. PR này chủ yếu để thông báo và kiểm tra lần cuối (có thể yêu cầu review).
7.  **Merge vào `main`:** Merge PR từ nhánh `release` vào `main` (thường dùng merge commit thông thường, không squash).
    ```bash
    # Thực hiện qua giao diện PR hoặc lệnh:
    git checkout main
    git pull origin main
    git merge --no-ff release/vX.Y.Z
    git push origin main
    ```
8.  **Deploy:** Thực hiện deploy mã nguồn từ nhánh `main`.
9.  **Tag Version:** Tạo Git tag (annotated tag) trên commit merge mới nhất của `main` để đánh dấu phiên bản.
    ```bash
    git tag -a vX.Y.Z -m "Release version X.Y.Z" # Thay version và message
    git push origin vX.Y.Z # Push tag lên remote
    ```
10. **Xóa Nhánh Release (Tùy chọn):** Có thể xóa nhánh `release` sau khi đã merge thành công.

---

## 7. Quy Trình Hotfix (Sửa Lỗi Khẩn Cấp trên Production)

1.  **Checkout Nhánh Hotfix:** Từ `main` mới nhất.
    ```bash
    git checkout main
    git pull origin main
    git checkout -b hotfix/hoa/critical-payment-bug # Dùng prefix và tên lỗi
    ```
2.  **Sửa Lỗi & Commit:** Thực hiện sửa lỗi và commit (có thể là 1 hoặc nhiều commit nhỏ, nhưng nên squash lại trước khi merge nếu cần). Nhớ tuân thủ chuẩn commit message.
3.  **Merge vào `main`:** Merge nhánh `hotfix` vào `main`. Nên dùng merge commit (`--no-ff`).
    ```bash
    git checkout main
    git pull origin main
    git merge --no-ff hotfix/hoa/critical-payment-bug
    git push origin main
    ```
4.  **Tag Hotfix Release:** Tạo tag cho phiên bản hotfix trên `main`.
    ```bash
    git tag -a vX.Y.Z+hotfix.1 -m "Hotfix for critical payment bug" # Cập nhật version
    git push origin vX.Y.Z+hotfix.1
    ```
5.  **Merge vào `develop`:** **Rất quan trọng!** Merge nhánh `hotfix` (hoặc `main` sau khi đã merge hotfix) vào `develop` để đảm bảo `develop` cũng chứa bản vá này.
    ```bash
    git checkout develop
    git pull origin develop
    git merge --no-ff main # Hoặc merge trực tiếp từ hotfix branch
    git push origin develop
    ```
6.  **Xóa Nhánh Hotfix:** Sau khi đã merge vào cả `main` và `develop`.

---

## 8. Mục Tiêu Cuối Cùng

> Quy trình này nhằm giữ repo:
>
> - Sạch sẽ
> - Rõ ràng người làm (nhờ prefix)
> - Dễ theo dõi (trace) lịch sử thay đổi
> - Dễ dàng quay lại (rollback) khi cần thiết
> - Dễ bảo trì (maintain) code
> - Dễ quản lý phiên bản (versioning)

---
