<!-- 
  Component Template Example
  Mẫu component chuẩn theo kiến trúc dự án
-->
<template>
  <Modal v-model="isVisible" :title="title" @close="handleClose">
    <template #body>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Sử dụng common components -->
          <FormField :label="fieldLabel" :icon="fieldIcon" :error="errors.fieldName" required>
            <InputComponent v-model="formData.fieldName" :disabled="loading" />
          </FormField>

          <!-- Action buttons -->
          <div class="flex gap-3 pt-4">
            <el-button @click="handleClose" :disabled="loading"> Hủy </el-button>
            <el-button type="primary" :loading="loading" :disabled="!canSubmit" @click="handleSubmit">
              {{ loading ? 'Đang xử lý...' : 'Xác nhận' }}
            </el-button>
          </div>
        </form>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { computed, watch, nextTick } from 'vue'
import { ElButton } from 'element-plus'

// Components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'

// Composables
import { useFeatureComposable } from '@/composables/useFeatureComposable.js'

// Props & Emits
const props = defineProps({
  modelValue: Boolean,
})

const emit = defineEmits(['update:modelValue', 'close'])

// Composables
const { loading, formData, errors, canSubmit, resetForm, submitForm, FORM_FIELDS } = useFeatureComposable()

// Computed
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// Methods
const handleClose = () => {
  isVisible.value = false
  emit('close')
  resetForm()
}

const handleSubmit = async () => {
  const result = await submitForm()
  if (result.success) {
    handleClose()
  }
}

// Watchers
watch(
  () => props.modelValue,
  (visible) => {
    if (!visible) {
      nextTick(() => resetForm())
    }
  },
)
</script>

<style scoped>
/* Component-specific styles */
</style>
