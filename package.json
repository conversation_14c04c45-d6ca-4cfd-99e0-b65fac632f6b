{"name": "admin-ui", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "axios": "^1.10.0", "dompurify": "^3.2.6", "echarts": "^6.0.0", "element-plus": "^2.10.2", "lodash": "^4.17.21", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-draggable-next": "^2.2.1", "vue-inline-svg": "^4.0.1", "vue-router": "^4.5.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/eslint-parser": "^7.27.5", "@eslint/js": "^9.22.0", "@tailwindcss/postcss": "^4.1.10", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "sass": "^1.89.2", "tailwindcss": "^4.1.10", "terser": "^5.31.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1"}, "lint-staged": {"*.{js,ts,vue}": "eslint --fix", "*.{js,ts,vue,json,css,md}": "prettier --write"}}