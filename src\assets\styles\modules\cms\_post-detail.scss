/* PostDetail Component Styles */

/* Content body styling */
.content-body {
  line-height: 1.8;
  
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    @apply text-gray-900 dark:text-white font-semibold mt-6 mb-3;
  }

  :deep(h1) { @apply text-2xl; }
  :deep(h2) { @apply text-xl; }
  :deep(h3) { @apply text-lg; }

  :deep(p) {
    @apply mb-4;
  }

  :deep(img) {
    @apply max-w-full h-auto rounded-lg border border-gray-200 dark:border-slate-600;
  }

  :deep(blockquote) {
    @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 my-4;
  }

  :deep(ul),
  :deep(ol) {
    @apply ml-6 mb-4;
  }

  :deep(li) {
    @apply mb-1;
  }

  :deep(a) {
    @apply text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline;
  }

  :deep(code) {
    @apply bg-gray-100 dark:bg-slate-800 px-1 py-0.5 rounded text-sm;
  }

  :deep(pre) {
    @apply bg-gray-100 dark:bg-slate-800 p-4 rounded-lg overflow-x-auto mb-4;
  }

  :deep(table) {
    @apply w-full border-collapse border border-gray-300 dark:border-slate-600 mb-4;
  }

  :deep(th),
  :deep(td) {
    @apply border border-gray-300 dark:border-slate-600 px-3 py-2;
  }

  :deep(th) {
    @apply bg-gray-100 dark:bg-slate-800 font-semibold;
  }
}

/* Prose styling for better typography */
.prose {
  @apply text-gray-700 dark:text-gray-300;
}

.prose-lg {
  @apply text-lg leading-relaxed;
}

.dark .prose-invert {
  @apply text-gray-300;
} 