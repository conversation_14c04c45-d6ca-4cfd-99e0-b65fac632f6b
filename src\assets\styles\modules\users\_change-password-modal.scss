// Change Password Modal Component Styles
// Extracted from ChangePasswordModal.vue <style> section

:deep(.el-button--primary) {
  @apply bg-brand-500 hover:bg-brand-600 border-brand-500 hover:border-brand-600;
  @apply shadow-lg transition-all duration-200 hover:shadow-xl;
}

:deep(.el-button--primary:disabled) {
  @apply border-gray-300 bg-gray-300 dark:border-gray-600 dark:bg-gray-600;
  @apply cursor-not-allowed opacity-50;
}

:deep(.el-button--default) {
  @apply border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300;
  @apply hover:border-gray-400 hover:bg-gray-50 dark:hover:border-gray-500 dark:hover:bg-gray-800;
  @apply transition-all duration-200;
}
