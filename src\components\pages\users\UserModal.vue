<template>
  <Modal
    v-model="isVisible"
    :title="isEdit ? 'Chỉnh sửa người dùng' : 'Tạo người dùng mới'"
    width="600px"
    @close="handleClose"
  >
    <template #body>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Form Fields -->
          <div class="space-y-6">
            <!-- Name Field -->
            <FormField label="Họ và tên" :icon="User" required :error="errors.name">
              <el-input
                v-model="form.name"
                placeholder="Nhập họ và tên người dùng"
                :disabled="saving"
                size="large"
                clearable
              />
            </FormField>

            <!-- Email Field -->
            <FormField label="Email" :icon="Message" required :error="errors.email">
              <el-input
                v-model="form.email"
                type="email"
                placeholder="Nhập địa chỉ email"
                :disabled="saving || isEdit"
                size="large"
                clearable
              />
              <div v-if="isEdit" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Email không thể thay đổi khi cập nhật người dùng
              </div>
            </FormField>

            <!-- Password Field (Only for new users) -->
            <FormField v-if="!isEdit" label="Mật khẩu" :icon="Lock" required :error="errors.password">
              <PasswordInput
                v-model="form.password"
                placeholder="Nhập mật khẩu"
                :has-error="!!errors.password"
                :disabled="saving"
              />

              <!-- Password Strength -->
              <PasswordStrength :password="form.password" />
            </FormField>

            <!-- Password Confirmation Field (Only for new users) -->
            <FormField
              v-if="!isEdit"
              label="Xác nhận mật khẩu"
              :icon="Check"
              required
              :error="errors.password_confirmation"
            >
              <PasswordInput
                v-model="form.password_confirmation"
                placeholder="Nhập lại mật khẩu"
                :has-error="!!errors.password_confirmation"
                :disabled="saving"
              />
            </FormField>

            <!-- Status Field -->
            <FormField label="Trạng thái" :icon="CircleCheck" required :error="errors.status">
              <el-select
                v-model="form.status"
                placeholder="Chọn trạng thái"
                :disabled="saving"
                size="large"
                class="w-full"
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </FormField>

            <!-- Roles Field -->
            <FormField label="Vai trò" :icon="UserFilled" required :error="errors.role_ids">
              <el-select
                v-model="form.role_ids"
                placeholder="Chọn vai trò"
                multiple
                :disabled="saving"
                size="large"
                class="w-full"
              >
                <el-option v-for="role in roles" :key="role.id" :label="role.name" :value="role.id" />
              </el-select>
            </FormField>
          </div>

          <!-- Action Buttons -->
          <ButtonModalCommon
            :loading="saving"
            :can-submit="canSubmit"
            cancel-text="Hủy"
            :submit-text="isEdit ? 'Cập nhật' : 'Tạo mới'"
            loading-text="Đang lưu..."
            @cancel="handleClose"
            @submit="handleSubmit"
          />
        </form>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElInput, ElSelect, ElOption, ElMessage } from 'element-plus'
import { User, Message, Lock, Check, CircleCheck, UserFilled } from '@element-plus/icons-vue'

// Components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import PasswordInput from '@/components/common/PasswordInput.vue'
import PasswordStrength from '@/components/common/PasswordStrength.vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'

// Composables
import { useUsers } from '@/composables/modules/users/useUsers.js'
import { USER_STATUS, USER_STATUS_OPTIONS } from '@/utils/configs/constant.config.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  user: {
    type: Object,
    default: null,
  },
  roles: {
    type: Array,
    default: () => [],
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Composables
const { createUser, updateUser, saving, errors, clearErrors } = useUsers()

// Computed
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isEdit = computed(() => !!props.user)
const statusOptions = computed(() => USER_STATUS_OPTIONS)

const canSubmit = computed(() => {
  const nameValid = !!form.name
  const statusValid = form.status !== undefined && form.status !== null
  const rolesValid = Array.isArray(form.role_ids) && form.role_ids.length > 0

  if (isEdit.value) {
    return nameValid && statusValid && rolesValid
  } else {
    const emailValid = !!form.email
    const passwordValid = !!form.password
    const passwordConfirmValid = !!form.password_confirmation
    const passwordMatch = form.password === form.password_confirmation

    return (
      nameValid && emailValid && passwordValid && passwordConfirmValid && statusValid && rolesValid && passwordMatch
    )
  }
})

// Form data
const form = reactive({
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  status: USER_STATUS.ACTIVE,
  role_ids: [],
})

// Methods
const resetForm = () => {
  Object.assign(form, {
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    status: USER_STATUS.ACTIVE,
    role_ids: [],
  })
  clearErrors()
}

const populateForm = (user) => {
  if (user) {
    Object.assign(form, {
      name: user.name || '',
      email: user.email || '',
      password: '',
      password_confirmation: '',
      status: user.status !== undefined ? user.status : USER_STATUS.ACTIVE,
      role_ids: user.roles ? user.roles.map((role) => role.id) : [],
    })
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const handleSubmit = async () => {
  try {
    if (isEdit.value) {
      await updateUser(props.user.id, form)
    } else {
      await createUser(form)
    }
    emit('success')
    handleClose()
  } catch (error) {
    console.error('Submit error:', error)
  }
}

// Watchers
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.user) {
        populateForm(props.user)
      } else {
        resetForm()
      }
    }
  },
)
</script>

<style lang="scss" scoped>
@use '@/assets/styles/modules/users/_user-form-modal.scss' as *;
</style>
