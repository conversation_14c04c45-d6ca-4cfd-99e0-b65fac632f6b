<template>
  <Modal v-model="isVisible" title="Thay đổi mật khẩu" width="500px" size="compact" @close="handleClose">
    <template #body>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Old Password -->
          <FormField label="Mật khẩu hiện tại" :icon="Lock" required :error="errors[FORM_FIELDS.OLD_PASSWORD]">
            <PasswordInput
              v-model="formData[FORM_FIELDS.OLD_PASSWORD]"
              placeholder="Nhập mật khẩu hiện tại"
              :has-error="!!errors[FORM_FIELDS.OLD_PASSWORD]"
            />
          </FormField>

          <!-- New Password -->
          <FormField label="Mật khẩu mới" :icon="Key" required :error="errors[FORM_FIELDS.NEW_PASSWORD]">
            <PasswordInput
              v-model="formData[FORM_FIELDS.NEW_PASSWORD]"
              placeholder="Nhập mật khẩu mới"
              :has-error="!!errors[FORM_FIELDS.NEW_PASSWORD]"
            />

            <!-- Password Strength -->
            <PasswordStrength :password="formData[FORM_FIELDS.NEW_PASSWORD]" />
          </FormField>

          <!-- New Password Confirmation -->
          <FormField
            label="Xác nhận mật khẩu mới"
            :icon="Check"
            required
            :error="errors[FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]"
          >
            <PasswordInput
              v-model="formData[FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]"
              placeholder="Nhập lại mật khẩu mới"
              :has-error="!!errors[FORM_FIELDS.NEW_PASSWORD_CONFIRMATION]"
            />
          </FormField>

          <!-- Action Buttons -->
          <div class="flex gap-3 border-t border-gray-200 pt-4 dark:border-gray-700">
            <el-button type="default" @click="handleClose" :disabled="loading" class="flex-1" size="large">
              <el-icon class="mr-1"><Close /></el-icon>
              Hủy
            </el-button>

            <el-button
              type="primary"
              :loading="loading"
              :disabled="!canSubmit"
              class="flex-1"
              size="large"
              @click="handleSubmit"
            >
              <el-icon v-if="!loading" class="mr-1"><Check /></el-icon>
              {{ loading ? 'Đang cập nhật...' : 'Cập nhật' }}
            </el-button>
          </div>
        </form>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { computed, watch, nextTick } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { Close, Lock, Key, Check } from '@element-plus/icons-vue'

// Components
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import PasswordInput from '@/components/common/PasswordInput.vue'
import PasswordStrength from '@/components/common/PasswordStrength.vue'

// Composables
import { useChangePassword } from '@/composables/useChangePassword.js'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'close'])

// Composables - Using unified field names
const { loading, formData, errors, canSubmit, resetForm, submitForm, FORM_FIELDS } = useChangePassword()

// Computed
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// Methods
const handleClose = () => {
  isVisible.value = false
  emit('close')
  resetForm()
}

const handleSubmit = async () => {
  const result = await submitForm()

  if (result.success) {
    handleClose()
  }
}

// Watchers
watch(
  () => props.modelValue,
  (visible) => {
    if (!visible) {
      // Reset form when modal closes
      nextTick(() => {
        resetForm()
      })
    }
  },
)
</script>

<style scoped>
:deep(.el-button--primary) {
  @apply bg-brand-500 hover:bg-brand-600 border-brand-500 hover:border-brand-600;
  @apply shadow-lg transition-all duration-200 hover:shadow-xl;
}

:deep(.el-button--primary:disabled) {
  @apply border-gray-300 bg-gray-300 dark:border-gray-600 dark:bg-gray-600;
  @apply cursor-not-allowed opacity-50;
}

:deep(.el-button--default) {
  @apply border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300;
  @apply hover:border-gray-400 hover:bg-gray-50 dark:hover:border-gray-500 dark:hover:bg-gray-800;
  @apply transition-all duration-200;
}
</style>
