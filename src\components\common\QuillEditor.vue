<template>
  <div class="quill-editor-wrapper">
    <quill-editor
      v-model:content="content"
      :options="editorOptions"
      contentType="html"
      @ready="onEditorReady"
      @change="onContentChange"
      :class="['custom-quill-editor', { 'dark-mode': isDarkMode }]"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, inject } from 'vue'
import { QuillEditor } from '@vueup/vue-quill'
import { ElMessage } from 'element-plus'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import '@/assets/styles/components/quill-dark-mode.css'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: 'Nhập nội dung bài viết...',
  },
  height: {
    type: String,
    default: '300px',
  },
  uploadUrl: {
    type: String,
    default: '/api/upload-image',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'blur', 'input'])

// Use theme from ThemeProvider instead of @vueuse/core
const theme = inject('theme', { isDarkMode: ref(false) })
const isDarkMode = computed(() => theme.isDarkMode.value)

// Reactive data
const content = ref(props.modelValue)
const quillInstance = ref(null)

// Editor options
const editorOptions = reactive({
  modules: {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['link', 'image', 'video'],
        ['clean'],
      ],
      handlers: {
        image: imageHandler,
      },
    },
  },
  placeholder: props.placeholder,
  readOnly: props.disabled,
  theme: 'snow',
})

// Watch for prop changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== content.value) {
      content.value = newValue
    }
  },
)

// Watch for content changes to emit update:modelValue
watch(content, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
  // Also emit input event for realtime validation
  emit('input', newValue)
})

watch(
  () => props.disabled,
  (newValue) => {
    editorOptions.readOnly = newValue
  },
)

// Editor ready handler
const onEditorReady = (quill) => {
  quillInstance.value = quill

  // Set editor height
  const editorElement = quill.container.querySelector('.ql-editor')
  if (editorElement) {
    editorElement.style.minHeight = props.height
  }

  // Add blur event listener for validation
  quill.on('text-change', () => {
    emit('input', content.value)
  })

  quill.on('selection-change', (range) => {
    if (!range) {
      // Editor lost focus
      emit('blur', content.value)
    }
  })

  // Add focus/blur events for better validation
  quill.on('editor-change', (eventName) => {
    if (eventName === 'text-change') {
      emit('input', content.value)
    }
  })
}

// Content change handler
const onContentChange = () => {
  // Emit input event for realtime validation
  emit('input', content.value)
}

// Custom image upload handler
async function imageHandler() {
  const input = document.createElement('input')
  input.setAttribute('type', 'file')
  input.setAttribute('accept', 'image/*')
  input.click()

  input.onchange = async () => {
    const file = input.files[0]
    if (!file) return

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      ElMessage.error('Kích thước file không được vượt quá 5MB')
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      ElMessage.error('Chỉ được phép upload file hình ảnh')
      return
    }

    try {
      // Show loading message
      const loadingMessage = ElMessage({
        message: 'Đang upload hình ảnh...',
        type: 'info',
        duration: 0,
      })

      // Create FormData for upload
      const formData = new FormData()
      formData.append('image', file)

      // Upload to server
      const response = await fetch(props.uploadUrl, {
        method: 'POST',
        body: formData,
        headers: {
          // Add authorization header if needed
          // 'Authorization': `Bearer ${token}`
        },
      })

      loadingMessage.close()

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success && data.url) {
        // Insert image into editor
        const range = quillInstance.value.getSelection()
        const index = range ? range.index : 0
        quillInstance.value.insertEmbed(index, 'image', data.url)

        // Move cursor after image
        quillInstance.value.setSelection(index + 1)

        ElMessage.success('Upload hình ảnh thành công')
      } else {
        throw new Error(data.message || 'Upload failed')
      }
    } catch (error) {
      console.error('Image upload error:', error)
      ElMessage.error(error.message || 'Upload hình ảnh thất bại')

      // Fallback: insert image as base64 (for development/testing)
      if (import.meta.env.DEV) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const range = quillInstance.value.getSelection()
          const index = range ? range.index : 0
          quillInstance.value.insertEmbed(index, 'image', e.target.result)
          quillInstance.value.setSelection(index + 1)
        }
        reader.readAsDataURL(file)
        ElMessage.warning('Đang ở chế độ development, hình ảnh được chèn dưới dạng base64')
      }
    }
  }
}

// Expose methods for parent component
defineExpose({
  getQuillInstance: () => quillInstance.value,
  insertText: (text) => {
    if (quillInstance.value) {
      const range = quillInstance.value.getSelection()
      const index = range ? range.index : 0
      quillInstance.value.insertText(index, text)
    }
  },
  focus: () => {
    if (quillInstance.value) {
      quillInstance.value.focus()
    }
  },
})
</script>

<style scoped>
.quill-editor-wrapper {
  width: 100%;
}

.custom-quill-editor {
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
}

.custom-quill-editor.dark-mode {
  border-color: #4b5563;
}

/* Additional custom styles can be added here */
:deep(.ql-editor) {
  font-family: inherit;
  font-size: 14px;
  line-height: 1.6;
}

:deep(.ql-editor.ql-blank::before) {
  font-style: normal;
  color: #999;
}

/* Dark mode specific styles */
.dark .custom-quill-editor :deep(.ql-editor.ql-blank::before) {
  color: #666;
}
</style>
