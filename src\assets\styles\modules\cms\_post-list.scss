// Post List Component Styles
// Following patterns from UserList and CategoryList components

.post-table {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-bg-color-page);
  --el-table-row-hover-bg-color: var(--el-fill-color-light);
}

/* Table header styling */
:deep(.el-table th.el-table__cell) {
  background-color: var(--el-table-header-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-table-border-color) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 12px !important;
}

/* Table cell styling */
:deep(.el-table td.el-table__cell) {
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-table-border-color) !important;
  padding: 16px 12px !important;
}

/* Row hover effects */
:deep(.el-table tr:hover > td.el-table__cell) {
  background-color: var(--el-table-row-hover-bg-color) !important;
  transition: background-color 0.2s ease;
}

/* Alternating row colors */
:deep(.el-table .even-row td.el-table__cell) {
  background-color: var(--el-bg-color) !important;
}

:deep(.el-table .odd-row td.el-table__cell) {
  background-color: var(--el-fill-color-lighter) !important;
}

/* Action buttons using Tailwind classes */
.action-btn {
  @apply rounded-lg px-3 py-1.5 text-xs font-medium;
  @apply transition-all duration-300 ease-out;
  @apply relative overflow-hidden;
  @apply h-8 min-w-[70px];
  @apply inline-flex items-center justify-center;
  @apply gap-1;
}

.view-btn {
  @apply bg-gradient-to-br from-gray-500 to-gray-600;
  @apply border-0 text-white;
  @apply shadow-sm shadow-gray-500/20;
}

.view-btn:hover {
  @apply bg-gradient-to-br from-gray-600 to-gray-700;
  @apply -translate-y-0.5;
  @apply shadow-md shadow-gray-500/30;
}

.edit-btn {
  @apply bg-gradient-to-br from-indigo-500 to-indigo-600;
  @apply border-0 text-white;
  @apply shadow-sm shadow-indigo-500/20;
}

.edit-btn:hover {
  @apply bg-gradient-to-br from-indigo-600 to-indigo-700;
  @apply -translate-y-0.5;
  @apply shadow-md shadow-indigo-500/30;
}

.delete-btn {
  @apply bg-gradient-to-br from-red-500 to-red-600;
  @apply border-0 text-white;
  @apply shadow-sm shadow-red-500/20;
}

.delete-btn:hover {
  @apply bg-gradient-to-br from-red-600 to-red-700;
  @apply -translate-y-0.5;
  @apply shadow-md shadow-red-500/30;
}

/* Create Post button styling */
.create-post-btn {
  @apply bg-gradient-to-br from-blue-500 to-blue-600;
  @apply border-0 text-white;
  @apply shadow-lg shadow-blue-500/30;
  @apply relative overflow-hidden;
  @apply transition-all duration-300 ease-out;
}

.create-post-btn:hover {
  @apply bg-gradient-to-br from-blue-600 to-blue-700;
  @apply -translate-y-0.5 scale-105;
  @apply shadow-xl shadow-blue-500/40;
}

/* Reset button styling */
.reset-btn {
  @apply border-2 border-gray-200 bg-white text-blue-500;
  @apply shadow-sm shadow-black/5;
  @apply relative overflow-hidden font-semibold;
  @apply transition-all duration-300 ease-out;
}

.reset-btn:hover {
  @apply border-blue-500 bg-gray-50 text-blue-600;
  @apply -translate-y-0.5;
  @apply shadow-lg shadow-blue-500/15;
}

/* Dark mode button enhancements */
.dark .view-btn {
  @apply bg-gradient-to-br from-gray-600 to-gray-700;
  @apply shadow-sm shadow-gray-500/30;
}

.dark .view-btn:hover {
  @apply bg-gradient-to-br from-gray-700 to-gray-800;
  @apply shadow-md shadow-gray-500/40;
}

.dark .edit-btn {
  @apply bg-gradient-to-br from-indigo-600 to-indigo-700;
  @apply shadow-sm shadow-indigo-500/30;
}

.dark .edit-btn:hover {
  @apply bg-gradient-to-br from-indigo-700 to-indigo-800;
  @apply shadow-md shadow-indigo-500/40;
}

.dark .delete-btn {
  @apply bg-gradient-to-br from-red-600 to-red-700;
  @apply shadow-sm shadow-red-500/30;
}

.dark .delete-btn:hover {
  @apply bg-gradient-to-br from-red-700 to-red-800;
  @apply shadow-md shadow-red-500/40;
}

.dark .create-post-btn {
  @apply bg-gradient-to-br from-blue-600 to-blue-700;
  @apply shadow-lg shadow-blue-500/40;
}

.dark .create-post-btn:hover {
  @apply bg-gradient-to-br from-blue-700 to-blue-800;
  @apply shadow-xl shadow-blue-500/50;
}

.dark .reset-btn {
  @apply border-gray-600 bg-gray-700 text-blue-400;
  @apply shadow-sm shadow-black/20;
}

.dark .reset-btn:hover {
  @apply border-blue-400 bg-gray-600 text-blue-300;
  @apply shadow-lg shadow-blue-400/20;
}

/* Tag styling */
.status-tag,
.hot-tag {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  padding: 4px 8px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.status-tag:hover,
.hot-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Post cover image styling */
.post-cover {
  @apply w-16 h-12 object-cover rounded-lg;
  @apply border border-gray-200 dark:border-gray-600;
  @apply shadow-sm;
}

/* Dark mode table adjustments */
.dark :deep(.el-table) {
  --el-table-border-color: #374151;
  --el-table-header-bg-color: #1e2636;
  --el-table-row-hover-bg-color: #374151;
}

.dark :deep(.el-table .odd-row td.el-table__cell) {
  background-color: #1e2636 !important;
}

.dark :deep(.el-table .even-row td.el-table__cell) {
  background-color: #1e2636 !important;
}

.dark :deep(.el-table tr:hover > td.el-table__cell) {
  background-color: #374151 !important;
}

/* Table empty state styles */
:deep(.el-table__empty-block) {
  background-color: var(--el-bg-color) !important;
  border: none !important;
}

:deep(.el-table__empty-text) {
  color: var(--el-text-color-regular) !important;
  font-size: 14px !important;
  padding: 40px 20px !important;
}

/* Dark mode empty state */
.dark :deep(.el-table__empty-block) {
  background-color: #1e293b !important;
}

.dark :deep(.el-table__empty-text) {
  color: #94a3b8 !important;
}

/* Focus states for accessibility */
.action-btn:focus,
.create-post-btn:focus,
.reset-btn:focus {
  @apply outline-none;
  @apply ring-2 ring-blue-500/30;
}

.dark .action-btn:focus,
.dark .create-post-btn:focus,
.dark .reset-btn:focus {
  @apply ring-2 ring-blue-400/30;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :deep(.el-table th.el-table__cell),
  :deep(.el-table td.el-table__cell) {
    @apply p-2 text-sm;
  }

  .action-btn {
    @apply px-2 py-1 text-xs;
    @apply h-7 min-w-[60px];
  }

  .action-btn .w-4 {
    @apply h-3 w-3;
  }

  .create-post-btn,
  .reset-btn {
    @apply px-4 py-2 text-sm;
  }

  .post-cover {
    @apply w-12 h-9;
  }
}

@media (max-width: 480px) {
  .action-btn {
    @apply px-1.5 py-0.5 text-xs;
    @apply h-6 min-w-[50px];
  }

  .action-btn .w-4 {
    @apply h-2.5 w-2.5;
  }

  .create-post-btn,
  .reset-btn {
    @apply px-3 py-1.5 text-xs;
  }

  .post-cover {
    @apply w-10 h-8;
  }
}