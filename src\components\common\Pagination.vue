<template>
  <div class="border-t border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
        <span class="text-sm text-gray-700 dark:text-gray-300">
          Hi<PERSON><PERSON> thị {{ pagination.from }} - {{ pagination.to }} của {{ pagination.total }} kết quả
        </span>
        <div class="flex items-center gap-2">
          <el-select
            :model-value="pagination.per_page"
            style="width: 80px"
            size="small"
            @update:model-value="handlePerPageChange"
          >
            <el-option v-for="option in perPageOptions" :key="option" :label="option" :value="option" />
          </el-select>
          <span class="text-sm text-gray-500">mục mỗi trang</span>
        </div>
      </div>
      <div class="flex-shrink-0">
        <el-pagination
          :current-page="pagination.current_page"
          :page-size="pagination.per_page"
          :total="pagination.total"
          layout="prev, pager, next"
          small
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'

const props = defineProps({
  pagination: {
    type: Object,
    required: true,
    default: () => ({
      current_page: 1,
      per_page: 10,
      total: 0,
      from: 0,
      to: 0,
    }),
  },
  perPageOptions: {
    type: Array,
    default: () => [10, 20, 30, 50],
  },
})

const emits = defineEmits(['update:pagination', 'page-change', 'per-page-change'])

// Watch pagination to update from/to values
watch(
  () => props.pagination,
  (p) => {
    if (p.total === 0) {
      p.from = 0
      p.to = 0
      return
    }
    const start = (p.current_page - 1) * p.per_page
    p.from = start + 1
    const end = start + p.per_page
    p.to = Math.min(end, p.total)
  },
  { deep: true, immediate: true },
)

const handleCurrentChange = (page) => {
  const updatedPagination = { ...props.pagination, current_page: page }
  emits('update:pagination', updatedPagination)
  emits('page-change', page)
}

const handlePerPageChange = (perPage) => {
  const updatedPagination = {
    ...props.pagination,
    per_page: perPage,
    current_page: 1, // Reset to first page when changing per page
  }
  emits('update:pagination', updatedPagination)
  emits('per-page-change', perPage)
}
</script>
