<template>
  <el-input
    :model-value="modelValue"
    :type="showPassword ? 'text' : 'password'"
    :placeholder="placeholder"
    :class="{ 'is-error': hasError }"
    :size="size"
    @update:model-value="$emit('update:modelValue', $event)"
    @input="$emit('input', $event)"
    @focus="$emit('focus', $event)"
    @blur="$emit('blur', $event)"
  >
    <template #suffix>
      <el-button
        type="text"
        @click="togglePassword"
        class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
        :aria-label="showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'"
      >
        <el-icon>
          <View v-if="showPassword" />
          <Hide v-else />
        </el-icon>
      </el-button>
    </template>
  </el-input>
</template>

<script setup>
import { ref } from 'vue'
import { ElInput, ElButton, ElIcon } from 'element-plus'
import { View, Hide } from '@element-plus/icons-vue'

defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  hasError: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'large',
  },
})

defineEmits(['update:modelValue', 'input', 'focus', 'blur'])

const showPassword = ref(false)

const togglePassword = () => {
  showPassword.value = !showPassword.value
}
</script>

<style scoped>
:deep(.el-input__wrapper) {
  @apply border-gray-300 bg-gray-50 shadow-sm dark:border-gray-600 dark:bg-gray-800;
  @apply focus:border-brand-500 dark:focus:border-brand-400 focus:ring-brand-500/10 focus:ring-2;
  @apply hover:border-gray-400 dark:hover:border-gray-500;
  @apply transition-all duration-200;
}

:deep(.el-input__wrapper.is-error) {
  @apply border-red-500 focus:ring-red-500/10 dark:border-red-400;
}

:deep(.el-input__inner) {
  @apply text-gray-800 placeholder:text-gray-400 dark:text-white/90 dark:placeholder:text-gray-500;
}
</style>
