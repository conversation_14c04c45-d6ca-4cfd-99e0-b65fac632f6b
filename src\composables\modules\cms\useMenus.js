/**
 * Menu Management Composable
 * Handles all menu management business logic
 */

import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { menusApi } from '@/utils/apis/index.js'

export function useMenus() {
  // State
  const loading = ref(false)
  const loadingMore = ref(false)
  const menus = ref([])
  const currentMenu = ref(null)
  const pagination = reactive({
    current_page: 1,
    per_page: 15,
    total: 0,
    has_more_pages: false,
  })
  const linkableTypes = ref([])

  // Menu CRUD operations
  const fetchMenus = async (page = 1, append = false, searchQuery = '') => {
    try {
      if (append) {
        loadingMore.value = true
      } else {
        loading.value = true
        pagination.current_page = 1
      }

      const params = {
        page: page,
        limit: pagination.per_page,
      }

      // Add search parameter if provided
      if (searchQuery && searchQuery.trim()) {
        params.name = searchQuery.trim()
      }

      const response = await menusApi.getMenus(params)


      if (response.data.success) {
        // Process menus and calculate menu items count
        const menusData = response.data.data.data || []
        const processedMenus = menusData.map((menu) => ({
          ...menu,
          items_count: menu.menu_items ? menu.menu_items.length : 0,
        }))

        if (append) {
          menus.value = [...menus.value, ...processedMenus]
        } else {
          menus.value = processedMenus
        }

        // Update pagination
        const paginationData = response.data.data.pagination || {}
        pagination.current_page = paginationData.current_page || page
        pagination.total = paginationData.total || 0
        pagination.has_more_pages = paginationData.current_page < paginationData.last_page
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải danh sách menu')
      }
    } catch (error) {
      console.error('Error fetching menus:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải danh sách menu')
    } finally {
      if (append) {
        loadingMore.value = false
      } else {
        loading.value = false
      }
    }
  }

  const loadMoreMenus = async () => {
    if (loadingMore.value || !pagination.has_more_pages) return
    
    const nextPage = pagination.current_page + 1
    await fetchMenus(nextPage, true)
  }

  const searchMenus = async (searchQuery) => {
    await fetchMenus(1, false, searchQuery)
  }

  const createMenu = async (menuData) => {
    try {
      loading.value = true
      const response = await menusApi.createMenu(menuData)

      if (response.data.success) {
        const newMenu = response.data.data
        menus.value.push(newMenu)
        pagination.total += 1
        ElMessage.success(response.data.message || 'Tạo menu thành công')
        return newMenu
      } else {
        // Kiểm tra có validation errors không
        if (response.data.errors) {
          const validationErrors = response.data.errors

          // Chỉ hiển thị lỗi validation chi tiết, không hiển thị lỗi chung
          Object.keys(validationErrors).forEach((field) => {
            const fieldErrors = validationErrors[field]
            if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
              fieldErrors.forEach((errorMessage) => {
                ElMessage.error(errorMessage)
              })
            }
          })

          throw { validationErrors }
        } else {
          // Chỉ throw error, không hiển thị message ở đây
          throw new Error(response.data.message || 'Lỗi khi tạo menu')
        }
      }
    } catch (error) {
      console.error('Error creating menu:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)

        throw { validationErrors: errors }
      } else {
        const errorMessage = message || 'Không thể tạo menu'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const getMenuById = async (id) => {
    try {
      loading.value = true
      const response = await menusApi.getMenuById(id)

      if (response.data.success) {
        currentMenu.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải menu')
      }
    } catch (error) {
      console.error('Error fetching menu:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải menu')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateMenu = async (id, menuData) => {
    try {
      loading.value = true
      const response = await menusApi.updateMenu(id, menuData)

      if (response.data.success) {
        const updatedMenu = response.data.data

        // Update in local state
        const index = menus.value.findIndex((m) => m.id === id)
        if (index !== -1) {
          menus.value[index] = updatedMenu
        }

        if (currentMenu.value && currentMenu.value.id === id) {
          currentMenu.value = updatedMenu
        }

        ElMessage.success(response.data.message || 'Cập nhật menu thành công')
        return updatedMenu
      } else {
        throw new Error(response.data.message || 'Lỗi khi cập nhật menu')
      }
    } catch (error) {
      console.error('Error updating menu:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)

        throw { validationErrors: errors }
      } else {
        const errorMessage = message || 'Không thể cập nhật menu'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteMenu = async (id) => {
    try {
      // Kiểm tra xem menu có còn menu items không
      const menuItemsResponse = await fetchMenuItems(id, { limit: 1 })
      const hasMenuItems = menuItemsResponse.data && menuItemsResponse.data.length > 0

      if (hasMenuItems) {
        ElMessage.error('Không thể xóa menu. Vui lòng xóa hết menu items trước khi xóa menu.')
        return false
      }

      await ElMessageBox.confirm(
        'Bạn có chắc chắn muốn xóa menu này?',
        'Xác nhận xóa',
        {
          confirmButtonText: 'Xóa',
          cancelButtonText: 'Hủy',
          type: 'warning',
        },
      )

      loading.value = true
      const response = await menusApi.deleteMenu(id)

      if (response.data.success) {
        // Remove from local state
        menus.value = menus.value.filter((m) => m.id !== id)
        pagination.total = Math.max(0, pagination.total - 1)

        if (currentMenu.value && currentMenu.value.id === id) {
          currentMenu.value = null
        }

        ElMessage.success(response.data.message || 'Xóa menu thành công')
        return true
      } else {
        throw new Error(response.data.message || 'Lỗi khi xóa menu')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Error deleting menu:', error)
        ElMessage.error(error.response?.data?.message || 'Không thể xóa menu')
      }
      return false
    } finally {
      loading.value = false
    }
  }

  // Menu Items operations - Updated for new API with pagination
  const fetchMenuItems = async (menuId, params = {}) => {
    try {
      loading.value = true
      const defaultParams = {
        page: 1,
        limit: 15,
        ...params,
      }

      const response = await menusApi.getMenuItems(menuId, defaultParams)

      if (response.data.success) {
        const menuItemsData = response.data.data.data || []
        const paginationData = response.data.data.pagination || {}
        return { data: menuItemsData, pagination: paginationData }
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải danh sách menu items')
      }
    } catch (error) {
      console.error('Error fetching menu items:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải danh sách menu items')
      throw error
    } finally {
      loading.value = false
    }
  }

  const createMenuItem = async (itemData) => {
    try {
      loading.value = true

      // Transform form data to API format
      const apiData = {
        menu_id: itemData.menu_id,
        title: itemData.title,
        slug: itemData.slug || itemData.title.toLowerCase().replace(/\s+/g, '-'),
        parent_id: itemData.parent_id || null,
        target: itemData.target || '_self',
        status: itemData.status !== false,
      }

      const response = await menusApi.createMenuItem(itemData.menu_id, apiData)

      if (response.data.success) {
        const newItem = response.data.data

        ElMessage.success(response.data.message || 'Thêm menu item thành công')
        return newItem
      } else {
        throw new Error(response.data.message || 'Lỗi khi thêm menu item')
      }
    } catch (error) {
      console.error('Error creating menu item:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)

        throw { validationErrors: errors }
      } else {
        const errorMessage = message || 'Không thể thêm menu item'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const updateMenuItem = async (itemId, itemData) => {
    try {
      loading.value = true

      // Transform form data to API format
      const apiData = {
        menu_id: itemData.menu_id,
        title: itemData.title,
        slug: itemData.slug || itemData.title.toLowerCase().replace(/\s+/g, '-'),
        parent_id: itemData.parent_id || null,
        target: itemData.target || '_self',
        status: itemData.status !== false,
        custom_url: itemData.custom_url || '',
        linkable_id: itemData.linkable_id || null,
        linkable_type: itemData.linkable_type || null,
      }

      const response = await menusApi.updateMenuItem(itemData.menu_id, itemId, apiData)

      if (response.data.success) {
        const updatedItem = response.data.data

        ElMessage.success(response.data.message || 'Cập nhật menu item thành công')
        return updatedItem
      } else {
        throw new Error(response.data.message || 'Lỗi khi cập nhật menu item')
      }
    } catch (error) {
      console.error('Error updating menu item:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)

        throw { validationErrors: errors }
      } else {
        const errorMessage = message || 'Không thể cập nhật menu item'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteMenuItem = async (menuId, itemId) => {
    try {
      loading.value = true
      const response = await menusApi.deleteMenuItem(menuId, itemId)

      if (response.status === 204 || (response.data && response.data.success)) {
        ElMessage.success('Xóa menu item thành công')
      } else {
        throw new Error('Lỗi khi xóa menu item')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Error deleting menu item:', error)
        ElMessage.error(error.response?.data?.message || 'Không thể xóa menu item')
      }
    } finally {
      loading.value = false
    }
  }

  const getMenuItemById = async (menuId, itemId) => {
    try {
      loading.value = true
      const response = await menusApi.getMenuItemById(menuId, itemId)

      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Lỗi khi tải menu item')
      }
    } catch (error) {
      console.error('Error fetching menu item:', error)
      ElMessage.error(error.response?.data?.message || 'Không thể tải menu item')
      throw error
    } finally {
      loading.value = false
    }
  }

  // Get linkable types
  const fetchLinkableTypes = async () => {
    try {
      // TODO: Implement getLinkableTypes in menusApi
      // const response = await menusApi.getLinkableTypes();
      // linkableTypes.value = response.data.data;
    } catch (error) {
      console.error('Error fetching linkable types:', error)
      ElMessage.error('Không thể tải danh sách loại liên kết')
    }
  }

  // Helper functions
  const findItemInTree = (items, itemId) => {
    if (!items) return null

    for (const item of items) {
      if (item.id === itemId) {
        return item
      }
      if (item.children && item.children.length > 0) {
        const found = findItemInTree(item.children, itemId)
        if (found) return found
      }
    }
    return null
  }

  const removeItemFromTree = (items, itemId) => {
    if (!items) return false

    for (let i = 0; i < items.length; i++) {
      if (items[i].id === itemId) {
        items.splice(i, 1)
        return true
      }
      if (items[i].children && items[i].children.length > 0) {
        if (removeItemFromTree(items[i].children, itemId)) {
          return true
        }
      }
    }
    return false
  }

  // Build tree structure for display
  const buildMenuTree = (items) => {
    if (!items || !Array.isArray(items)) return []

    const tree = []
    const itemMap = {}

    // Create a map of all items
    items.forEach((item) => {
      itemMap[item.id] = { ...item, children: [] }
    })

    // Build the tree
    items.forEach((item) => {
      if (item.parent_id && itemMap[item.parent_id]) {
        itemMap[item.parent_id].children.push(itemMap[item.id])
      } else {
        tree.push(itemMap[item.id])
      }
    })

    return tree
  }

  return {
    // State
    loading,
    loadingMore,
    menus,
    currentMenu,
    pagination,
    linkableTypes,

    // Menu operations
    fetchMenus,
    loadMoreMenus,
    searchMenus,
    createMenu,
    getMenuById,
    updateMenu,
    deleteMenu,

    // Menu item operations
    fetchMenuItems,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    getMenuItemById,

    // Utility
    fetchLinkableTypes,
    findItemInTree,
    removeItemFromTree,
    buildMenuTree,
  }
}
