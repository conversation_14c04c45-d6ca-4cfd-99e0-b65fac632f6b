import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import overviewApi from '@/utils/apis/overview.api.js'
import { TIME_PERIOD, OVERVIEW_TYPE } from '@/utils/configs/constant.config.js'

/**
 * Composable for managing overview analytics data
 * <PERSON><PERSON>ản lý dữ liệu thống kê tổng quan theo thời gian
 */
export function useOverviewAnalytics() {
  // State for each analytics box
  const analyticsData = ref({
    users: { current: 0, growth: 0, previous: 0, loading: false },
    ips: { current: 0, growth: 0, previous: 0, loading: false },
    revenue: { current: 0, growth: 0, previous: 0, loading: false },
    transactions: { current: 0, growth: 0, previous: 0, loading: false }
  })

  // Generic function to load analytics data
  const loadAnalyticsData = async (timePeriod, overviewType, dataKey, errorMessage) => {
    try {
      analyticsData.value[dataKey].loading = true

      // Get current period data
      const response = await overviewApi.getOverviewByDate(timePeriod, overviewType)
     
      
      // Handle axios response (response.data contains the actual data)
      const result = response.data
      
      if (result.success && result.data) {
        const current = result?.data?.total || 0
        const previous = result?.data?.previousValue || 0
        const growth = result?.data?.growthPercentage || 0
        
        analyticsData.value[dataKey] = {
          current,
          growth: Number(growth.toFixed(1)),
          previous,
          loading: false
        }
        
      } else {
        throw new Error(result.message || 'Invalid response format')
      }
    } catch (error) {
      console.error(`Error loading ${dataKey} analytics:`, error)
      ElMessage.error(errorMessage)
      
      // Set fallback values
      analyticsData.value[dataKey] = { 
        current: 0, 
        growth: 0, 
        previous: 0, 
        loading: false 
      }
    }
  }

  // Specific analytics loaders
  const loadUsersAnalytics = (timePeriod) => {
    return loadAnalyticsData(
      timePeriod,
      OVERVIEW_TYPE.REGISTERED_ACCOUNTS,
      'users',
      'Lỗi khi tải dữ liệu người dùng'
    )
  }

  const loadIpsAnalytics = (timePeriod) => {
    return loadAnalyticsData(
      timePeriod,
      OVERVIEW_TYPE.REGISTERED_IPS,
      'ips',
      'Lỗi khi tải dữ liệu IP'
    )
  }

  const loadRevenueAnalytics = (timePeriod) => {
    return loadAnalyticsData(
      timePeriod,
      OVERVIEW_TYPE.TOTAL_REVENUE,
      'revenue',
      'Lỗi khi tải dữ liệu doanh thu'
    )
  }

  const loadTransactionsAnalytics = (timePeriod) => {
    return loadAnalyticsData(
      timePeriod,
      OVERVIEW_TYPE.TRANSACTION_COUNT,
      'transactions',
      'Lỗi khi tải dữ liệu giao dịch'
    )
  }

  // Load all analytics at once
  const loadAllAnalytics = async (timePeriods = {}) => {
    const defaultPeriods = {
      users: TIME_PERIOD.TODAY,
      ips: TIME_PERIOD.TODAY,
      revenue: TIME_PERIOD.TODAY,
      transactions: TIME_PERIOD.TODAY
    }

    const periods = { ...defaultPeriods, ...timePeriods }

    await Promise.allSettled([
      loadUsersAnalytics(periods.users),
      loadIpsAnalytics(periods.ips),
      loadRevenueAnalytics(periods.revenue),
      loadTransactionsAnalytics(periods.transactions)
    ])
  }

  return {
    analyticsData,
    loadUsersAnalytics,
    loadIpsAnalytics,
    loadRevenueAnalytics,
    loadTransactionsAnalytics,
    loadAllAnalytics
  }
}
