# ButtonCommon Usage Examples

## Import

```javascript
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { RefreshIcon, PlusIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'
```

## Basic Usage

### Simple Button

```vue
<ButtonCommon text="Click me" @click="handleClick" />
```

### Button with Icon

```vue
<ButtonCommon text="Refresh" :icon="RefreshIcon" @click="handleRefresh" />
```

### Icon-only Button with Tooltip

```vue
<!-- Edit button with tooltip -->
<ButtonCommon :icon="EditIcon" type="info" size="small" rounded tooltip="Chỉnh sửa" @click="handleEdit" />

<!-- Delete button with tooltip -->
<ButtonCommon :icon="TrashIcon" type="danger" size="small" rounded tooltip="Xóa" @click="handleDelete" />
```

### Primary Button

```vue
<ButtonCommon type="primary" text="Save Changes" :icon="SaveIcon" @click="handleSave" />
```

## Common Use Cases for Menu Management

### 1. Refresh/Sync Buttons

```vue
<!-- Refresh Menu List -->
<ButtonCommon
  text="Đồng bộ Menu"
  :icon="RefreshIcon"
  type="default"
  size="medium"
  :loading="loading"
  @click="handleRefresh"
/>

<!-- Sync Menu Items -->
<ButtonCommon text="Sync Items" :icon="RefreshIcon" type="info" variant="outline" @click="syncMenuItems" />
```

### 2. CRUD Operation Buttons

```vue
<!-- Create Button -->
<ButtonCommon text="Thêm Menu" :icon="PlusIcon" type="primary" @click="showCreateModal = true" />

<!-- Edit Button -->
<ButtonCommon text="Sửa" :icon="EditIcon" type="info" size="small" @click="editItem(item)" />

<!-- Delete Button -->
<ButtonCommon text="Xóa" :icon="TrashIcon" type="danger" size="small" @click="deleteItem(item.id)" />
```

### 3. Action Buttons

```vue
<!-- Settings Button -->
<ButtonCommon :icon="SettingsIcon" type="default" variant="ghost" rounded @click="openSettings" />

<!-- Icon Only Button -->
<ButtonCommon :icon="RefreshIcon" type="primary" size="small" rounded :loading="refreshing" @click="refresh" />
```

## Props Reference

| Prop           | Type          | Default   | Options                                                              | Description          |
| -------------- | ------------- | --------- | -------------------------------------------------------------------- | -------------------- |
| `type`         | String        | 'default' | 'default', 'primary', 'success', 'warning', 'danger', 'info', 'text' | Button color theme   |
| `size`         | String        | 'medium'  | 'small', 'medium', 'large'                                           | Button size          |
| `variant`      | String        | 'solid'   | 'solid', 'outline', 'ghost', 'gradient'                              | Button style variant |
| `disabled`     | Boolean       | false     | -                                                                    | Disable button       |
| `loading`      | Boolean       | false     | -                                                                    | Show loading state   |
| `text`         | String        | ''        | -                                                                    | Button text          |
| `icon`         | String/Object | null      | -                                                                    | Icon component       |
| `iconPosition` | String        | 'left'    | 'left', 'right'                                                      | Icon position        |
| `htmlType`     | String        | 'button'  | 'button', 'submit', 'reset'                                          | HTML button type     |
| `rounded`      | Boolean       | false     | -                                                                    | Rounded button       |
| `block`        | Boolean       | false     | -                                                                    | Full width button    |
| `shadow`       | Boolean       | true      | -                                                                    | Button shadow        |

## Events

| Event    | Description                  |
| -------- | ---------------------------- |
| `@click` | Fired when button is clicked |

## Example Implementation in MenuList.vue

```vue
<template>
  <!-- Replace existing buttons with ButtonCommon -->
  <div class="header-content">
    <h3 class="panel-title">
      {{ selectedMenu ? `${selectedMenu.name} - ${totalItems} menu items` : 'Chọn Menu để quản lý Items' }}
    </h3>

    <!-- Add Item Button -->
    <ButtonCommon
      v-if="selectedMenu"
      text="Thêm Item"
      :icon="PlusIcon"
      type="primary"
      size="small"
      @click="showAddItemModal = true"
    />
  </div>

  <!-- Menu Sidebar Actions -->
  <div class="sidebar-actions">
    <!-- Create Menu -->
    <ButtonCommon text="Tạo Menu" :icon="PlusIcon" type="primary" @click="handleCreateMenu" />

    <!-- Refresh -->
    <ButtonCommon text="Đồng bộ" :icon="RefreshIcon" type="default" :loading="loading" @click="handleRefresh" />
  </div>

  <!-- Item Actions -->
  <div class="item-actions">
    <!-- Edit Item -->
    <ButtonCommon text="Sửa" :icon="EditIcon" type="info" size="small" @click="editMenuItem(item)" />

    <!-- Delete Item -->
    <ButtonCommon text="Xóa" :icon="TrashIcon" type="danger" size="small" @click="deleteMenuItem(item.id)" />
  </div>
</template>

<script setup>
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import { PlusIcon, RefreshIcon, EditIcon, TrashIcon } from '@/components/icons/index.js'
// ... rest of your script
</script>
```

## Styling Benefits

1. **Consistent Design**: All buttons follow the same design system
2. **Theme Support**: Automatic dark mode support
3. **Responsive**: Adapts to different screen sizes
4. **Accessible**: Built-in focus states and ARIA support
5. **Performance**: Optimized with Tailwind classes
6. **Maintainable**: Single source of truth for button styling

## Migration Guide

### Before (Element Plus)

```vue
<el-button type="primary" size="small" :icon="PlusIcon" @click="handleClick">
  Add Item
</el-button>
```

### After (ButtonCommon)

```vue
<ButtonCommon text="Add Item" :icon="PlusIcon" type="primary" size="small" @click="handleClick" />
```

## Tooltip Feature

### Automatic Tooltip Display

Tooltip sẽ tự động hiển thị khi:

- Prop `tooltip` có giá trị
- Button chỉ có icon mà không có text (icon-only button)
- Button không trong trạng thái `disabled` hoặc `loading`

### Examples

```vue
<!-- Tooltip sẽ hiển thị -->
<ButtonCommon :icon="EditIcon" tooltip="Chỉnh sửa" />

<!-- Tooltip KHÔNG hiển thị vì có text -->
<ButtonCommon :icon="EditIcon" text="Sửa" tooltip="Chỉnh sửa" />

<!-- Tooltip KHÔNG hiển thị vì disabled -->
<ButtonCommon :icon="EditIcon" tooltip="Chỉnh sửa" :disabled="true" />

<!-- Icon-only action buttons với tooltip -->
<div class="action-buttons">
  <ButtonCommon 
    :icon="EditIcon" 
    type="info" 
    size="small" 
    rounded 
    tooltip="Chỉnh sửa"
    @click="handleEdit" 
  />
  <ButtonCommon 
    :icon="TrashIcon" 
    type="danger" 
    size="small" 
    rounded 
    tooltip="Xóa"
    @click="handleDelete" 
  />
</div>
```
