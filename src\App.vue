<template>
  <div id="app">
    <ThemeProvider>
      <SidebarProvider>
        <MainLayout v-if="isAuthenticated" />
        <AuthLayout v-else />
      </SidebarProvider>
    </ThemeProvider>
  </div>
</template>

<script setup>
import ThemeProvider from '@/components/layouts/ThemeProvider.vue'
import SidebarProvider from '@/components/layouts/SidebarProvider.vue'
import MainLayout from '@/layouts/MainLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'
import { useAuthStore } from '@/state/index.js'
import { storeToRefs } from 'pinia'

const authStore = useAuthStore()
const { isAuthenticated } = storeToRefs(authStore)
</script>
