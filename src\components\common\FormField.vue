<template>
  <div class="form-field">
    <label v-if="label" class="form-label">
      <div class="mb-3 flex items-center gap-2">
        <el-icon v-if="icon" class="text-brand-500">
          <component :is="icon" />
        </el-icon>
        <span class="text-sm font-semibold text-gray-700 dark:text-gray-300">
          {{ label }}
          <span v-if="required" class="text-red-500">*</span>
        </span>
      </div>
    </label>

    <slot />

    <p v-if="error" class="form-error">{{ error }}</p>
  </div>
</template>

<script setup>
import { ElIcon } from 'element-plus'

defineProps({
  label: {
    type: String,
    default: '',
  },
  icon: {
    type: [String, Object],
    default: null,
  },
  required: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: '',
  },
})
</script>

<style scoped>
.form-field > * + * {
  margin-top: 0.5rem;
}

.form-label {
  @apply block;
}
</style>
