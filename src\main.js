import { createApp } from 'vue'
import App from '@/App.vue'
import router from '@/router/index.js'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import InlineSvg from 'vue-inline-svg'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import '@/assets/styles/_main.css'
import '@/assets/styles/components/element-plus-dark.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  zIndex: 100000,
  // Configure Element Plus for better dark mode support
  experimentalFeatures: {
    // Enable CSS variables for better theme switching
    useCssVars: true,
  },
})
app.component('inline-svg', InlineSvg)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
