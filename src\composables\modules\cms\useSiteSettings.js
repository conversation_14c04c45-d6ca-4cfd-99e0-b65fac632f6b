import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { siteSettingsApi } from '@/utils/apis/index.js'
import { 
  isProtectedSettingGroup, 
  isProtectedSetting, 
  getProtectedGroupDeletionMessage, 
  getProtectedSettingDeletionMessage 
} from '@/utils/configs/protected-settings.config.js'

export function useSiteSettings() {
  // State
  const loading = ref(false)
  const loadingSettings = ref(false)
  const loadingMoreGroups = ref(false)
  const loadingMoreSettings = ref(false)
  const siteSettingGroups = ref([])
  const currentGroup = ref(null)
  const siteSettings = ref([])
  const currentSetting = ref(null)
  const groupsPagination = reactive({
    current_page: 1,
    per_page: 15,
    limit: 15, // Keep for API compatibility
    total: 0,
    last_page: 1,
    from: 0,
    to: 0,
    has_more_pages: false
  })
  const settingsPagination = reactive({
    current_page: 1,
    per_page: 15,
    limit: 15, // Keep for API compatibility
    total: 0,
    last_page: 1,
    from: 0,
    to: 0,
    has_more_pages: false
  })

  // Site Setting Groups Methods
  const fetchSiteSettingGroups = async (params = {}, loadMore = false) => {
    try {
      if (loadMore) {
        loadingMoreGroups.value = true
      } else {
        loading.value = true
        groupsPagination.current_page = 1
        siteSettingGroups.value = []
      }

      const response = await siteSettingsApi.getSiteSettingGroups({
        ...params,
        page: groupsPagination.current_page,
        limit: groupsPagination.limit
      })

      if (response.data.success) {
        const newGroups = response.data.data.data
        if (loadMore) {
          siteSettingGroups.value = [...siteSettingGroups.value, ...newGroups]
        } else {
          siteSettingGroups.value = newGroups
        }
        
        const paginationData = response.data.data.pagination
        Object.assign(groupsPagination, {
          ...paginationData,
          per_page: paginationData.limit || paginationData.per_page || groupsPagination.per_page,
          limit: paginationData.limit || paginationData.per_page || groupsPagination.limit,
          has_more_pages: paginationData.has_more_pages || paginationData.current_page < paginationData.last_page
        })
      } else {
        throw new Error(response.data.message || 'Không thể lấy danh sách nhóm cài đặt')
      }
    } catch (error) {
      console.error('Error fetching site setting groups:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể lấy danh sách nhóm cài đặt')
    } finally {
      loading.value = false
      loadingMoreGroups.value = false
    }
  }

  const loadMoreGroups = async (params = {}) => {
    if (loadingMoreGroups.value || !groupsPagination.has_more_pages) return
    
    groupsPagination.current_page += 1
    await fetchSiteSettingGroups(params, true)
  }

  const getSiteSettingGroupById = async (id) => {
    try {
      loading.value = true
      const response = await siteSettingsApi.getSiteSettingGroupById(id)

      if (response.data.success) {
        currentGroup.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Không thể lấy thông tin nhóm cài đặt')
      }
    } catch (error) {
      console.error('Error fetching site setting group:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể lấy thông tin nhóm cài đặt')
      throw error
    } finally {
      loading.value = false
    }
  }

  const createSiteSettingGroup = async (data) => {
    try {
      loading.value = true
      const response = await siteSettingsApi.createSiteSettingGroup(data)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Tạo nhóm cài đặt thành công')
        await fetchSiteSettingGroups()
        return response.data.data
      } else {
        // If response is not successful, check for detailed errors first
        if (response.data.errors) {
          const errors = response.data.errors
          const errorMessages = Object.values(errors).flat()
          throw new Error(errorMessages.join(', '))
        } else {
          throw new Error(response.data.message || 'Không thể tạo nhóm cài đặt')
        }
      }
    } catch (error) {
      console.error('Error creating site setting group:', error)
      console.error('Error object structure:', error)

      // Check if error has errors property directly (from axios interceptor or custom error)
      if (error.errors) {
        console.error('Direct validation errors:', error.errors)
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.errors) {
        // Handle validation errors from response
        const errors = error.response.data.errors
        console.error('Response validation errors:', errors)
        const errorMessages = Object.values(errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else {
        ElMessage.error(error.response?.data?.message || error.message || 'Không thể tạo nhóm cài đặt')
      }
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSiteSettingGroup = async (id, data) => {
    try {
      loading.value = true
      const response = await siteSettingsApi.updateSiteSettingGroup(id, data)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Cập nhật nhóm cài đặt thành công')
        await fetchSiteSettingGroups()
        return response.data.data
      } else {
        // If response is not successful, check for detailed errors first
        if (response.data.errors) {
          const errors = response.data.errors
          const errorMessages = Object.values(errors).flat()
          throw new Error(errorMessages.join(', '))
        } else {
          throw new Error(response.data.message || 'Không thể cập nhật nhóm cài đặt')
        }
      }
    } catch (error) {
      console.error('Error updating site setting group:', error)

      // Check if error has errors property directly (from axios interceptor or custom error)
      if (error.errors) {
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.errors) {
        // Handle validation errors from response
        const errors = error.response.data.errors
        const errorMessages = Object.values(errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else {
        ElMessage.error(error.response?.data?.message || error.message || 'Không thể cập nhật nhóm cài đặt')
      }
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteSiteSettingGroup = async (id) => {
    try {
      // Tìm thông tin nhóm cài đặt để kiểm tra bảo vệ
      const group = siteSettingGroups.value.find(g => g.id === id)
      if (group) {
        // Kiểm tra xem nhóm có được bảo vệ không (sử dụng cả group_key và name)
        if (isProtectedSettingGroup(group.group_key, group.name)) {
          const errorMessage = getProtectedGroupDeletionMessage(group.group_key)
          ElMessage.error(errorMessage)
          return false
        }
      }

      // Kiểm tra xem nhóm cài đặt có còn setting con không
      await fetchSiteSettings(id, { limit: 1 })
      const hasSettings = siteSettings.value && siteSettings.value.length > 0

      if (hasSettings) {
        ElMessage.error('Không thể xóa nhóm cài đặt. Vui lòng xóa hết các cài đặt con trước khi xóa nhóm cài đặt.')
        return false
      }

      await ElMessageBox.confirm(
        'Bạn có chắc chắn muốn xóa nhóm cài đặt này?',
        'Xác nhận xóa',
        {
          confirmButtonText: 'Xóa',
          cancelButtonText: 'Hủy',
          type: 'warning',
        },
      )

      loading.value = true
      const response = await siteSettingsApi.deleteSiteSettingGroup(id)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Xóa nhóm cài đặt thành công')
        await fetchSiteSettingGroups()
        return true
      } else {
        throw new Error(response.data.message || 'Không thể xóa nhóm cài đặt')
      }
    } catch (error) {
      if (error === 'cancel' || error.message === 'cancel') {
        return false // User cancelled
      }
      console.error('Error deleting site setting group:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể xóa nhóm cài đặt')
      return false
    } finally {
      loading.value = false
    }
  }

  // Site Settings Methods
  const fetchSiteSettings = async (groupId, params = {}, loadMore = false) => {
    try {
      if (loadMore) {
        loadingMoreSettings.value = true
      } else {
        loadingSettings.value = true
        settingsPagination.current_page = 1
        siteSettings.value = []
      }

      const response = await siteSettingsApi.getSiteSettings(groupId, {
        ...params,
        page: settingsPagination.current_page,
        limit: settingsPagination.limit
      })

      if (response.data.success) {
        // Ensure status values are converted to numbers and group_id is set for consistent comparison
        const newSettings = response.data.data.data.map((setting) => ({
          ...setting,
          status: parseInt(setting.status, 10),
          site_setting_group_id: setting.site_setting_group_id || groupId, // Ensure group_id is set
          statusLoading: false, // Initialize loading state for UI components
        }))
        
        if (loadMore) {
          siteSettings.value = [...siteSettings.value, ...newSettings]
        } else {
          siteSettings.value = newSettings
        }
        
        const paginationData = response.data.data.pagination
        Object.assign(settingsPagination, {
          ...paginationData,
          has_more_pages: paginationData.has_more_pages || paginationData.current_page < paginationData.last_page
        })
      } else {
        throw new Error(response.data.message || 'Không thể lấy danh sách cài đặt')
      }
    } catch (error) {
      console.error('Error fetching site settings:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể lấy danh sách cài đặt')
    } finally {
      loadingSettings.value = false
      loadingMoreSettings.value = false
    }
  }

  const loadMoreSettings = async (groupId, params = {}) => {
    if (loadingMoreSettings.value || !settingsPagination.has_more_pages) return
    
    settingsPagination.current_page += 1
    await fetchSiteSettings(groupId, params, true)
  }

  const getSiteSettingById = async (groupId, id) => {
    try {
      loading.value = true
      const response = await siteSettingsApi.getSiteSettingById(groupId, id)

      if (response.data.success) {
        // Ensure status value is converted to number for consistent comparison
        const settingData = {
          ...response.data.data,
          status: parseInt(response.data.data.status, 10),
        }
        currentSetting.value = settingData
        return settingData
      } else {
        throw new Error(response.data.message || 'Không thể lấy thông tin cài đặt')
      }
    } catch (error) {
      console.error('Error fetching site setting:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể lấy thông tin cài đặt')
      throw error
    } finally {
      loading.value = false
    }
  }

  const createSiteSetting = async (data) => {
    try {
      loadingSettings.value = true

      // Extract group ID from FormData or regular object
      let groupId
      if (data instanceof FormData) {
        groupId = data.get('site_setting_group_id')
      } else {
        groupId = data.site_setting_group_id
      }

      const response = await siteSettingsApi.createSiteSetting(groupId, data)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Tạo cài đặt thành công')
        return response.data.data
      } else {
        // If response is not successful, check for detailed errors first
        if (response.data.errors) {
          const errors = response.data.errors
          const errorMessages = Object.values(errors).flat()
          throw new Error(errorMessages.join(', '))
        } else {
          throw new Error(response.data.message || 'Không thể tạo cài đặt')
        }
      }
    } catch (error) {
      console.error('Error creating site setting:', error)
      console.error('Full error response:', error.response)
      console.error('Error object structure:', error)

      // Check if error has errors property directly (from axios interceptor or custom error)
      if (error.errors) {
        console.error('Direct validation errors:', error.errors)
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.errors) {
        // Handle validation errors from response
        const errors = error.response.data.errors
        console.error('Response validation errors:', errors)
        const errorMessages = Object.values(errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.message) {
        console.error('Server message:', error.response.data.message)
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error(error.message || 'Không thể tạo cài đặt')
      }
      throw error
    } finally {
      loadingSettings.value = false
    }
  }

  const updateSiteSetting = async (id, data) => {
    try {
      loadingSettings.value = true

      // Extract group ID from FormData or regular object
      let groupId
      if (data instanceof FormData) {
        groupId = data.get('site_setting_group_id')
      } else {
        groupId = data.site_setting_group_id
      }

      const response = await siteSettingsApi.updateSiteSetting(groupId, id, data)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Cập nhật cài đặt thành công')
        return response.data.data
      } else {
        // If response is not successful, check for detailed errors first
        if (response.data.errors) {
          const errors = response.data.errors
          const errorMessages = Object.values(errors).flat()
          throw new Error(errorMessages.join(', '))
        } else {
          throw new Error(response.data.message || 'Không thể cập nhật cài đặt')
        }
      }
    } catch (error) {
      console.error('Error updating site setting:', error)

      // Check if error has errors property directly (from axios interceptor or custom error)
      if (error.errors) {
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.errors) {
        // Handle validation errors from response
        const errors = error.response.data.errors
        const errorMessages = Object.values(errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else {
        ElMessage.error(error.response?.data?.message || error.message || 'Không thể cập nhật cài đặt')
      }
      throw error
    } finally {
      loadingSettings.value = false
    }
  }

  const deleteSiteSetting = async (id) => {
    try {
      // Find the setting to get group id
      const setting = siteSettings.value.find((s) => s.id === id)
      if (!setting) {
        throw new Error('Không tìm thấy cài đặt')
      }

      // Kiểm tra xem setting có được bảo vệ không (sử dụng cả key và name)
      if (isProtectedSetting(setting.key, setting.name)) {
        const errorMessage = getProtectedSettingDeletionMessage(setting.key)
        ElMessage.error(errorMessage)
        return false
      }

      await ElMessageBox.confirm(
        'Bạn có chắc chắn muốn xóa cài đặt này? Hành động này không thể hoàn tác.',
        'Xác nhận xóa',
        {
          confirmButtonText: 'Xóa',
          cancelButtonText: 'Hủy',
          type: 'warning',
        },
      )

      loadingSettings.value = true
      const response = await siteSettingsApi.deleteSiteSetting(setting.site_setting_group_id, id)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Xóa cài đặt thành công')
        // Remove from current list instead of refetching
        siteSettings.value = siteSettings.value.filter((s) => s.id !== id)
      } else {
        throw new Error(response.data.message || 'Không thể xóa cài đặt')
      }
    } catch (error) {
      if (error === 'cancel' || error.message === 'cancel') {
        return // User cancelled, don't show error
      }
      console.error('Error deleting site setting:', error)
      ElMessage.error(error.response?.data?.message || error.message || 'Không thể xóa cài đặt')
    } finally {
      loadingSettings.value = false
    }
  }

  const patchSiteSettingAttributes = async (groupId, id, data) => {
    try {
      const response = await siteSettingsApi.patchSiteSettingAttributes(groupId, id, data)

      // Handle different response formats - prioritize response.data.success
      if (response.data && response.data.success === true) {
        
        // Update the setting in the local array
        const settingIndex = siteSettings.value.findIndex(setting => setting.id === id)
        
        if (settingIndex !== -1) {          
          // Preserve UI state properties before updating
          const preservedProps = {
            statusLoading: siteSettings.value[settingIndex].statusLoading || false
          }
          
          // Update the setting with new data from server
          if (response.data.data) {
            siteSettings.value[settingIndex] = {
              ...siteSettings.value[settingIndex],
              ...response.data.data,
              ...preservedProps  // Restore preserved UI properties
            }
          } else {
            // If no data returned, just update the fields we sent
            siteSettings.value[settingIndex] = {
              ...siteSettings.value[settingIndex],
              ...data,
              ...preservedProps  // Restore preserved UI properties
            }
          }
        }

        const message = response.data.message || 'Cập nhật thuộc tính thành công'
        ElMessage.success(message)
        
        return response.data.data || data
      } else {
        throw new Error(response.data?.message || 'Không thể cập nhật thuộc tính')
      }
    } catch (error) {

      // If the PATCH endpoint doesn't exist (404), fallback to the old method
      if (error.response?.status === 404 || error.response?.status === 405) {
        return await fallbackUpdateSetting(groupId, id, data)
      }

      // Handle different error formats
      if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.errors) {
        const errorMessages = Object.values(error.errors).flat()
        ElMessage.error(errorMessages.join(', '))
      } else if (error.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error(error.message || 'Có lỗi xảy ra khi cập nhật thuộc tính')
      }
      throw error
    }
  }

  // Fallback method if PATCH endpoint doesn't exist
  const fallbackUpdateSetting = async (groupId, id, data) => {
    try {
      // Find the current setting to get all its data
      const setting = siteSettings.value.find(s => s.id === id)
      if (!setting) {
        throw new Error('Không tìm thấy cài đặt')
      }

      // Create full update data by merging current setting with new data
      const updateData = {
        name: setting.name,
        key: setting.key,
        type: setting.type,
        value: setting.value || '',
        site_setting_group_id: groupId,
        group_id: groupId,
        ...data // Override with new data
      }

      const response = await siteSettingsApi.updateSiteSetting(groupId, id, updateData)
      
      if (response.data && (response.data.success === true || response.status === 200)) {
        // Update the setting in the local array
        const settingIndex = siteSettings.value.findIndex(setting => setting.id === id)
        if (settingIndex !== -1) {
          // Preserve UI state properties before updating
          const preservedProps = {
            statusLoading: siteSettings.value[settingIndex].statusLoading || false
          }
          
          if (response.data.data) {
            siteSettings.value[settingIndex] = {
              ...siteSettings.value[settingIndex],
              ...response.data.data,
              ...preservedProps  // Restore preserved UI properties
            }
          } else {
            siteSettings.value[settingIndex] = {
              ...siteSettings.value[settingIndex],
              ...data,
              ...preservedProps  // Restore preserved UI properties
            }
          }
        }

        const message = response.data.message || 'Cập nhật thuộc tính thành công'
        ElMessage.success(message)
        return response.data.data || data
      } else {
        throw new Error(response.data?.message || 'Không thể cập nhật thuộc tính')
      }
    } catch (error) {
      console.error('Error in fallback update:', error)
      throw error
    }
  }

  return {
    // State
    loading,
    loadingSettings,
    loadingMoreGroups,
    loadingMoreSettings,
    siteSettingGroups,
    currentGroup,
    siteSettings,
    currentSetting,
    groupsPagination,
    settingsPagination,

    // Site Setting Groups Methods
    fetchSiteSettingGroups,
    loadMoreGroups,
    getSiteSettingGroupById,
    createSiteSettingGroup,
    updateSiteSettingGroup,
    deleteSiteSettingGroup,

    // Site Settings Methods
    fetchSiteSettings,
    loadMoreSettings,
    getSiteSettingById,
    createSiteSetting,
    updateSiteSetting,
    deleteSiteSetting,
    patchSiteSettingAttributes,
  }
}
