/**
 * Helper functions để xử lý response data và tránh việ<PERSON> truy cập đa cấp trực tiếp
 */

/**
 * L<PERSON>y giá trị nested từ object một cách an toàn
 * @param {Object} obj - Object cần lấy giá trị
 * @param {string} path - Đường dẫn đến giá trị (VD: 'data.success')
 * @param {*} defaultValue - Giá trị mặc định nếu không tìm thấy
 * @returns {*} Giá trị tìm được hoặc giá trị mặc định
 */
export const getNestedValue = (obj, path, defaultValue = undefined) => {
  if (!obj || !path) return defaultValue
  return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue
}

/**
 * Xử lý response từ API một cách chuẩn hóa
 * @param {Object} response - Response từ API
 * @returns {Object} Object đã được chuẩn hóa với success, data, message
 */
export const extractApiResponse = (response) => {
  const { data: responseData } = response || {}
  let success = (responseData && (responseData.success ?? (responseData.status === 'success'))) || false
  let message = (responseData && responseData.message) || ''
  let contentData = responseData?.data ?? null

  // If inner layer also has a success flag, prefer that for analytics/overview endpoints
  if (contentData && typeof contentData === 'object' && typeof contentData.success === 'boolean') {
    success = contentData.success
    if (contentData.message) message = contentData.message
    // Unwrap one level if nested data exists
    if (contentData.data && typeof contentData.data === 'object') {
      contentData = contentData.data
    }
  }

  return {
    success,
    data: contentData || null,
    message,
    rawResponse: response
  }
}

/**
 * Xử lý response từ API có cấu trúc data.data (double nested)
 * @param {Object} response - Response từ API
 * @returns {Object} Object đã được chuẩn hóa
 */
export const extractDoubleNestedResponse = (response) => {
  const { data: responseData } = response || {}
  const { success, data: contentData, message } = responseData || {}
  
  return {
    success: success || false,
    data: contentData || null,
    message: message || '',
    rawResponse: response
  }
}

/**
 * Lấy danh sách items từ response có cấu trúc data.data.items
 * @param {Object} response - Response từ API
 * @returns {Object} Object chứa success, items, message
 */
export const extractItemsFromResponse = (response) => {
  const { success, data, message } = extractDoubleNestedResponse(response)
  const { items } = data || {}
  
  return {
    success,
    items: items || [],
    message,
    rawData: data
  }
}

/**
 * Xử lý pagination response
 * @param {Object} response - Response từ API
 * @returns {Object} Object chứa success, items, pagination, message
 */
export const extractPaginationResponse = (response) => {
  const { success, data, message } = extractDoubleNestedResponse(response)
  
  if (!data) {
    return {
      success,
      items: [],
      pagination: null,
      message
    }
  }
  
  const {
    data: items,
    current_page,
    last_page,
    per_page,
    total,
    from,
    to
  } = data
  
  return {
    success,
    items: items || [],
    pagination: {
      currentPage: current_page || 1,
      lastPage: last_page || 1,
      perPage: per_page || 10,
      total: total || 0,
      from: from || 0,
      to: to || 0
    },
    message
  }
}

/**
 * Xử lý error response
 * @param {Object} error - Error object từ catch
 * @returns {Object} Object chứa message, errors, status
 */
export const extractErrorResponse = (error) => {
  const { response } = error || {}
  const { data: errorData, status } = response || {}
  const { message, errors } = errorData || {}
  
  return {
    message: message || error?.message || 'Có lỗi xảy ra',
    errors: errors || null,
    status: status || null,
    rawError: error
  }
}

/**
 * Xử lý analytics/statistics response
 * @param {Object} response - Response từ API
 * @returns {Object} Object chứa success, stats, message
 */
export const extractAnalyticsResponse = (response) => {
  // Be flexible with various response shapes
  const { data: responseData } = response || {}
  let success = (responseData && (responseData.success ?? (responseData.status === 'success'))) || false
  let message = (responseData && responseData.message) || ''

  // Preferred shape: { success, data: { data: { ... } } }
  let stats = responseData?.data ?? null

  // If inner layer also has a success flag, prefer that for analytics endpoints
  if (stats && typeof stats === 'object' && typeof stats.success === 'boolean') {
    success = stats.success
    if (stats.message) message = stats.message
  }

  // If backend returns flat object (e.g., { success, data: { total: 10 } }),
  // normalize to always have stats.data
  if (stats && typeof stats === 'object' && !Array.isArray(stats)) {
    if (!('data' in stats)) {
      stats = { data: stats }
    }
  } else {
    stats = { data: {} }
  }

  return { success, stats, message }
}
