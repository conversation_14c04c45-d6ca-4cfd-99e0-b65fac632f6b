import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import galleriesApi from '@/utils/apis/galleries.api.js'

/**
 * Composable quản lý gallery và gallery items
 * Cung cấp các chức năng CRUD cho gallery groups và gallery items
 * @returns {Object} Các reactive state và methods để quản lý gallery
 */
export function useGalleries() {
  // ===== REACTIVE STATE =====
  /** @type {import('vue').Ref<Array>} Danh sách tất cả gallery groups */
  const galleries = ref([])

  /** @type {import('vue').Ref<Object|null>} Thông tin chi tiết một gallery group */
  const gallery = ref(null)

  /** @type {import('vue').Ref<boolean>} Trạng thái loading */
  const loading = ref(false)

  const loadingGetItemsGallery = ref(false)
  const galleryItems = ref([])
  const pagination = reactive({
    current_page: 1,
    per_page: 10,
    total: 0,
    last_page: 1,
    from: 1,
    to: 0,
    has_more_pages: false,
  })

  // ===== GALLERY GROUPS METHODS =====

  /**
   * Lấy danh sách tất cả gallery groups
   * @returns {Promise<void>}
   */
  const fetchGalleries = async () => {
    try {
      loading.value = true
      const response = await galleriesApi.getGalleries()
      galleries.value = response.data.data.data
      console.log(galleries.value)
    } catch (error) {
      console.error('Error fetching galleries:', error)
      ElMessage.error('Không thể tải danh sách gallery.')
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * Lấy thông tin chi tiết một gallery group theo ID
   * @param {string|number} id - ID của gallery group
   * @returns {Promise<void>}
   */
  const fetchGalleryById = async (id) => {
    try {
      const response = await galleriesApi.getGalleryById(id)
      gallery.value = response.data.data
    } catch (error) {
      console.error('Error fetching gallery by ID:', error)
      ElMessage.error('Không thể tải thông tin gallery.')
      throw error
    }
  }

  /**
   *
   */
  const fetchItemsByGalleryGroupId = async (id, params = {}) => {
    const { page, per_page } = params

    try {
      loadingGetItemsGallery.value = true

      // Truyền tham số mặc định
      const apiParams = {
        page: page ?? pagination.current_page,
        limit: per_page ?? pagination.per_page,
      }

      const response = await galleriesApi.getItemsByGalleryGroupId(id, apiParams)

      if (response.data.success) {
        galleryItems.value = response.data.data.data

        const paginationData = response.data.data.pagination
        Object.assign(pagination, paginationData)
      } else {
        throw new Error(response.data.message || 'Không thể lấy danh sách item trong bộ sưu tập')
      }
    } catch (error) {
      console.error('Error fetching items by gallary group:', error)
      ElMessage.error('Không thể tải thông tin Items')
    } finally {
      loadingGetItemsGallery.value = false
    }
  }

  /**
   * Tạo mới gallery group
   * @param {Object} galleryData - Dữ liệu gallery group mới
   * @returns {Promise<void>}
   */
  const createGallery = async (galleryData) => {
    try {
      await galleriesApi.createGallery(galleryData)
      ElMessage.success('Thêm gallery thành công!')
      await fetchGalleries() // Tải lại danh sách
    } catch (error) {
      console.error('Error creating gallery:', error)
      ElMessage.error('Thêm gallery thất bại.')
      throw error
    }
  }

  /**
   * Cập nhật gallery group
   * @param {string|number} id - ID của gallery group
   * @param {Object} galleryData - Dữ liệu cập nhật
   * @returns {Promise<void>}
   */
  const updateGallery = async (id, galleryData) => {
    try {
      await galleriesApi.updateGallery(id, galleryData)
      ElMessage.success('Cập nhật gallery thành công!')
      await fetchGalleries() // Tải lại danh sách
    } catch (error) {
      console.error('Error updating gallery:', error)
      ElMessage.error('Cập nhật gallery thất bại.')
      throw error
    }
  }

  /**
   * Xóa gallery group
   * @param {string|number} id - ID của gallery group
   * @returns {Promise<void>}
   */
  const deleteGallery = async (id) => {
    try {
      await galleriesApi.deleteGallery(id)
      ElMessage.success('Xóa gallery thành công!')
      await fetchGalleries() // Tải lại danh sách
    } catch (error) {
      console.error('Error deleting gallery:', error)
      ElMessage.error('Xóa gallery thất bại.')
      throw error
    }
  }

  // ===== GALLERY ITEMS METHODS =====

  /**
   * Xóa gallery item
   * @param {string|number} galleryGroupId - ID của gallery group
   * @param {string|number} itemId - ID của gallery item
   * @returns {Promise<void>}
   */
  const deleteGalleryItem = async (galleryGroupId, itemId) => {
    try {
      await galleriesApi.deleteGalleryItem(galleryGroupId, itemId)
      ElMessage.success('Xóa ảnh thành công!')
      await fetchGalleryById(galleryGroupId) // Tải lại thông tin nhóm ảnh
    } catch (error) {
      console.error('Error deleting gallery item:', error)
      ElMessage.error('Xóa ảnh thất bại.')
      throw error
    }
  }

  /**
   * Tạo mới gallery item
   * @param {string|number} galleryGroupId - ID của gallery group
   * @param {Object} galleryItemData - Dữ liệu gallery item mới
   * @returns {Promise<void>}
   */
  const createGalleryItem = async (galleryGroupId, galleryItemData) => {
    try {
      const response = await galleriesApi.createGalleryItem(galleryGroupId, galleryItemData)

      if (response.data.success) {
        ElMessage.success(response.data.message || 'Thêm hình ảnh thành công')
        return response.data.data
      } else {
        let errorMessage = 'Thêm ảnh thất bại'

        try {
          // Thử parse message nếu nó là JSON string
          const parsedMessage =
            typeof response.data.message === 'string' ? JSON.parse(response.data.message) : response.data.message

          if (typeof parsedMessage === 'object' && parsedMessage !== null) {
            if (Array.isArray(parsedMessage.errors)) {
              errorMessage = parsedMessage.errors.join(', ')
            } else if (parsedMessage.error) {
              errorMessage = parsedMessage.error
            }
          } else if (typeof parsedMessage === 'string') {
            errorMessage = parsedMessage
          }
        } catch (e) {
          // Nếu không parse được, giữ nguyên message gốc
          errorMessage = response.data.message || errorMessage
        }

        ElMessage.error(errorMessage)
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error creating gallery item:', error)

      ElMessage.error('Lỗi khi gửi yêu cầu: ' + error.message)
      throw error
    }
  }

  /**
   * Cập nhật gallery item
   * @param {string|number} galleryGroupId - ID của gallery group
   * @param {string|number} itemId - ID của gallery item
   * @param {Object} galleryItemData - Dữ liệu cập nhật
   * @returns {Promise<void>}
   */
  const updateGalleryItem = async (galleryGroupId, itemId, galleryItemData) => {
    try {
      await galleriesApi.updateGalleryItem(galleryGroupId, itemId, galleryItemData)
      ElMessage.success('Cập nhật ảnh thành công!')
    } catch (error) {
      console.error('Error updating gallery item:', error)
      ElMessage.error('Cập nhật ảnh thất bại.')
      throw error
    }
  }

  // ===== RETURN COMPOSABLE API =====
  return {
    // Reactive state
    galleries,
    gallery,
    loading,
    loadingGetItemsGallery,
    galleryItems,
    pagination,

    // Gallery groups methods
    fetchGalleries,
    fetchGalleryById,
    createGallery,
    updateGallery,
    deleteGallery,

    // Gallery items methods
    fetchItemsByGalleryGroupId,
    deleteGalleryItem,
    createGalleryItem,
    updateGalleryItem,
  }
}
