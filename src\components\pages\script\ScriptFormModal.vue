<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa Script' : 'Tạo Script Mới'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="scriptData" :rules="formRules" label-position="top" @submit.prevent="handleSubmit">
      <el-form-item label="Tên Script" prop="name">
        <el-input v-model="scriptData.name" placeholder="Ví dụ: Google Analytics" />
      </el-form-item>
      <el-form-item label="Key (Unique)" prop="key">
        <el-input v-model="scriptData.key" placeholder="Ví dụ: google_analytics" />
      </el-form-item>
      <el-form-item label="Mã Script" prop="code">
        <el-input v-model="scriptData.code" type="textarea" :rows="10" placeholder="Dán mã script của bạn vào đây..." />
      </el-form-item>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <el-form-item label="Vị trí chèn" prop="position">
          <el-select v-model="scriptData.position" class="w-full">
            <el-option label="Trong thẻ <head>" value="head" />
            <el-option label="Sau khi mở thẻ <body>" value="body" />
            <el-option label="Trước khi đóng thẻ </body>" value="footer" />
          </el-select>
        </el-form-item>
        <el-form-item label="Thứ tự ưu tiên" prop="sort_order">
          <el-input-number v-model="scriptData.sort_order" :min="0" class="w-full" />
        </el-form-item>
        <el-form-item label="Trạng thái" prop="status">
          <el-switch
            v-model="scriptData.status"
            active-text="Active"
            inactive-text="Inactive"
            active-value="active"
            inactive-value="inactive"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? 'Cập nhật' : 'Tạo mới' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useScriptStore } from '@/state'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  script: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Store
const { addScript, updateScript } = useScriptStore()

// Refs
const formRef = ref(null)
const loading = ref(false)

// Form data
const scriptData = reactive({
  id: null,
  name: '',
  key: '',
  code: '',
  position: 'body',
  sort_order: 10,
  status: 'active',
})

// Validation rules
const formRules = reactive({
  name: [{ required: true, message: 'Vui lòng nhập tên script', trigger: 'blur' }],
  key: [{ required: true, message: 'Vui lòng nhập key cho script', trigger: 'blur' }],
  code: [{ required: true, message: 'Vui lòng nhập mã script', trigger: 'blur' }],
})

// Computed properties
const isEdit = computed(() => !!props.script)
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// Methods
const resetForm = () => {
  Object.assign(scriptData, {
    id: null,
    name: '',
    key: '',
    code: '',
    position: 'body',
    sort_order: 10,
    status: 'active',
  })
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const populateForm = (script) => {
  if (script) {
    Object.assign(scriptData, script)
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      setTimeout(() => {
        try {
          if (isEdit.value) {
            updateScript(scriptData)
            ElMessage.success('Cập nhật script thành công!')
          } else {
            addScript(scriptData)
            ElMessage.success('Tạo script mới thành công!')
          }
          emit('success')
          handleClose()
        } catch (error) {
          ElMessage.error('Đã có lỗi xảy ra.')
          console.error(error)
        } finally {
          loading.value = false
        }
      }, 300)
    }
  })
}

const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

// Watchers
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      if (isEdit.value) {
        populateForm(props.script)
      } else {
        resetForm()
      }
    }
  },
)
</script>
